import { describe, it, expect, beforeEach } from 'bun:test';
import * as userService from './user.service';

const emailMock = {
  sendEmail: async () => true,
  createVerificationEmail: () => '<html></html>',
  createResetPasswordEmail: () => '<html></html>',
};

let userDb: any[] = [];
const UserMock = {
  findOne: async (query: any) => userDb.find(u => u.email === query.email) || null,
  create: async (data: any) => {
    userDb.push(data);
    return { ...data, save: async () => {}, resetPasswordToken: undefined };
  },
  findById: async (id: string) => userDb.find(u => u._id === id) || null,
};

const deps = {
  User: UserMock,
  ...emailMock,
};

describe('user.service', () => {
  beforeEach(() => {
    userDb = [{
      _id: 'id1',
      email: '<EMAIL>',
      isEmailVerified: true,
      comparePassword: async (pw: string) => pw === '123456',
      save: async () => {},
      resetPasswordToken: undefined,
    }];
  });

  it('signupUser: สมัครสำเร็จ', async () => {
    userDb = [];
    const user = await userService.signupUser({ email: '<EMAIL>', password: '123456' }, deps);
    expect(user.email).toBe('<EMAIL>');
  });

  it('signupUser: อีเมลซ้ำ', async () => {
    await expect(userService.signupUser({ email: '<EMAIL>', password: '123456' }, deps)).rejects.toThrow('อีเมลนี้ถูกใช้งานแล้ว');
  });

  it('signinUser: เข้าสู่ระบบเรียบร้อย', async () => {
    const { user, token } = await userService.signinUser({ email: '<EMAIL>', password: '123456' }, deps);
    expect(user.email).toBe('<EMAIL>');
    expect(token).toBeDefined();
  });

  it('signinUser: ไม่พบผู้ใช้', async () => {
    await expect(userService.signinUser({ email: '<EMAIL>', password: '123456' }, deps)).rejects.toThrow('ไม่พบผู้ใช้');
  });

  it('signinUser: รหัสผ่านผิด', async () => {
    userDb[0].comparePassword = async () => false;
    await expect(userService.signinUser({ email: '<EMAIL>', password: 'wrong' }, deps)).rejects.toThrow('รหัสผ่านไม่ถูกต้อง');
  });

  it('signinUser: ยังไม่ยืนยันอีเมล', async () => {
    userDb[0].isEmailVerified = false;
    userDb[0].comparePassword = async () => true;
    await expect(userService.signinUser({ email: '<EMAIL>', password: '123456' }, deps)).rejects.toThrow('กรุณายืนยันอีเมลก่อนเข้าสู่ระบบ');
  });

  it('signoutUser: always true', async () => {
    expect(await userService.signoutUser(undefined, deps)).toBe(true);
  });

  it('forgotPassword: ไม่พบผู้ใช้', async () => {
    await expect(userService.forgotPassword('<EMAIL>', deps)).rejects.toThrow('ไม่พบผู้ใช้');
  });

  it('forgotPassword: ส่งอีเมลสำเร็จ', async () => {
    // userDb[0] มี property save/resetPasswordToken อยู่แล้วจาก beforeEach
    await expect(userService.forgotPassword('<EMAIL>', deps)).resolves.toBe(true);
  });

  it('resetPassword: โทเค็นผิด', async () => {
    await expect(userService.resetPassword('badtoken', 'newpass', deps)).rejects.toThrow();
  });
}); 