import { t } from "elysia";

// Theme settings schema
const themeSettingsSchema = t.Object({
	primaryColor: t.Optional(t.String({ error: "สีหลักต้องเป็นข้อความ" })),
	secondaryColor: t.Optional(t.String({ error: "สีรองต้องเป็นข้อความ" })),
	backgroundColor: t.Optional(t.String({ error: "สีพื้นหลังต้องเป็นข้อความ" })),
	textColor: t.Optional(t.String({ error: "สีตัวอักษรต้องเป็นข้อความ" })),
	fontFamily: t.Optional(t.String({ error: "ฟอนต์ต้องเป็นข้อความ" })),
	borderRadius: t.Optional(t.String({ error: "ขอบมนต้องเป็นข้อความ" })),
	spacing: t.Optional(t.String({ error: "ระยะห่างต้องเป็นข้อความ" })),
	layout: t.Optional(
		t.Union([
			t.Literal("grid", { error: "ต้องเป็น grid เท่านั้น" }),
			t.Literal("list", { error: "ต้องเป็น list เท่านั้น" }),
			t.Literal("masonry", { error: "ต้องเป็น masonry เท่านั้น" }),
		], { error: "รูปแบบ layout ไม่ถูกต้อง" }),
	),
	headerStyle: t.Optional(
		t.Union([
			t.Literal("centered", { error: "ต้องเป็น centered เท่านั้น" }),
			t.Literal("left", { error: "ต้องเป็น left เท่านั้น" }),
			t.Literal("minimal", { error: "ต้องเป็น minimal เท่านั้น" }),
		], { error: "รูปแบบ header ไม่ถูกต้อง" }),
	),
	footerStyle: t.Optional(
		t.Union([
			t.Literal("simple", { error: "ต้องเป็น simple เท่านั้น" }),
			t.Literal("detailed", { error: "ต้องเป็น detailed เท่านั้น" }),
			t.Literal("minimal", { error: "ต้องเป็น minimal เท่านั้น" }),
		], { error: "รูปแบบ footer ไม่ถูกต้อง" }),
	),
	customCSS: t.Optional(t.String({ error: "CSS ต้องเป็นข้อความ" })),
});

// SEO settings schema
const seoSettingsSchema = t.Object({
	title: t.Optional(t.String({ error: "Title ต้องเป็นข้อความ" })),
	description: t.Optional(t.String({ error: "Description ต้องเป็นข้อความ" })),
	keywords: t.Optional(t.String({ error: "Keywords ต้องเป็นข้อความ" })),
	ogImage: t.Optional(t.String({ error: "OG Image ต้องเป็นข้อความ" })),
	ogTitle: t.Optional(t.String({ error: "OG Title ต้องเป็นข้อความ" })),
	ogDescription: t.Optional(t.String({ error: "OG Description ต้องเป็นข้อความ" })),
	twitterCard: t.Optional(t.String({ error: "Twitter Card ต้องเป็นข้อความ" })),
	canonicalUrl: t.Optional(t.String({ error: "Canonical URL ต้องเป็นข้อความ" })),
	robots: t.Optional(t.String({ error: "Robots ต้องเป็นข้อความ" })),
});

// Loading settings schema
const loadingSettingsSchema = t.Object({
	message: t.Optional(t.String({ error: "ข้อความ loading ต้องเป็นข้อความ" })),
	size: t.Optional(
		t.Union([
			t.Literal("sm", { error: "ต้องเป็น sm เท่านั้น" }),
			t.Literal("md", { error: "ต้องเป็น md เท่านั้น" }),
			t.Literal("lg", { error: "ต้องเป็น lg เท่านั้น" }),
		], { error: "ขนาด loading ไม่ถูกต้อง" }),
	),
	color: t.Optional(
		t.Union([
			t.Literal("blue", { error: "ต้องเป็น blue เท่านั้น" }),
			t.Literal("green", { error: "ต้องเป็น green เท่านั้น" }),
			t.Literal("red", { error: "ต้องเป็น red เท่านั้น" }),
			t.Literal("yellow", { error: "ต้องเป็น yellow เท่านั้น" }),
			t.Literal("purple", { error: "ต้องเป็น purple เท่านั้น" }),
		], { error: "สี loading ไม่ถูกต้อง" }),
	),
	showLogo: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	logoUrl: t.Optional(t.String({ error: "URL โลโก้ต้องเป็นข้อความ" })),
	logoSize: t.Optional(t.String({ error: "ขนาดโลโก้ต้องเป็นข้อความ" })),
	type: t.Optional(
		t.Union([
			t.Literal("spinner", { error: "ต้องเป็น spinner เท่านั้น" }),
			t.Literal("dots", { error: "ต้องเป็น dots เท่านั้น" }),
			t.Literal("bars", { error: "ต้องเป็น bars เท่านั้น" }),
			t.Literal("pulse", { error: "ต้องเป็น pulse เท่านั้น" }),
		], { error: "ประเภท loading ไม่ถูกต้อง" }),
	),
});

// Analytics settings schema
const analyticsSettingsSchema = t.Object({
	googleAnalyticsId: t.Optional(t.String({ error: "Google Analytics ID ต้องเป็นข้อความ" })),
	googleTagManagerId: t.Optional(t.String({ error: "Google Tag Manager ID ต้องเป็นข้อความ" })),
	facebookPixelId: t.Optional(t.String({ error: "Facebook Pixel ID ต้องเป็นข้อความ" })),
	hotjarId: t.Optional(t.String({ error: "Hotjar ID ต้องเป็นข้อความ" })),
	customTrackingCode: t.Optional(t.String({ error: "Custom Tracking Code ต้องเป็นข้อความ" })),
});

// Security settings schema
const securitySettingsSchema = t.Object({
	sslEnabled: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	cspEnabled: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	cspPolicy: t.Optional(t.String({ error: "CSP Policy ต้องเป็นข้อความ" })),
	hstsEnabled: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	xssProtection: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	contentTypeOptions: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	referrerPolicy: t.Optional(t.String({ error: "Referrer Policy ต้องเป็นข้อความ" })),
});

// Navigation settings schema
const navigationSettingsSchema = t.Object({
	showHeader: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	showFooter: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	showSearch: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	showLanguageSelector: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	showThemeToggle: t.Optional(t.Boolean({ error: "ต้องเป็น true หรือ false เท่านั้น" })),
	menuItems: t.Optional(
		t.Array(
			t.Object({
				id: t.String({ error: "ID เมนูต้องเป็นข้อความ" }),
				title: t.String({ error: "ชื่อเมนูต้องเป็นข้อความ" }),
				url: t.String({ error: "URL เมนูต้องเป็นข้อความ" }),
				type: t.Union([
					t.Literal("internal", { error: "ต้องเป็น internal เท่านั้น" }),
					t.Literal("external", { error: "ต้องเป็น external เท่านั้น" }),
					t.Literal("page", { error: "ต้องเป็น page เท่านั้น" }),
				], { error: "ประเภทเมนูไม่ถูกต้อง" }),
				pageId: t.Optional(t.String({ error: "Page ID ต้องเป็นข้อความ" })),
				order: t.Number({ error: "ลำดับเมนูต้องเป็นตัวเลข" }),
				isActive: t.Boolean({ error: "สถานะเมนูต้องเป็น true หรือ false เท่านั้น" }),
				parentId: t.Optional(t.String({ error: "Parent ID ต้องเป็นข้อความ" })),
				icon: t.Optional(t.String({ error: "Icon ต้องเป็นข้อความ" })),
			}),
		),
	),
	mobileMenuStyle: t.Optional(
		t.Union([
			t.Literal("slide", { error: "ต้องเป็น slide เท่านั้น" }),
			t.Literal("overlay", { error: "ต้องเป็น overlay เท่านั้น" }),
			t.Literal("dropdown", { error: "ต้องเป็น dropdown เท่านั้น" }),
		], { error: "รูปแบบเมนูมือถือไม่ถูกต้อง" }),
	),
	desktopMenuStyle: t.Optional(
		t.Union([
			t.Literal("horizontal", { error: "ต้องเป็น horizontal เท่านั้น" }),
			t.Literal("vertical", { error: "ต้องเป็น vertical เท่านั้น" }),
			t.Literal("mega", { error: "ต้องเป็น mega เท่านั้น" }),
		], { error: "รูปแบบเมนูเดสก์ท็อปไม่ถูกต้อง" }),
	),
});

// Page settings schema
const pageSettingsSchema = t.Object({
	pages: t.Optional(
		t.Array(
			t.Object({
				id: t.String({ error: "ID เพจต้องเป็นข้อความ" }),
				title: t.String({ error: "ชื่อเพจต้องเป็นข้อความ" }),
				slug: t.String({ error: "Slug ต้องเป็นข้อความ" }),
				content: t.String({ error: "เนื้อหาเพจต้องเป็นข้อความ" }),
				metaTitle: t.Optional(t.String({ error: "Meta Title ต้องเป็นข้อความ" })),
				metaDescription: t.Optional(t.String({ error: "Meta Description ต้องเป็นข้อความ" })),
				isActive: t.Boolean({ error: "สถานะเพจต้องเป็น true หรือ false เท่านั้น" }),
				isPublic: t.Boolean({ error: "สถานะสาธารณะต้องเป็น true หรือ false เท่านั้น" }),
				template: t.Optional(
					t.Union([
						t.Literal("default", { error: "ต้องเป็น default เท่านั้น" }),
						t.Literal("landing", { error: "ต้องเป็น landing เท่านั้น" }),
						t.Literal("about", { error: "ต้องเป็น about เท่านั้น" }),
						t.Literal("contact", { error: "ต้องเป็น contact เท่านั้น" }),
						t.Literal("custom", { error: "ต้องเป็น custom เท่านั้น" }),
					], { error: "รูปแบบ template ไม่ถูกต้อง" }),
				),
				customCSS: t.Optional(t.String({ error: "Custom CSS ต้องเป็นข้อความ" })),
				customJS: t.Optional(t.String({ error: "Custom JS ต้องเป็นข้อความ" })),
				seoSettings: t.Optional(seoSettingsSchema),
			}),
		),
	),
	defaultPage: t.Optional(t.String({ error: "Default Page ต้องเป็นข้อความ" })),
	errorPage: t.Optional(t.String({ error: "Error Page ต้องเป็นข้อความ" })),
	maintenancePage: t.Optional(t.String({ error: "Maintenance Page ต้องเป็นข้อความ" })),
});

// Site creation schema
export const createSiteSchema = t.Object({
	name: t.String({
		minLength: 3,
		maxLength: 100,
		description: "Site name",
		error: "ชื่อเว็บไซต์ต้องมีความยาว 3-100 ตัวอักษร"
	}),
	plan: t.Union(
		[
			t.Literal("daily", { error: "ต้องเป็น daily เท่านั้น" }),
			t.Literal("weekly", { error: "ต้องเป็น weekly เท่านั้น" }),
			t.Literal("monthly", { error: "ต้องเป็น monthly เท่านั้น" }),
			t.Literal("yearly", { error: "ต้องเป็น yearly เท่านั้น" }),
			t.Literal("permanent", { error: "ต้องเป็น permanent เท่านั้น" }),
		],
		{
			description: "Subscription plan",
			error: "รูปแบบแพ็กเกจไม่ถูกต้อง"
		},
	),
	typeDomain: t.Union([
		t.Literal("subdomain", { error: "ต้องเป็น subdomain เท่านั้น" }),
		t.Literal("custom", { error: "ต้องเป็น custom เท่านั้น" })
	], {
		description: "Domain type",
		error: "ประเภทโดเมนไม่ถูกต้อง ต้องเป็น subdomain หรือ custom"
	}),
	subDomain: t.Optional(t.String({
		minLength: 3,
		maxLength: 20,
		description: "Subdomain",
		error: "ซับโดเมนต้องมีความยาว 3-20 ตัวอักษร"
	})),
	mainDomain: t.Optional(t.String({
		minLength: 3,
		maxLength: 100,
		description: "Main domain",
		error: "โดเมนหลักต้องมีความยาว 3-100 ตัวอักษร"
	})),
	customDomain: t.Optional(t.String({
		minLength: 4,
		maxLength: 100,
		description: "Custom domain",
		error: "โดเมนส่วนตัวต้องมีความยาว 4-100 ตัวอักษร"
	})),
});

// Settings update schema (partial)
export const updateSiteSettingsSchema = t.Object({
	loadingSettings: t.Optional(loadingSettingsSchema),
	themeSettings: t.Optional(themeSettingsSchema),
	seoSettings: t.Optional(seoSettingsSchema),
	analyticsSettings: t.Optional(analyticsSettingsSchema),
	securitySettings: t.Optional(securitySettingsSchema),
	navigationSettings: t.Optional(navigationSettingsSchema),
	pageSettings: t.Optional(pageSettingsSchema),
});

// Domain check query schema
export const domainCheckQuerySchema = t.Object({
	domain: t.String({
		minLength: 1,
		description: "Domain to check",
		error: "กรุณากรอกโดเมนที่ต้องการตรวจสอบ"
	}),
});

// Site list query schema
export const siteListQuerySchema = t.Object({
	page: t.Optional(t.String({ error: "หน้าต้องเป็นตัวเลขหรือข้อความ" })),
	limit: t.Optional(t.String({ error: "จำกัดจำนวนต้องเป็นตัวเลขหรือข้อความ" })),
});

// Response schemas
export const siteResponseSchema = t.Object({
	success: t.Boolean({ error: "success ต้องเป็น true หรือ false เท่านั้น" }),
	message: t.Optional(t.String({ error: "ข้อความต้องเป็นข้อความ" })),
	data: t.Optional(t.Any()),
});

export const siteListResponseSchema = t.Object({
	success: t.Boolean({ error: "success ต้องเป็น true หรือ false เท่านั้น" }),
	data: t.Array(t.Any()),
	total: t.Number({ error: "total ต้องเป็นตัวเลข" }),
	page: t.Number({ error: "page ต้องเป็นตัวเลข" }),
	limit: t.Number({ error: "limit ต้องเป็นตัวเลข" }),
});

export const domainCheckResponseSchema = t.Object({
	success: t.Boolean({ error: "success ต้องเป็น true หรือ false เท่านั้น" }),
	available: t.Boolean({ error: "available ต้องเป็น true หรือ false เท่านั้น" }),
	domain: t.String({ error: "domain ต้องเป็นข้อความ" }),
	message: t.String({ error: "message ต้องเป็นข้อความ" }),
});
