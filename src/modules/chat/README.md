# 🚀 ระบบแชทสด (Real-time Chat System)

ระบบแชทแบบเรียลไทม์สำหรับการสื่อสารระหว่าง User กับ Customer และ Customer กับ Customer ในเว็บไซต์ โดยใช้ Elysia WebSocket

## ✨ คุณสมบัติหลัก

### 🔥 Real-time Features
- **แชทแบบเรียลไทม์** - ส่งและรับข้อความทันที
- **Typing Indicators** - แสดงสถานะกำลังพิมพ์
- **Online Status** - ตรวจสอบสถานะออนไลน์
- **Message Read Status** - ติดตามการอ่านข้อความ
- **Auto-reconnection** - เชื่อมต่อใหม่อัตโนมัติ

### 💬 Chat Types
- **User ↔ Customer** - แชทระหว่างผู้ดูแลระบบกับลูกค้า
- **Customer ↔ Customer** - แชทระหว่างลูกค้าด้วยกัน
- **Multi-site Support** - รองรับหลายเว็บไซต์

### 🛡️ Security & Authentication
- **JWT Authentication** - ระบบยืนยันตัวตนที่ปลอดภัย
- **Site-based Isolation** - แยกข้อมูลตามเว็บไซต์
- **Permission Control** - ควบคุมสิทธิ์การเข้าถึง

## 🏗️ สถาปัตยกรรม

```
src/modules/chat/
├── chat.model.ts          # MongoDB Models (ChatRoom, Message)
├── chat.service.ts        # Business Logic
├── websocket.service.ts   # WebSocket Connection Management
├── chat.routes.ts         # REST API + WebSocket Routes
├── index.ts              # Module Exports
└── README.md             # Documentation
```

## 📊 Database Schema

### ChatRoom Model
```typescript
{
  _id: string;
  siteId: string;
  roomType: 'user_customer' | 'customer_customer';
  participants: [{
    userId: string;
    userType: 'user' | 'customer';
    joinedAt: Date;
    isActive: boolean;
  }];
  lastMessage?: {
    message: string;
    senderId: string;
    senderType: 'user' | 'customer';
    sentAt: Date;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Message Model
```typescript
{
  _id: string;
  chatRoomId: string;
  senderId: string;
  senderType: 'user' | 'customer';
  message: string;
  messageType: 'text' | 'image' | 'file';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isRead: boolean;
  readBy: [{
    userId: string;
    userType: 'user' | 'customer';
    readAt: Date;
  }];
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## 🔌 API Endpoints

### REST API

#### Chat Rooms
```http
POST   /v1/chat/rooms                    # สร้างห้องแชทใหม่
GET    /v1/chat/rooms                    # ดึงรายการห้องแชท
GET    /v1/chat/rooms/:roomId/messages   # ดึงข้อความในห้อง
POST   /v1/chat/rooms/:roomId/read       # อ่านข้อความทั้งหมด
POST   /v1/chat/rooms/:roomId/leave      # ออกจากห้องแชท
```

#### Messages
```http
DELETE /v1/chat/messages/:messageId      # ลบข้อความ
```

#### Users
```http
GET    /v1/chat/search-users?q=term     # ค้นหาผู้ใช้
GET    /v1/chat/online-users            # ดูผู้ใช้ออนไลน์
```

### WebSocket API

#### Connection
```javascript
// เชื่อมต่อ WebSocket
const ws = new WebSocket('ws://localhost:5000/v1/chat/ws');

// ยืนยันตัวตน
ws.send(JSON.stringify({
  type: 'auth',
  data: { token: 'your-jwt-token' }
}));
```

#### Message Types

**เข้าร่วมห้องแชท**
```javascript
ws.send(JSON.stringify({
  type: 'join_room',
  data: { chatRoomId: 'room-id' }
}));
```

**ส่งข้อความ**
```javascript
ws.send(JSON.stringify({
  type: 'send_message',
  data: {
    message: 'Hello World!',
    messageType: 'text'
  }
}));
```

**แสดงสถานะพิมพ์**
```javascript
// เริ่มพิมพ์
ws.send(JSON.stringify({
  type: 'typing_start',
  data: {}
}));

// หยุดพิมพ์
ws.send(JSON.stringify({
  type: 'typing_stop',
  data: {}
}));
```

**อ่านข้อความ**
```javascript
ws.send(JSON.stringify({
  type: 'mark_read',
  data: { messageId: 'message-id' }
}));
```

## 🚀 การใช้งาน

### 1. การตั้งค่าเบื้องต้น

```typescript
// ใน src/index.ts
import { chatRoutes } from '@/modules/chat';

const app = new Elysia()
  .use(chatRoutes)
  // ... other routes
```

### 2. การเชื่อมต่อ WebSocket

```javascript
class ChatClient {
  constructor(token) {
    this.token = token;
    this.ws = null;
    this.connect();
  }

  connect() {
    this.ws = new WebSocket('ws://localhost:5000/v1/chat/ws');
    
    this.ws.onopen = () => {
      // ยืนยันตัวตน
      this.ws.send(JSON.stringify({
        type: 'auth',
        data: { token: this.token }
      }));
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };
  }

  handleMessage(message) {
    switch (message.type) {
      case 'new_message':
        this.displayMessage(message.data);
        break;
      case 'typing_start':
        this.showTypingIndicator(message.data);
        break;
      // ... handle other message types
    }
  }

  sendMessage(roomId, text) {
    // เข้าร่วมห้องก่อน
    this.ws.send(JSON.stringify({
      type: 'join_room',
      data: { chatRoomId: roomId }
    }));

    // ส่งข้อความ
    this.ws.send(JSON.stringify({
      type: 'send_message',
      data: { message: text }
    }));
  }
}
```

### 3. การสร้างห้องแชท

```javascript
async function createChatRoom(targetUserId, targetUserType) {
  const response = await fetch('/v1/chat/rooms', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      targetUserId,
      targetUserType,
      roomType: 'user_customer' // หรือ 'customer_customer'
    })
  });

  const data = await response.json();
  return data.data.chatRoom;
}
```

## 🎯 ตัวอย่างการใช้งาน

### Frontend Integration

```html
<!DOCTYPE html>
<html>
<head>
    <title>Chat Example</title>
</head>
<body>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="พิมพ์ข้อความ...">
    <button onclick="sendMessage()">ส่ง</button>

    <script>
        let ws;
        let currentRoom;

        // เชื่อมต่อ WebSocket
        function connectChat(token) {
            ws = new WebSocket('ws://localhost:5000/v1/chat/ws');
            
            ws.onopen = () => {
                ws.send(JSON.stringify({
                    type: 'auth',
                    data: { token }
                }));
            };

            ws.onmessage = (event) => {
                const message = JSON.parse(event.data);
                if (message.type === 'new_message') {
                    displayMessage(message.data);
                }
            };
        }

        // เข้าร่วมห้องแชท
        function joinRoom(roomId) {
            currentRoom = roomId;
            ws.send(JSON.stringify({
                type: 'join_room',
                data: { chatRoomId: roomId }
            }));
        }

        // ส่งข้อความ
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message && currentRoom) {
                ws.send(JSON.stringify({
                    type: 'send_message',
                    data: { message }
                }));
                input.value = '';
            }
        }

        // แสดงข้อความ
        function displayMessage(messageData) {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `
                <strong>${messageData.senderType}:</strong> 
                ${messageData.message}
                <small>(${new Date(messageData.createdAt).toLocaleTimeString()})</small>
            `;
            messagesDiv.appendChild(messageElement);
        }
    </script>
</body>
</html>
```

## 🔧 การกำหนดค่า

### Environment Variables
```env
JWT_SECRET=your-jwt-secret
REFRESH_TOKEN_SECRET=your-refresh-token-secret
MONGODB_URI=mongodb://localhost:27017/your-database
```

### WebSocket Configuration
```typescript
// ใน chat.routes.ts
.ws('/ws', {
  // กำหนดค่า WebSocket
  idleTimeout: 120, // 2 minutes
  maxPayloadLength: 16 * 1024, // 16KB
  compression: true
})
```

## 📱 Mobile Support

ระบบรองรับการใช้งานบนมือถือผ่าน:
- **Responsive Design** - ปรับขนาดหน้าจออัตโนมัติ
- **Touch Events** - รองรับการสัมผัส
- **Mobile WebSocket** - เชื่อมต่อเสถียรบนมือถือ

## 🛠️ การพัฒนาและทดสอบ

### การรัน Development Server
```bash
bun run dev
```

### การทดสอบ WebSocket
```bash
# ใช้ wscat สำหรับทดสอบ
npm install -g wscat
wscat -c ws://localhost:5000/v1/chat/ws
```

### การทดสอบ API
```bash
# ทดสอบสร้างห้องแชท
curl -X POST http://localhost:5000/v1/chat/rooms \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"targetUserId":"user123","targetUserType":"customer","roomType":"user_customer"}'
```

## 🚨 การจัดการข้อผิดพลาด

### WebSocket Error Handling
```javascript
ws.onerror = (error) => {
  console.error('WebSocket error:', error);
  // Implement reconnection logic
  setTimeout(() => {
    connectChat(token);
  }, 5000);
};

ws.onclose = (event) => {
  console.log('WebSocket closed:', event.code, event.reason);
  // Auto-reconnect if not intentional
  if (event.code !== 1000) {
    setTimeout(() => {
      connectChat(token);
    }, 3000);
  }
};
```

### API Error Handling
```javascript
async function apiCall(url, options) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'API Error');
    }
    
    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
}
```

## 📈 Performance Tips

### 1. Connection Management
- ใช้ Connection Pooling
- ทำ Cleanup connections ที่ไม่ใช้งาน
- จำกัดจำนวน connections ต่อ user

### 2. Message Optimization
- ใช้ Pagination สำหรับ message history
- Compress ข้อความขนาดใหญ่
- Cache ข้อความที่ใช้บ่อย

### 3. Database Optimization
- สร้าง Index ที่เหมาะสม
- ใช้ MongoDB Aggregation
- ทำ Database cleanup เป็นระยะ

## 🔒 Security Considerations

### 1. Authentication
- ตรวจสอบ JWT token ทุกครั้ง
- ใช้ HTTPS/WSS ใน production
- จำกัด token lifetime

### 2. Authorization
- ตรวจสอบสิทธิ์เข้าถึงห้องแชท
- แยกข้อมูลตาม siteId
- ป้องกัน message injection

### 3. Rate Limiting
- จำกัดจำนวนข้อความต่อนาที
- ป้องกัน spam
- จำกัด connection ต่อ IP

## 🎨 UI/UX Best Practices

### 1. Real-time Feedback
- แสดง typing indicators
- แสดงสถานะการส่งข้อความ
- แสดงสถานะออนไลน์

### 2. Message Display
- จัดกลุ่มข้อความตามเวลา
- แสดงเวลาที่ส่ง
- แยกข้อความของตัวเองและคนอื่น

### 3. Mobile Experience
- ใช้ responsive design
- ปรับขนาด input สำหรับมือถือ
- รองรับ touch gestures

## 📚 Additional Resources

- [Elysia WebSocket Documentation](https://elysiajs.com/patterns/websocket.html)
- [MongoDB Best Practices](https://docs.mongodb.com/manual/administration/production-notes/)
- [JWT Security Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details