import mongoose, { Schema, type Document } from "mongoose";
import { generateFileId } from "@/core/utils/idGenerator";

export type CustomerSegment = 'new' | 'returning' | 'loyal' | 'vip' | 'inactive';
export type CustomerBehavior = 'high_value' | 'frequent_buyer' | 'seasonal' | 'one_time' | 'browsing';

export interface ICustomerMetrics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  activeCustomers: number;
  inactiveCustomers: number;
  averageOrderValue: number;
  customerLifetimeValue: number;
  retentionRate: number;
  churnRate: number;
}

export interface ICustomerSegment {
  segment: CustomerSegment;
  count: number;
  percentage: number;
  averageOrderValue: number;
  totalRevenue: number;
  averageLifetimeValue: number;
}

export interface ICustomerBehavior {
  behavior: CustomerBehavior;
  count: number;
  percentage: number;
  averageOrderValue: number;
  frequency: number;
}

export interface ICustomerAnalytics extends Document {
  _id: string;
  siteId: string;
  
  // Period info
  startDate: Date;
  endDate: Date;
  
  // Customer metrics
  metrics: ICustomerMetrics;
  
  // Segments
  segments: ICustomerSegment[];
  
  // Behaviors
  behaviors: ICustomerBehavior[];
  
  // Top customers
  topCustomers: Array<{
    customerId: string;
    customerName: string;
    email: string;
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate: Date;
    firstOrderDate: Date;
    customerLifetimeValue: number;
    segment: CustomerSegment;
    behavior: CustomerBehavior;
  }>;
  
  // Customer acquisition
  acquisitionBySource: Array<{
    source: string;
    count: number;
    percentage: number;
  }>;
  
  // Customer retention
  retentionByMonth: Array<{
    month: string;
    retainedCustomers: number;
    newCustomers: number;
    churnedCustomers: number;
    retentionRate: number;
  }>;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const customerMetricsSchema = new Schema({
  totalCustomers: { type: Number, default: 0 },
  newCustomers: { type: Number, default: 0 },
  returningCustomers: { type: Number, default: 0 },
  activeCustomers: { type: Number, default: 0 },
  inactiveCustomers: { type: Number, default: 0 },
  averageOrderValue: { type: Number, default: 0 },
  customerLifetimeValue: { type: Number, default: 0 },
  retentionRate: { type: Number, default: 0 },
  churnRate: { type: Number, default: 0 }
}, { _id: false });

const customerSegmentSchema = new Schema({
  segment: {
    type: String,
    enum: ['new', 'returning', 'loyal', 'vip', 'inactive'],
    required: true
  },
  count: { type: Number, default: 0 },
  percentage: { type: Number, default: 0 },
  averageOrderValue: { type: Number, default: 0 },
  totalRevenue: { type: Number, default: 0 },
  averageLifetimeValue: { type: Number, default: 0 }
}, { _id: false });

const customerBehaviorSchema = new Schema({
  behavior: {
    type: String,
    enum: ['high_value', 'frequent_buyer', 'seasonal', 'one_time', 'browsing'],
    required: true
  },
  count: { type: Number, default: 0 },
  percentage: { type: Number, default: 0 },
  averageOrderValue: { type: Number, default: 0 },
  frequency: { type: Number, default: 0 }
}, { _id: false });

const customerAnalyticsSchema = new Schema<ICustomerAnalytics>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    
    // Period info
    startDate: { type: Date, required: true, index: true },
    endDate: { type: Date, required: true, index: true },
    
    // Customer metrics
    metrics: { type: customerMetricsSchema, required: true },
    
    // Segments
    segments: [customerSegmentSchema],
    
    // Behaviors
    behaviors: [customerBehaviorSchema],
    
    // Top customers
    topCustomers: [{
      customerId: { type: String, required: true },
      customerName: { type: String, required: true },
      email: { type: String, required: true },
      totalOrders: { type: Number, default: 0 },
      totalSpent: { type: Number, default: 0 },
      averageOrderValue: { type: Number, default: 0 },
      lastOrderDate: { type: Date },
      firstOrderDate: { type: Date },
      customerLifetimeValue: { type: Number, default: 0 },
      segment: {
        type: String,
        enum: ['new', 'returning', 'loyal', 'vip', 'inactive'],
        required: true
      },
      behavior: {
        type: String,
        enum: ['high_value', 'frequent_buyer', 'seasonal', 'one_time', 'browsing'],
        required: true
      }
    }],
    
    // Customer acquisition
    acquisitionBySource: [{
      source: { type: String, required: true },
      count: { type: Number, default: 0 },
      percentage: { type: Number, default: 0 }
    }],
    
    // Customer retention
    retentionByMonth: [{
      month: { type: String, required: true },
      retainedCustomers: { type: Number, default: 0 },
      newCustomers: { type: Number, default: 0 },
      churnedCustomers: { type: Number, default: 0 },
      retentionRate: { type: Number, default: 0 }
    }]
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
customerAnalyticsSchema.index({ siteId: 1, startDate: 1, endDate: 1 });
customerAnalyticsSchema.index({ siteId: 1, createdAt: -1 });

// Static methods
customerAnalyticsSchema.statics.getAnalyticsByPeriod = function(siteId: string, startDate: Date, endDate: Date) {
  return this.findOne({ siteId, startDate, endDate });
};

customerAnalyticsSchema.statics.getLatestAnalytics = function(siteId: string) {
  return this.findOne({ siteId }).sort({ createdAt: -1 });
};

export const CustomerAnalytics = mongoose.model<ICustomerAnalytics>("CustomerAnalytics", customerAnalyticsSchema); 