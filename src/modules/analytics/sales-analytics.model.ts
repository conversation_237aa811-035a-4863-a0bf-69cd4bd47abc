import mongoose, { Schema, type Document } from "mongoose";
import { generateFileId } from "@/core/utils/idGenerator";

export type AnalyticsPeriod = 'daily' | 'weekly' | 'monthly' | 'yearly';
export type SalesMetric = 'revenue' | 'orders' | 'customers' | 'average_order_value' | 'conversion_rate';

export interface ISalesData {
  date: Date;
  revenue: number;
  orders: number;
  customers: number;
  averageOrderValue: number;
  conversionRate: number;
  refunds: number;
  netRevenue: number;
}

export interface IProductSalesData {
  productId: string;
  productName: string;
  categoryId: string;
  categoryName: string;
  brandId?: string;
  brandName?: string;
  quantitySold: number;
  revenue: number;
  averageRating: number;
  reviewCount: number;
}

export interface ICustomerSalesData {
  customerId: string;
  customerName: string;
  email: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate: Date;
  firstOrderDate: Date;
  customerLifetimeValue: number;
}

export interface ISalesAnalytics extends Document {
  _id: string;
  siteId: string;
  
  // Period info
  period: AnalyticsPeriod;
  startDate: Date;
  endDate: Date;
  
  // Sales metrics
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  averageOrderValue: number;
  conversionRate: number;
  totalRefunds: number;
  netRevenue: number;
  
  // Growth metrics
  revenueGrowth: number;
  orderGrowth: number;
  customerGrowth: number;
  
  // Top performers
  topProducts: IProductSalesData[];
  topCategories: Array<{
    categoryId: string;
    categoryName: string;
    revenue: number;
    orders: number;
  }>;
  topCustomers: ICustomerSalesData[];
  
  // Sales data by period
  dailySales: ISalesData[];
  weeklySales: ISalesData[];
  monthlySales: ISalesData[];
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const salesDataSchema = new Schema({
  date: { type: Date, required: true },
  revenue: { type: Number, default: 0 },
  orders: { type: Number, default: 0 },
  customers: { type: Number, default: 0 },
  averageOrderValue: { type: Number, default: 0 },
  conversionRate: { type: Number, default: 0 },
  refunds: { type: Number, default: 0 },
  netRevenue: { type: Number, default: 0 }
}, { _id: false });

const productSalesDataSchema = new Schema({
  productId: { type: String, required: true },
  productName: { type: String, required: true },
  categoryId: { type: String, required: true },
  categoryName: { type: String, required: true },
  brandId: { type: String },
  brandName: { type: String },
  quantitySold: { type: Number, default: 0 },
  revenue: { type: Number, default: 0 },
  averageRating: { type: Number, default: 0 },
  reviewCount: { type: Number, default: 0 }
}, { _id: false });

const customerSalesDataSchema = new Schema({
  customerId: { type: String, required: true },
  customerName: { type: String, required: true },
  email: { type: String, required: true },
  totalOrders: { type: Number, default: 0 },
  totalSpent: { type: Number, default: 0 },
  averageOrderValue: { type: Number, default: 0 },
  lastOrderDate: { type: Date },
  firstOrderDate: { type: Date },
  customerLifetimeValue: { type: Number, default: 0 }
}, { _id: false });

const salesAnalyticsSchema = new Schema<ISalesAnalytics>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    
    // Period info
    period: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly'],
      required: true,
      index: true
    },
    startDate: { type: Date, required: true, index: true },
    endDate: { type: Date, required: true, index: true },
    
    // Sales metrics
    totalRevenue: { type: Number, default: 0 },
    totalOrders: { type: Number, default: 0 },
    totalCustomers: { type: Number, default: 0 },
    averageOrderValue: { type: Number, default: 0 },
    conversionRate: { type: Number, default: 0 },
    totalRefunds: { type: Number, default: 0 },
    netRevenue: { type: Number, default: 0 },
    
    // Growth metrics
    revenueGrowth: { type: Number, default: 0 },
    orderGrowth: { type: Number, default: 0 },
    customerGrowth: { type: Number, default: 0 },
    
    // Top performers
    topProducts: [productSalesDataSchema],
    topCategories: [{
      categoryId: { type: String, required: true },
      categoryName: { type: String, required: true },
      revenue: { type: Number, default: 0 },
      orders: { type: Number, default: 0 }
    }],
    topCustomers: [customerSalesDataSchema],
    
    // Sales data by period
    dailySales: [salesDataSchema],
    weeklySales: [salesDataSchema],
    monthlySales: [salesDataSchema]
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
salesAnalyticsSchema.index({ siteId: 1, period: 1, startDate: 1, endDate: 1 });
salesAnalyticsSchema.index({ siteId: 1, createdAt: -1 });

// Static methods
salesAnalyticsSchema.statics.getAnalyticsByPeriod = function(siteId: string, period: AnalyticsPeriod, startDate: Date, endDate: Date) {
  return this.findOne({ siteId, period, startDate, endDate });
};

salesAnalyticsSchema.statics.getLatestAnalytics = function(siteId: string, period: AnalyticsPeriod) {
  return this.findOne({ siteId, period }).sort({ createdAt: -1 });
};

export const SalesAnalytics = mongoose.model<ISalesAnalytics>("SalesAnalytics", salesAnalyticsSchema); 