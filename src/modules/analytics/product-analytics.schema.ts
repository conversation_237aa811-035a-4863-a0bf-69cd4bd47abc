import { t } from 'elysia';

// Product Analytics Schemas
export const ProductAnalyticsQuerySchema = t.Object({
  period: t.Optional(t.Union([
    t.Literal('day', { error: 'ต้องเป็น day เท่านั้น' }),
    t.Literal('week', { error: 'ต้องเป็น week เท่านั้น' }),
    t.Literal('month', { error: 'ต้องเป็น month เท่านั้น' }),
    t.Literal('year', { error: 'ต้องเป็น year เท่านั้น' })
  ], { error: 'period ไม่ถูกต้อง' })),
  startDate: t.Optional(t.String({ error: 'startDate ต้องเป็นข้อความ' })),
  endDate: t.Optional(t.String({ error: 'endDate ต้องเป็นข้อความ' })),
  limit: t.Optional(t.String({ error: 'limit ต้องเป็นข้อความ' }))
});

export const ProductAnalyticsGenerateSchema = t.Object({
  period: t.Union([
    t.Literal('day', { error: 'ต้องเป็น day เท่านั้น' }),
    t.Literal('week', { error: 'ต้องเป็น week เท่านั้น' }),
    t.Literal('month', { error: 'ต้องเป็น month เท่านั้น' }),
    t.Literal('year', { error: 'ต้องเป็น year เท่านั้น' })
  ], { error: 'period ไม่ถูกต้อง' }),
  startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
  endDate: t.String({ error: 'endDate ต้องเป็นข้อความ' })
});

export const ProductAnalyticsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
    startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
    endDate: t.String({ error: 'endDate ต้องเป็นข้อความ' }),
    period: t.String({ error: 'period ต้องเป็นข้อความ' }),
    metrics: t.Object({
      totalProducts: t.Number({ error: 'totalProducts ต้องเป็นตัวเลข' }),
      activeProducts: t.Number({ error: 'activeProducts ต้องเป็นตัวเลข' }),
      inactiveProducts: t.Number({ error: 'inactiveProducts ต้องเป็นตัวเลข' }),
      totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
      averagePrice: t.Number({ error: 'averagePrice ต้องเป็นตัวเลข' }),
      totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
      averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' }),
      totalReviews: t.Number({ error: 'totalReviews ต้องเป็นตัวเลข' })
    }),
    topProducts: t.Array(t.Object({
      productId: t.String({ error: 'productId ต้องเป็นข้อความ' }),
      productName: t.String({ error: 'productName ต้องเป็นข้อความ' }),
      category: t.String({ error: 'category ต้องเป็นข้อความ' }),
      totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
      totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
      totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
      averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' }),
      totalReviews: t.Number({ error: 'totalReviews ต้องเป็นตัวเลข' }),
      conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' })
    }), { error: 'topProducts ต้องเป็น array ของ object' }),
    categoryPerformance: t.Array(t.Object({
      category: t.String({ error: 'category ต้องเป็นข้อความ' }),
      totalProducts: t.Number({ error: 'totalProducts ต้องเป็นตัวเลข' }),
      totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
      totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
      averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' }),
      conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' })
    }), { error: 'categoryPerformance ต้องเป็น array ของ object' }),
    priceAnalysis: t.Object({
      priceRanges: t.Array(t.Object({
        range: t.String({ error: 'range ต้องเป็นข้อความ' }),
        count: t.Number({ error: 'count ต้องเป็นตัวเลข' }),
        totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
        averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' })
      }), { error: 'priceRanges ต้องเป็น array ของ object' }),
      averagePriceByCategory: t.Array(t.Object({
        category: t.String({ error: 'category ต้องเป็นข้อความ' }),
        averagePrice: t.Number({ error: 'averagePrice ต้องเป็นตัวเลข' }),
        minPrice: t.Number({ error: 'minPrice ต้องเป็นตัวเลข' }),
        maxPrice: t.Number({ error: 'maxPrice ต้องเป็นตัวเลข' })
      }), { error: 'averagePriceByCategory ต้องเป็น array ของ object' })
    }),
    inventoryAnalysis: t.Object({
      lowStockProducts: t.Number({ error: 'lowStockProducts ต้องเป็นตัวเลข' }),
      outOfStockProducts: t.Number({ error: 'outOfStockProducts ต้องเป็นตัวเลข' }),
      overstockedProducts: t.Number({ error: 'overstockedProducts ต้องเป็นตัวเลข' }),
      inventoryValue: t.Number({ error: 'inventoryValue ต้องเป็นตัวเลข' }),
      turnoverRate: t.Number({ error: 'turnoverRate ต้องเป็นตัวเลข' })
    }),
    performanceMetrics: t.Object({
      pageViews: t.Number({ error: 'pageViews ต้องเป็นตัวเลข' }),
      uniqueVisitors: t.Number({ error: 'uniqueVisitors ต้องเป็นตัวเลข' }),
      addToCartRate: t.Number({ error: 'addToCartRate ต้องเป็นตัวเลข' }),
      purchaseRate: t.Number({ error: 'purchaseRate ต้องเป็นตัวเลข' }),
      bounceRate: t.Number({ error: 'bounceRate ต้องเป็นตัวเลข' }),
      averageSessionDuration: t.Number({ error: 'averageSessionDuration ต้องเป็นตัวเลข' })
    }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
  })
});

export const TopProductsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Array(t.Object({
    productId: t.String({ error: 'productId ต้องเป็นข้อความ' }),
    productName: t.String({ error: 'productName ต้องเป็นข้อความ' }),
    category: t.String({ error: 'category ต้องเป็นข้อความ' }),
    totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
    totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
    totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
    averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' }),
    totalReviews: t.Number({ error: 'totalReviews ต้องเป็นตัวเลข' }),
    conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' })
  }), { error: 'data ต้องเป็น array ของ object' })
});

export const CategoryPerformanceResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Array(t.Object({
    category: t.String({ error: 'category ต้องเป็นข้อความ' }),
    totalProducts: t.Number({ error: 'totalProducts ต้องเป็นตัวเลข' }),
    totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
    totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
    averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' }),
    conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' })
  }), { error: 'data ต้องเป็น array ของ object' })
});

export const PriceAnalysisResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    priceRanges: t.Array(t.Object({
      range: t.String({ error: 'range ต้องเป็นข้อความ' }),
      count: t.Number({ error: 'count ต้องเป็นตัวเลข' }),
      totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
      averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' })
    }), { error: 'priceRanges ต้องเป็น array ของ object' }),
    averagePriceByCategory: t.Array(t.Object({
      category: t.String({ error: 'category ต้องเป็นข้อความ' }),
      averagePrice: t.Number({ error: 'averagePrice ต้องเป็นตัวเลข' }),
      minPrice: t.Number({ error: 'minPrice ต้องเป็นตัวเลข' }),
      maxPrice: t.Number({ error: 'maxPrice ต้องเป็นตัวเลข' })
    }), { error: 'averagePriceByCategory ต้องเป็น array ของ object' })
  })
});

export const InventoryAnalysisResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    lowStockProducts: t.Number({ error: 'lowStockProducts ต้องเป็นตัวเลข' }),
    outOfStockProducts: t.Number({ error: 'outOfStockProducts ต้องเป็นตัวเลข' }),
    overstockedProducts: t.Number({ error: 'overstockedProducts ต้องเป็นตัวเลข' }),
    inventoryValue: t.Number({ error: 'inventoryValue ต้องเป็นตัวเลข' }),
    turnoverRate: t.Number({ error: 'turnoverRate ต้องเป็นตัวเลข' })
  })
});

export const PerformanceMetricsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    pageViews: t.Number({ error: 'pageViews ต้องเป็นตัวเลข' }),
    uniqueVisitors: t.Number({ error: 'uniqueVisitors ต้องเป็นตัวเลข' }),
    addToCartRate: t.Number({ error: 'addToCartRate ต้องเป็นตัวเลข' }),
    purchaseRate: t.Number({ error: 'purchaseRate ต้องเป็นตัวเลข' }),
    bounceRate: t.Number({ error: 'bounceRate ต้องเป็นตัวเลข' }),
    averageSessionDuration: t.Number({ error: 'averageSessionDuration ต้องเป็นตัวเลข' })
  })
}); 