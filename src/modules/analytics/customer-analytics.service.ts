import { CustomerAnalytics, ICustomerAnalytics, type CustomerSegment, type CustomerBehavior } from './customer-analytics.model';
import { Order } from './order.model';
import { User } from '@/modules/user/user.model';
import { HttpError } from '@/core/utils/error';

// Customer Analytics Service
export async function generateCustomerAnalytics(siteId: string, startDate: Date, endDate: Date) {
  try {
    // ตรวจสอบว่ามี analytics นี้แล้วหรือไม่
    const existingAnalytics = await (CustomerAnalytics as any).getAnalyticsByPeriod(siteId, startDate, endDate);
    if (existingAnalytics) {
      return existingAnalytics;
    }

    // ดึงข้อมูลลูกค้าทั้งหมด
    const allCustomers = await User.find({ siteId });
    const totalCustomers = allCustomers.length;

    // ดึงข้อมูลออเดอร์ในช่วงเวลาที่กำหนด
    const orders = await Order.find({
      siteId,
      createdAt: { $gte: startDate, $lte: endDate },
      status: { $in: ['completed', 'delivered', 'shipped'] }
    }).populate('userId', 'name email createdAt');

    // คำนวณ metrics พื้นฐาน
    const uniqueCustomers = new Set(orders.map(order => order.userId.toString()));
    const activeCustomers = uniqueCustomers.size;
    const inactiveCustomers = totalCustomers - activeCustomers;

    // คำนวณ new customers (ลูกค้าที่สมัครในช่วงเวลาที่กำหนด)
    const newCustomers = allCustomers.filter(customer => 
      customer.createdAt >= startDate && customer.createdAt <= endDate
    ).length;

    // คำนวณ returning customers
    const returningCustomers = activeCustomers - newCustomers;

    // คำนวณ average order value
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

    // คำนวณ customer lifetime value
    const customerLifetimeValue = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;

    // คำนวณ retention rate
    const retentionRate = totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0;

    // คำนวณ churn rate
    const churnRate = totalCustomers > 0 ? (inactiveCustomers / totalCustomers) * 100 : 0;

    // สร้าง customer segments
    const segments = await calculateCustomerSegments(siteId, startDate, endDate);

    // สร้าง customer behaviors
    const behaviors = await calculateCustomerBehaviors(siteId, startDate, endDate);

    // สร้าง top customers
    const topCustomers = await calculateTopCustomers(siteId, startDate, endDate);

    // สร้าง customer acquisition data
    const acquisitionBySource = await calculateCustomerAcquisition(siteId, startDate, endDate);

    // สร้าง customer retention data
    const retentionByMonth = await calculateCustomerRetention(siteId, startDate, endDate);

    // สร้าง analytics record
    const analytics = await CustomerAnalytics.create({
      siteId,
      startDate,
      endDate,
      metrics: {
        totalCustomers,
        newCustomers,
        returningCustomers,
        activeCustomers,
        inactiveCustomers,
        averageOrderValue,
        customerLifetimeValue,
        retentionRate,
        churnRate
      },
      segments,
      behaviors,
      topCustomers,
      acquisitionBySource,
      retentionByMonth
    });

    return analytics;
  } catch (err: any) {
    console.error('Error in generateCustomerAnalytics:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง customer analytics');
  }
}

async function calculateCustomerSegments(siteId: string, startDate: Date, endDate: Date) {
  const orders = await Order.find({
    siteId,
    createdAt: { $gte: startDate, $lte: endDate },
    status: { $in: ['completed', 'delivered', 'shipped'] }
  }).populate('userId', 'name email createdAt');

  const customerData = new Map();
  
  // จัดกลุ่มข้อมูลลูกค้า
  for (const order of orders) {
    const customerId = order.userId.toString();
    if (!customerData.has(customerId)) {
      customerData.set(customerId, {
        customerId,
        totalOrders: 0,
        totalSpent: 0,
        orders: []
      });
    }
    const data = customerData.get(customerId);
    data.totalOrders += 1;
    data.totalSpent += order.totalAmount;
    data.orders.push(order);
  }

  // จัดหมวดหมู่ลูกค้า
  const segments = {
    new: { count: 0, totalRevenue: 0, totalOrders: 0 },
    returning: { count: 0, totalRevenue: 0, totalOrders: 0 },
    loyal: { count: 0, totalRevenue: 0, totalOrders: 0 },
    vip: { count: 0, totalRevenue: 0, totalOrders: 0 },
    inactive: { count: 0, totalRevenue: 0, totalOrders: 0 }
  };

  for (const [customerId, data] of customerData) {
    const averageOrderValue = data.totalOrders > 0 ? data.totalSpent / data.totalOrders : 0;
    
    if (data.totalOrders === 1) {
      segments.new.count++;
      segments.new.totalRevenue += data.totalSpent;
      segments.new.totalOrders += data.totalOrders;
    } else if (data.totalOrders <= 3) {
      segments.returning.count++;
      segments.returning.totalRevenue += data.totalSpent;
      segments.returning.totalOrders += data.totalOrders;
    } else if (data.totalOrders <= 10) {
      segments.loyal.count++;
      segments.loyal.totalRevenue += data.totalSpent;
      segments.loyal.totalOrders += data.totalOrders;
    } else {
      segments.vip.count++;
      segments.vip.totalRevenue += data.totalSpent;
      segments.vip.totalOrders += data.totalOrders;
    }
  }

  const totalCustomers = customerData.size;
  const result = [];

  for (const [segment, data] of Object.entries(segments)) {
    if (data.count > 0) {
      result.push({
        segment: segment as CustomerSegment,
        count: data.count,
        percentage: (data.count / totalCustomers) * 100,
        averageOrderValue: data.totalOrders > 0 ? data.totalRevenue / data.totalOrders : 0,
        totalRevenue: data.totalRevenue,
        averageLifetimeValue: data.totalRevenue / data.count
      });
    }
  }

  return result;
}

async function calculateCustomerBehaviors(siteId: string, startDate: Date, endDate: Date) {
  const orders = await Order.find({
    siteId,
    createdAt: { $gte: startDate, $lte: endDate },
    status: { $in: ['completed', 'delivered', 'shipped'] }
  }).populate('userId', 'name email');

  const customerData = new Map();
  
  // จัดกลุ่มข้อมูลลูกค้า
  for (const order of orders) {
    const customerId = order.userId.toString();
    if (!customerData.has(customerId)) {
      customerData.set(customerId, {
        customerId,
        totalOrders: 0,
        totalSpent: 0,
        orders: []
      });
    }
    const data = customerData.get(customerId);
    data.totalOrders += 1;
    data.totalSpent += order.totalAmount;
    data.orders.push(order);
  }

  // จัดหมวดหมู่พฤติกรรม
  const behaviors = {
    high_value: { count: 0, totalRevenue: 0, totalOrders: 0 },
    frequent_buyer: { count: 0, totalRevenue: 0, totalOrders: 0 },
    seasonal: { count: 0, totalRevenue: 0, totalOrders: 0 },
    one_time: { count: 0, totalRevenue: 0, totalOrders: 0 },
    browsing: { count: 0, totalRevenue: 0, totalOrders: 0 }
  };

  for (const [customerId, data] of customerData) {
    const averageOrderValue = data.totalOrders > 0 ? data.totalSpent / data.totalOrders : 0;
    
    if (averageOrderValue > 1000) {
      behaviors.high_value.count++;
      behaviors.high_value.totalRevenue += data.totalSpent;
      behaviors.high_value.totalOrders += data.totalOrders;
    } else if (data.totalOrders > 5) {
      behaviors.frequent_buyer.count++;
      behaviors.frequent_buyer.totalRevenue += data.totalSpent;
      behaviors.frequent_buyer.totalOrders += data.totalOrders;
    } else if (data.totalOrders === 1) {
      behaviors.one_time.count++;
      behaviors.one_time.totalRevenue += data.totalSpent;
      behaviors.one_time.totalOrders += data.totalOrders;
    } else {
      behaviors.seasonal.count++;
      behaviors.seasonal.totalRevenue += data.totalSpent;
      behaviors.seasonal.totalOrders += data.totalOrders;
    }
  }

  const totalCustomers = customerData.size;
  const result = [];

  for (const [behavior, data] of Object.entries(behaviors)) {
    if (data.count > 0) {
      result.push({
        behavior: behavior as CustomerBehavior,
        count: data.count,
        percentage: (data.count / totalCustomers) * 100,
        averageOrderValue: data.totalOrders > 0 ? data.totalRevenue / data.totalOrders : 0,
        frequency: data.totalOrders / data.count
      });
    }
  }

  return result;
}

async function calculateTopCustomers(siteId: string, startDate: Date, endDate: Date) {
  const orders = await Order.find({
    siteId,
    createdAt: { $gte: startDate, $lte: endDate },
    status: { $in: ['completed', 'delivered', 'shipped'] }
  }).populate('userId', 'name email');

  const customerData = new Map();
  
  for (const order of orders) {
    const customerId = order.userId.toString();
    if (!customerData.has(customerId)) {
      customerData.set(customerId, {
        customerId,
        customerName: (order.userId as any).name,
        email: (order.userId as any).email,
        totalOrders: 0,
        totalSpent: 0,
        orders: []
      });
    }
    const data = customerData.get(customerId);
    data.totalOrders += 1;
    data.totalSpent += order.totalAmount;
    data.orders.push(order);
  }

  const topCustomers = [];
  for (const [customerId, data] of customerData) {
    const averageOrderValue = data.totalOrders > 0 ? data.totalSpent / data.totalOrders : 0;
    const lastOrderDate = Math.max(...data.orders.map((order: any) => order.createdAt.getTime()));
    const firstOrderDate = Math.min(...data.orders.map((order: any) => order.createdAt.getTime()));

    // กำหนด segment และ behavior
    let segment: CustomerSegment = 'new';
    let behavior: CustomerBehavior = 'one_time';

    if (data.totalOrders === 1) {
      segment = 'new';
      behavior = 'one_time';
    } else if (data.totalOrders <= 3) {
      segment = 'returning';
      behavior = 'seasonal';
    } else if (data.totalOrders <= 10) {
      segment = 'loyal';
      behavior = 'frequent_buyer';
    } else {
      segment = 'vip';
      behavior = 'high_value';
    }

    topCustomers.push({
      customerId,
      customerName: data.customerName,
      email: data.email,
      totalOrders: data.totalOrders,
      totalSpent: data.totalSpent,
      averageOrderValue,
      lastOrderDate: new Date(lastOrderDate),
      firstOrderDate: new Date(firstOrderDate),
      customerLifetimeValue: data.totalSpent,
      segment,
      behavior
    });
  }

  return topCustomers.sort((a, b) => b.totalSpent - a.totalSpent).slice(0, 10);
}

async function calculateCustomerAcquisition(siteId: string, startDate: Date, endDate: Date) {
  // จำลองข้อมูล acquisition sources
  const sources = [
    { source: 'organic_search', count: 45, percentage: 45 },
    { source: 'social_media', count: 25, percentage: 25 },
    { source: 'direct', count: 15, percentage: 15 },
    { source: 'referral', count: 10, percentage: 10 },
    { source: 'email', count: 5, percentage: 5 }
  ];

  return sources;
}

async function calculateCustomerRetention(siteId: string, startDate: Date, endDate: Date) {
  // จำลองข้อมูล retention โดยเดือน
  const months = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    const monthKey = currentDate.toISOString().slice(0, 7);
    months.push({
      month: monthKey,
      retainedCustomers: Math.floor(Math.random() * 100) + 50,
      newCustomers: Math.floor(Math.random() * 30) + 10,
      churnedCustomers: Math.floor(Math.random() * 20) + 5,
      retentionRate: Math.floor(Math.random() * 30) + 70
    });
    
    currentDate.setMonth(currentDate.getMonth() + 1);
  }

  return months;
}

export async function getCustomerAnalytics(siteId: string, startDate: Date, endDate: Date) {
  try {
    let analytics = await (CustomerAnalytics as any).getAnalyticsByPeriod(siteId, startDate, endDate);
    
    if (!analytics) {
      analytics = await generateCustomerAnalytics(siteId, startDate, endDate);
    }
    
    return analytics;
  } catch (err: any) {
    console.error('Error in getCustomerAnalytics:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง customer analytics');
  }
}

export async function getLatestCustomerAnalytics(siteId: string) {
  try {
    const analytics = await (CustomerAnalytics as any).getLatestAnalytics(siteId);
    if (!analytics) {
      throw new HttpError(404, 'ไม่พบ customer analytics');
    }
    return analytics;
  } catch (err: any) {
    console.error('Error in getLatestCustomerAnalytics:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง latest customer analytics');
  }
}

// Export functions สำหรับ routes
export async function getCustomerSegments(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateCustomerSegments(siteId, startDate, endDate);
  } catch (err: any) {
    console.error('Error in getCustomerSegments:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง customer segments');
  }
}

export async function getCustomerBehaviors(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateCustomerBehaviors(siteId, startDate, endDate);
  } catch (err: any) {
    console.error('Error in getCustomerBehaviors:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง customer behaviors');
  }
}

export async function getTopCustomers(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateTopCustomers(siteId, startDate, endDate);
  } catch (err: any) {
    console.error('Error in getTopCustomers:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง top customers');
  }
}

export async function getCustomerAcquisition(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateCustomerAcquisition(siteId, startDate, endDate);
  } catch (err: any) {
    console.error('Error in getCustomerAcquisition:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง customer acquisition');
  }
}

export async function getCustomerRetention(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateCustomerRetention(siteId, startDate, endDate);
  } catch (err: any) {
    console.error('Error in getCustomerRetention:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง customer retention');
  }
} 