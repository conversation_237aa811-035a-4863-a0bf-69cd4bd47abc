import { SearchIndex, SearchQuery } from './search.model';
import { searchCache } from './search.cache';
import { ThaiTextProcessor } from './search.utils';

export class SearchExperimentEngine {
    private static experiments: Map<string, SearchExperiment> = new Map();

    static async createExperiment(config: ExperimentConfig): Promise<SearchExperiment> {
        const experiment: SearchExperiment = {
            id: config.id,
            name: config.name,
            description: config.description,
            siteId: config.siteId,
            status: 'draft',
            variants: config.variants,
            trafficAllocation: config.trafficAllocation,
            targetMetric: config.targetMetric,
            startDate: config.startDate,
            endDate: config.endDate,
            createdAt: new Date(),
            results: {
                totalParticipants: 0,
                variantResults: new Map()
            }
        };

        this.experiments.set(experiment.id, experiment);
        return experiment;
    }

    static async startExperiment(experimentId: string): Promise<void> {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) {
            throw new Error('Experiment not found');
        }

        experiment.status = 'running';
        experiment.actualStartDate = new Date();

        console.log(`Started experiment: ${experiment.name}`);
    }

    static async stopExperiment(experimentId: string): Promise<ExperimentResults> {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) {
            throw new Error('Experiment not found');
        }

        experiment.status = 'completed';
        experiment.actualEndDate = new Date();

        const results = await this.calculateExperimentResults(experiment);
        // experiment.results = results;

        console.log(`Stopped experiment: ${experiment.name}`, results);
        return results;
    }

    static async assignUserToVariant(experimentId: string, userId: string, siteId: string): Promise<string> {
        const experiment = this.experiments.get(experimentId);
        if (!experiment || experiment.status !== 'running' || experiment.siteId !== siteId) {
            return 'control'; // Default to control variant
        }

        // Check if user is already assigned
        const cacheKey = `experiment:${experimentId}:user:${userId}`;
        const cachedVariant = await searchCache.get(siteId, 'experiment', cacheKey);
        if (cachedVariant) {
            return cachedVariant as string;
        }

        // Assign user to variant based on traffic allocation
        const variant = this.selectVariant(userId, experiment.trafficAllocation);

        // Cache the assignment
        await searchCache.set(siteId, 'experiment', cacheKey, variant, 86400 * 7); // 7 days

        return variant;
    }

    private static selectVariant(userId: string, allocation: TrafficAllocation): string {
        // Use consistent hashing to ensure same user always gets same variant
        const hash = this.hashUserId(userId);
        const percentage = hash % 100;

        let cumulative = 0;
        for (const [variant, percent] of Object.entries(allocation)) {
            cumulative += percent;
            if (percentage < cumulative) {
                return variant;
            }
        }

        return 'control';
    }

    private static hashUserId(userId: string): number {
        let hash = 0;
        for (let i = 0; i < userId.length; i++) {
            const char = userId.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    static async executeSearchWithExperiment(
        experimentId: string,
        variant: string,
        siteId: string,
        query: string,
        filters: any = {},
        sort: any = {},
        page: number = 1,
        limit: number = 20,
        userId?: string
    ): Promise<any> {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) {
            throw new Error('Experiment not found');
        }

        const variantConfig = experiment.variants.find(v => v.name === variant);
        if (!variantConfig) {
            throw new Error('Variant not found');
        }

        // Execute search with variant-specific algorithm
        const results = await this.executeVariantSearch(variantConfig, siteId, query, filters, sort, page, limit);

        // Track experiment participation
        await this.trackExperimentEvent(experimentId, variant, 'search', {
            userId,
            query,
            resultCount: results.pagination.total,
            timestamp: new Date()
        });

        return results;
    }

    private static async executeVariantSearch(
        variant: SearchVariant,
        siteId: string,
        query: string,
        filters: any,
        sort: any,
        page: number,
        limit: number
    ): Promise<any> {
        const searchQuery: any = { siteId };

        switch (variant.algorithm) {
            case 'standard':
                return this.executeStandardSearch(searchQuery, query, filters, sort, page, limit);

            case 'semantic':
                return this.executeSemanticSearch(searchQuery, query, filters, sort, page, limit);

            case 'popularity_boost':
                return this.executePopularityBoostedSearch(searchQuery, query, filters, sort, page, limit, variant.parameters);

            case 'personalized':
                return this.executePersonalizedSearch(searchQuery, query, filters, sort, page, limit, variant.parameters);

            default:
                return this.executeStandardSearch(searchQuery, query, filters, sort, page, limit);
        }
    }

    private static async executeStandardSearch(
        searchQuery: any,
        query: string,
        filters: any,
        sort: any,
        page: number,
        limit: number
    ): Promise<any> {
        if (query) {
            searchQuery.$text = { $search: query };
        }

        this.applyFilters(searchQuery, filters);
        const sortOptions = this.buildSortOptions(sort, !!query);

        const skip = (page - 1) * limit;
        const [results, total] = await Promise.all([
            SearchIndex.find(searchQuery).sort(sortOptions).skip(skip).limit(limit).lean(),
            SearchIndex.countDocuments(searchQuery)
        ]);

        return {
            results,
            pagination: { page, limit, total, pages: Math.ceil(total / limit) }
        };
    }

    private static async executeSemanticSearch(
        searchQuery: any,
        query: string,
        filters: any,
        sort: any,
        page: number,
        limit: number
    ): Promise<any> {
        if (query) {
            const processed = ThaiTextProcessor.processQuery(query);
            const expandedQuery = [
                processed.processed,
                ...processed.synonyms.slice(0, 3)
            ].join(' ');

            searchQuery.$text = { $search: expandedQuery };
        }

        this.applyFilters(searchQuery, filters);
        const sortOptions = this.buildSortOptions(sort, !!query);

        const skip = (page - 1) * limit;
        const [results, total] = await Promise.all([
            SearchIndex.find(searchQuery).sort(sortOptions).skip(skip).limit(limit).lean(),
            SearchIndex.countDocuments(searchQuery)
        ]);

        return {
            results,
            pagination: { page, limit, total, pages: Math.ceil(total / limit) }
        };
    }

    private static async executePopularityBoostedSearch(
        searchQuery: any,
        query: string,
        filters: any,
        sort: any,
        page: number,
        limit: number,
        parameters: any
    ): Promise<any> {
        if (query) {
            searchQuery.$text = { $search: query };
        }

        this.applyFilters(searchQuery, filters);

        // Boost popular items
        const boostFactor = parameters.boostFactor || 2;
        const sortOptions: any = {};

        if (query) {
            sortOptions.score = { $meta: 'textScore' };
        }

        // Add popularity boost to sorting
        sortOptions.popularity = -1 * boostFactor;

        const skip = (page - 1) * limit;
        const [results, total] = await Promise.all([
            SearchIndex.find(searchQuery).sort(sortOptions).skip(skip).limit(limit).lean(),
            SearchIndex.countDocuments(searchQuery)
        ]);

        return {
            results,
            pagination: { page, limit, total, pages: Math.ceil(total / limit) }
        };
    }

    private static async executePersonalizedSearch(
        searchQuery: any,
        query: string,
        filters: any,
        sort: any,
        page: number,
        limit: number,
        parameters: any
    ): Promise<any> {
        // This would integrate with user behavior data
        // For now, implement basic personalization based on user's search history

        if (query) {
            searchQuery.$text = { $search: query };
        }

        this.applyFilters(searchQuery, filters);

        // Personalization would consider:
        // - User's previous searches
        // - User's category preferences
        // - User's brand preferences
        // - Time-based preferences

        const sortOptions = this.buildSortOptions(sort, !!query);

        const skip = (page - 1) * limit;
        const [results, total] = await Promise.all([
            SearchIndex.find(searchQuery).sort(sortOptions).skip(skip).limit(limit).lean(),
            SearchIndex.countDocuments(searchQuery)
        ]);

        return {
            results,
            pagination: { page, limit, total, pages: Math.ceil(total / limit) }
        };
    }

    private static applyFilters(searchQuery: any, filters: any): void {
        if (filters.category && filters.category.length > 0) {
            searchQuery['metadata.category'] = { $in: filters.category };
        }
        if (filters.brand && filters.brand.length > 0) {
            searchQuery['metadata.brand'] = { $in: filters.brand };
        }
        if (filters.priceRange) {
            searchQuery['metadata.price'] = {
                $gte: filters.priceRange.min || 0,
                $lte: filters.priceRange.max || Number.MAX_SAFE_INTEGER
            };
        }
        if (filters.rating) {
            searchQuery['metadata.rating'] = { $gte: filters.rating };
        }
    }

    private static buildSortOptions(sort: any, hasQuery: boolean): any {
        const sortOptions: any = {};

        if (sort.field && sort.order) {
            sortOptions[sort.field] = sort.order === 'asc' ? 1 : -1;
        } else if (hasQuery) {
            sortOptions.score = { $meta: 'textScore' };
            sortOptions.popularity = -1;
        } else {
            sortOptions.popularity = -1;
            sortOptions.lastUpdated = -1;
        }

        return sortOptions;
    }

    private static async trackExperimentEvent(
        experimentId: string,
        variant: string,
        eventType: string,
        data: any
    ): Promise<void> {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) return;

        // Initialize variant results if not exists
        if (!experiment.results.variantResults.has(variant)) {
            experiment.results.variantResults.set(variant, {
                participants: 0,
                events: [],
                metrics: {}
            });
        }

        const variantResult = experiment.results.variantResults.get(variant)!;
        variantResult.events.push({
            type: eventType,
            data,
            timestamp: new Date()
        });

        if (eventType === 'search') {
            variantResult.participants++;
            experiment.results.totalParticipants++;
        }

        // Update metrics based on event type
        this.updateVariantMetrics(variantResult, eventType, data);
    }

    private static updateVariantMetrics(variantResult: VariantResult, eventType: string, data: any): void {
        if (!variantResult.metrics[eventType]) {
            variantResult.metrics[eventType] = {
                count: 0,
                totalValue: 0,
                avgValue: 0
            };
        }

        const metric = variantResult.metrics[eventType];
        metric.count++;

        if (eventType === 'search' && data.resultCount !== undefined) {
            metric.totalValue += data.resultCount;
            metric.avgValue = metric.totalValue / metric.count;
        }
    }

    private static async calculateExperimentResults(experiment: SearchExperiment): Promise<ExperimentResults> {
        const results: ExperimentResults = {
            totalParticipants: experiment.results.totalParticipants,
            variantResults: new Map(),
            winner: null,
            confidence: 0,
            statisticalSignificance: false
        };

        // Calculate metrics for each variant
        const variantEntries = Array.from(experiment.results.variantResults.entries());
        for (const [variantName, variantResult] of variantEntries) {
            const metrics = this.calculateVariantMetrics(variantResult, experiment.targetMetric);
            results.variantResults.set(variantName, metrics);
        }

        // Determine winner and statistical significance
        const winner = this.determineWinner(results.variantResults, experiment.targetMetric);
        results.winner = winner.variant;
        results.confidence = winner.confidence;
        results.statisticalSignificance = winner.confidence > 0.95;

        return results;
    }

    private static calculateVariantMetrics(variantResult: VariantResult, targetMetric: string): VariantMetrics {
        const searchMetric = variantResult.metrics.search;
        const clickMetric = variantResult.metrics.click;
        const conversionMetric = variantResult.metrics.conversion;

        return {
            participants: variantResult.participants,
            avgResultsPerSearch: searchMetric?.avgValue || 0,
            clickThroughRate: searchMetric && clickMetric ?
                (clickMetric.count / searchMetric.count) : 0,
            conversionRate: searchMetric && conversionMetric ?
                (conversionMetric.count / searchMetric.count) : 0,
            targetMetricValue: this.getTargetMetricValue(variantResult, targetMetric)
        };
    }

    private static getTargetMetricValue(variantResult: VariantResult, targetMetric: string): number {
        switch (targetMetric) {
            case 'click_through_rate':
                const searchCount = variantResult.metrics.search?.count || 0;
                const clickCount = variantResult.metrics.click?.count || 0;
                return searchCount > 0 ? clickCount / searchCount : 0;

            case 'conversion_rate':
                const searchCount2 = variantResult.metrics.search?.count || 0;
                const conversionCount = variantResult.metrics.conversion?.count || 0;
                return searchCount2 > 0 ? conversionCount / searchCount2 : 0;

            case 'avg_results_per_search':
                return variantResult.metrics.search?.avgValue || 0;

            default:
                return 0;
        }
    }

    private static determineWinner(variantResults: Map<string, VariantMetrics>, targetMetric: string): {
        variant: string | null;
        confidence: number;
    } {
        const variants = Array.from(variantResults.entries());
        if (variants.length < 2) {
            return { variant: null, confidence: 0 };
        }

        // Simple winner determination - in production, use proper statistical tests
        let bestVariant = variants[0];
        let bestValue = bestVariant[1].targetMetricValue;

        for (const [name, metrics] of variants) {
            if (metrics.targetMetricValue > bestValue) {
                bestVariant = [name, metrics];
                bestValue = metrics.targetMetricValue;
            }
        }

        // Mock confidence calculation - in production, use proper statistical significance testing
        const confidence = Math.min(0.99, 0.5 + (bestValue * 0.5));

        return {
            variant: bestVariant[0],
            confidence
        };
    }

    static getExperiment(experimentId: string): SearchExperiment | undefined {
        return this.experiments.get(experimentId);
    }

    static getAllExperiments(): SearchExperiment[] {
        return Array.from(this.experiments.values());
    }

    static async getExperimentsByStatus(status: ExperimentStatus): Promise<SearchExperiment[]> {
        return Array.from(this.experiments.values()).filter(exp => exp.status === status);
    }
}

// Type definitions
interface ExperimentConfig {
    id: string;
    name: string;
    description: string;
    siteId: string;
    variants: SearchVariant[];
    trafficAllocation: TrafficAllocation;
    targetMetric: string;
    startDate: Date;
    endDate: Date;
}

interface SearchVariant {
    name: string;
    description: string;
    algorithm: 'standard' | 'semantic' | 'popularity_boost' | 'personalized';
    parameters: Record<string, any>;
}

interface TrafficAllocation {
    [variantName: string]: number; // Percentage
}

type ExperimentStatus = 'draft' | 'running' | 'paused' | 'completed' | 'cancelled';

interface SearchExperiment {
    id: string;
    name: string;
    description: string;
    siteId: string;
    status: ExperimentStatus;
    variants: SearchVariant[];
    trafficAllocation: TrafficAllocation;
    targetMetric: string;
    startDate: Date;
    endDate: Date;
    actualStartDate?: Date;
    actualEndDate?: Date;
    createdAt: Date;
    results: {
        totalParticipants: number;
        variantResults: Map<string, VariantResult>;
    };
}

interface ExperimentResults {
    totalParticipants: number;
    variantResults: Map<string, VariantMetrics>;
    winner?: string | null;
    confidence?: number;
    statisticalSignificance?: boolean;
}

interface VariantResult {
    participants: number;
    events: ExperimentEvent[];
    metrics: Record<string, MetricData>;
}

interface ExperimentEvent {
    type: string;
    data: any;
    timestamp: Date;
}

interface MetricData {
    count: number;
    totalValue: number;
    avgValue: number;
}

interface VariantMetrics {
    participants: number;
    avgResultsPerSearch: number;
    clickThroughRate: number;
    conversionRate: number;
    targetMetricValue: number;
}