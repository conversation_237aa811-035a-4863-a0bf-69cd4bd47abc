import { t } from 'elysia';

// Search Schemas
export const SearchQuerySchema = t.Object({
  query: t.String({ error: 'query ต้องเป็นข้อความ' }),
  filters: t.Optional(t.Object({
    category: t.Optional(t.Array(t.String({ error: 'category ต้องเป็นข้อความ' }), { error: 'category ต้องเป็น array ของข้อความ' })),
    brand: t.Optional(t.Array(t.String({ error: 'brand ต้องเป็นข้อความ' }), { error: 'brand ต้องเป็น array ของข้อความ' })),
    priceRange: t.Optional(t.Object({
      min: t.Number({ error: 'min ต้องเป็นตัวเลข' }),
      max: t.Number({ error: 'max ต้องเป็นตัวเลข' })
    })),
    rating: t.Optional(t.Number({ minimum: 1, maximum: 5, error: 'rating ต้องเป็นตัวเลข 1-5' })),
    availability: t.Optional(t.Union([
      t.Literal('in_stock', { error: 'ต้องเป็น in_stock เท่านั้น' }),
      t.Literal('out_of_stock', { error: 'ต้องเป็น out_of_stock เท่านั้น' }),
      t.Literal('pre_order', { error: 'ต้องเป็น pre_order เท่านั้น' })
    ], { error: 'availability ไม่ถูกต้อง' })),
    sortBy: t.Optional(t.Union([
      t.Literal('relevance', { error: 'ต้องเป็น relevance เท่านั้น' }),
      t.Literal('price_low', { error: 'ต้องเป็น price_low เท่านั้น' }),
      t.Literal('price_high', { error: 'ต้องเป็น price_high เท่านั้น' }),
      t.Literal('newest', { error: 'ต้องเป็น newest เท่านั้น' }),
      t.Literal('rating', { error: 'ต้องเป็น rating เท่านั้น' }),
      t.Literal('popularity', { error: 'ต้องเป็น popularity เท่านั้น' })
    ], { error: 'sortBy ไม่ถูกต้อง' })),
    sortOrder: t.Optional(t.Union([
      t.Literal('asc', { error: 'ต้องเป็น asc เท่านั้น' }),
      t.Literal('desc', { error: 'ต้องเป็น desc เท่านั้น' })
    ], { error: 'sortOrder ไม่ถูกต้อง' }))
  })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' }))
});

export const SearchResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    results: t.Array(t.Object({
      _id: t.String(),
      siteId: t.String(),
      entityType: t.String(),
      entityId: t.String(),
      title: t.String(),
      description: t.Optional(t.String()),
      content: t.String(),
      tags: t.Array(t.String()),
      metadata: t.Object({
        price: t.Optional(t.Number()),
        category: t.Optional(t.String()),
        brand: t.Optional(t.String()),
        rating: t.Optional(t.Number()),
        reviewCount: t.Optional(t.Number()),
        stock: t.Optional(t.Number()),
        status: t.Optional(t.String())
      }),
      searchableFields: t.Object({
        name: t.String(),
        description: t.Optional(t.String()),
        tags: t.Array(t.String()),
        attributes: t.Record(t.String(), t.Any())
      }),
      popularity: t.Number(),
      lastUpdated: t.String()
    })),
    pagination: t.Object({
      page: t.Number(),
      limit: t.Number(),
      total: t.Number(),
      pages: t.Number()
    }),
    suggestions: t.Array(t.String()),
    filters: t.Object({
      categories: t.Array(t.Object({
        name: t.String(),
        count: t.Number()
      })),
      brands: t.Array(t.Object({
        name: t.String(),
        count: t.Number()
      })),
      priceRanges: t.Array(t.Object({
        min: t.Number(),
        max: t.Number(),
        count: t.Number()
      })),
      ratings: t.Array(t.Object({
        rating: t.Number(),
        count: t.Number()
      }))
    })
  })
});

export const SearchIndexSchema = t.Object({
  entityType: t.String(),
  entityId: t.String(),
  title: t.String(),
  description: t.Optional(t.String()),
  content: t.String(),
  tags: t.Array(t.String()),
  metadata: t.Object({
    price: t.Optional(t.Number()),
    category: t.Optional(t.String()),
    brand: t.Optional(t.String()),
    rating: t.Optional(t.Number()),
    reviewCount: t.Optional(t.Number()),
    stock: t.Optional(t.Number()),
    status: t.Optional(t.String())
  }),
  searchableFields: t.Object({
    name: t.String(),
    description: t.Optional(t.String()),
    tags: t.Array(t.String()),
    attributes: t.Record(t.String(), t.Any())
  }),
  popularity: t.Optional(t.Number())
});

export const SearchIndexResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    entityType: t.String(),
    entityId: t.String(),
    title: t.String(),
    description: t.Optional(t.String()),
    content: t.String(),
    tags: t.Array(t.String()),
    metadata: t.Object({
      price: t.Optional(t.Number()),
      category: t.Optional(t.String()),
      brand: t.Optional(t.String()),
      rating: t.Optional(t.Number()),
      reviewCount: t.Optional(t.Number()),
      stock: t.Optional(t.Number()),
      status: t.Optional(t.String())
    }),
    searchableFields: t.Object({
      name: t.String(),
      description: t.Optional(t.String()),
      tags: t.Array(t.String()),
      attributes: t.Record(t.String(), t.Any())
    }),
    popularity: t.Number(),
    lastUpdated: t.String(),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const SearchSuggestionSchema = t.Object({
  query: t.String(),
  suggestions: t.Array(t.String()),
  category: t.Optional(t.String()),
  isActive: t.Optional(t.Boolean())
});

export const SearchSuggestionResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    query: t.String(),
    suggestions: t.Array(t.String()),
    category: t.Optional(t.String()),
    isActive: t.Boolean(),
    usageCount: t.Number(),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const SearchFilterSchema = t.Object({
  name: t.String(),
  type: t.Union([
    t.Literal('category'),
    t.Literal('brand'),
    t.Literal('price'),
    t.Literal('rating'),
    t.Literal('availability'),
    t.Literal('custom')
  ]),
  options: t.Array(t.Object({
    value: t.String(),
    label: t.String(),
    count: t.Optional(t.Number())
  })),
  isActive: t.Optional(t.Boolean()),
  sortOrder: t.Optional(t.Number())
});

export const SearchFilterResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    name: t.String(),
    type: t.String(),
    options: t.Array(t.Object({
      value: t.String(),
      label: t.String(),
      count: t.Optional(t.Number())
    })),
    isActive: t.Boolean(),
    sortOrder: t.Number(),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const SearchQueryLogSchema = t.Object({
  query: t.String(),
  filters: t.Optional(t.Object({
    category: t.Optional(t.Array(t.String())),
    brand: t.Optional(t.Array(t.String())),
    priceRange: t.Optional(t.Object({
      min: t.Number(),
      max: t.Number()
    })),
    rating: t.Optional(t.Number()),
    availability: t.Optional(t.String()),
    sortBy: t.Optional(t.String()),
    sortOrder: t.Optional(t.String())
  })),
  results: t.Object({
    total: t.Number(),
    returned: t.Number(),
    page: t.Number(),
    limit: t.Number()
  }),
  userAgent: t.Optional(t.String()),
  ipAddress: t.Optional(t.String())
});

export const SearchQueryLogResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    query: t.String(),
    filters: t.Optional(t.Object({
      category: t.Optional(t.Array(t.String())),
      brand: t.Optional(t.Array(t.String())),
      priceRange: t.Optional(t.Object({
        min: t.Number(),
        max: t.Number()
      })),
      rating: t.Optional(t.Number()),
      availability: t.Optional(t.String()),
      sortBy: t.Optional(t.String()),
      sortOrder: t.Optional(t.String())
    })),
    sortBy: t.String(),
    sortOrder: t.String(),
    results: t.Object({
      total: t.Number(),
      returned: t.Number(),
      page: t.Number(),
      limit: t.Number()
    }),
    userAgent: t.Optional(t.String()),
    ipAddress: t.Optional(t.String()),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const SearchStatsResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(t.Object({
    _id: t.String(),
    count: t.Number(),
    avgResults: t.Number(),
    avgReturned: t.Number()
  }))
});

export const PopularSearchesResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(t.Object({
    query: t.String(),
    count: t.Number(),
    lastSearched: t.String()
  }))
}); 