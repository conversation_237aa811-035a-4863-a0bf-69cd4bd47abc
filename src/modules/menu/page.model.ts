import { generateFileId } from '@/core/utils/idGenerator';
import { Schema, model } from 'mongoose';

export interface IPageBase {
  _id: string;
  siteId: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  isActive: boolean;
  isPublic: boolean;
  template: 'default' | 'landing' | 'about' | 'contact' | 'blog' | 'custom';
  customCSS?: string;
  customJS?: string;
  featuredImage?: string;
  author?: string;
  publishedAt?: Date;
  seoSettings?: {
    title?: string;
    description?: string;
    keywords?: string;
    ogImage?: string;
    ogTitle?: string;
    ogDescription?: string;
    canonicalUrl?: string;
    robots?: string;
    structuredData?: string;
  };
  visibility: 'public' | 'customer' | 'admin';
  permissions?: string[];
  parentId?: string;
  order: number;
  showInMenu: boolean;
  showInSitemap: boolean;
  allowComments: boolean;
  viewCount: number;
}

export interface IPage extends IPageBase {
  createdAt: Date;
  updatedAt: Date;
}

const pageSchema = new Schema<IPage>(
  {
    _id: {
      type: String,
      required: true,
      trim: true,
      default: () => generateFileId(8),
    },
    siteId: {
      type: String,
      required: true,
      ref: 'Site',
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    content: {
      type: String,
      required: true,
    },
    excerpt: {
      type: String,
      default: '',
    },
    metaTitle: {
      type: String,
      default: '',
    },
    metaDescription: {
      type: String,
      default: '',
    },
    metaKeywords: {
      type: String,
      default: '',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
    template: {
      type: String,
      enum: ['default', 'landing', 'about', 'contact', 'blog', 'custom'],
      default: 'default',
    },
    customCSS: {
      type: String,
      default: '',
    },
    customJS: {
      type: String,
      default: '',
    },
    featuredImage: {
      type: String,
      default: '',
    },
    author: {
      type: String,
      default: '',
    },
    publishedAt: {
      type: Date,
    },
    seoSettings: {
      title: { type: String, default: '' },
      description: { type: String, default: '' },
      keywords: { type: String, default: '' },
      ogImage: { type: String, default: '' },
      ogTitle: { type: String, default: '' },
      ogDescription: { type: String, default: '' },
      canonicalUrl: { type: String, default: '' },
      robots: { type: String, default: 'index, follow' },
      structuredData: { type: String, default: '' },
    },
    visibility: {
      type: String,
      enum: ['public', 'customer', 'admin'],
      default: 'public',
    },
    permissions: [{
      type: String,
    }],
    parentId: {
      type: String,
      ref: 'Page',
    },
    order: {
      type: Number,
      default: 0,
    },
    showInMenu: {
      type: Boolean,
      default: false,
    },
    showInSitemap: {
      type: Boolean,
      default: true,
    },
    allowComments: {
      type: Boolean,
      default: false,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
  }
);

// Indexes for better performance
pageSchema.index({ siteId: 1, slug: 1 }, { unique: true });
pageSchema.index({ siteId: 1, isActive: 1 });
pageSchema.index({ siteId: 1, isPublic: 1 });
pageSchema.index({ siteId: 1, template: 1 });
pageSchema.index({ siteId: 1, publishedAt: -1 });
pageSchema.index({ siteId: 1, order: 1 });

// Virtual for children pages
pageSchema.virtual('children', {
  ref: 'Page',
  localField: '_id',
  foreignField: 'parentId',
});

// Pre-save middleware to generate slug if not provided
pageSchema.pre('save', function(next) {
  if (!this.slug && this.title) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
  
  // Set publishedAt if not set and page is active
  if (this.isActive && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  next();
});

export const Page = model<IPage>('Page', pageSchema);