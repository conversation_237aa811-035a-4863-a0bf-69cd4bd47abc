import { MenuItem, type IMenuItem } from './menu.model';
import { Page, type IPage } from './page.model';
import { logger } from '@/core/utils/logger';

export class MenuService {
  // ===== MENU ITEM METHODS =====
  
  // สร้างรายการเมนู
  async createMenuItem(data: Partial<IMenuItem>): Promise<IMenuItem> {
    try {
      // ตรวจสอบว่า parent menu มีอยู่จริง (ถ้าระบุ)
      if (data.parentId) {
        const parentMenu = await MenuItem.findOne({
          _id: data.parentId,
          siteId: data.siteId
        });
        if (!parentMenu) {
          throw new Error('ไม่พบเมนูหลักที่ระบุ');
        }
      }

      // ตรวจสอบว่า page มีอยู่จริง (ถ้าเป็นประเภท page)
      if (data.type === 'page' && data.pageId) {
        const page = await Page.findOne({
          _id: data.pageId,
          siteId: data.siteId
        });
        if (!page) {
          throw new Error('ไม่พบหน้าเว็บที่ระบุ');
        }
      }

      const menuItem = new MenuItem(data);
      await menuItem.save();
      
      logger.info(`สร้างรายการเมนู: ${menuItem.title} สำหรับเว็บไซต์ ${data.siteId}`);
      
      return menuItem;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการสร้างรายการเมนู:', error);
      throw error;
    }
  }

  // อัพเดทรายการเมนู
  async updateMenuItem(menuId: string, siteId: string, data: Partial<IMenuItem>): Promise<IMenuItem | null> {
    try {
      // ตรวจสอบว่า parent menu มีอยู่จริง (ถ้าระบุ)
      if (data.parentId) {
        const parentMenu = await MenuItem.findOne({
          _id: data.parentId,
          siteId: siteId
        });
        if (!parentMenu) {
          throw new Error('ไม่พบเมนูหลักที่ระบุ');
        }
      }

      const menuItem = await MenuItem.findOneAndUpdate(
        { _id: menuId, siteId },
        data,
        { new: true }
      );

      if (!menuItem) {
        throw new Error('ไม่พบรายการเมนูที่ระบุ');
      }

      logger.info(`อัพเดทรายการเมนู: ${menuItem.title}`);
      
      return menuItem;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการอัพเดทรายการเมนู:', error);
      throw error;
    }
  }

  // ลบรายการเมนู
  async deleteMenuItem(menuId: string, siteId: string): Promise<boolean> {
    try {
      // ตรวจสอบว่ามี submenu หรือไม่
      const hasChildren = await MenuItem.countDocuments({
        parentId: menuId,
        siteId
      });

      if (hasChildren > 0) {
        throw new Error('ไม่สามารถลบเมนูที่มี submenu ได้ กรุณาลบ submenu ก่อน');
      }

      const result = await MenuItem.findOneAndDelete({ _id: menuId, siteId });
      
      if (!result) {
        throw new Error('ไม่พบรายการเมนูที่ระบุ');
      }

      logger.info(`ลบรายการเมนู: ${result.title}`);
      
      return true;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการลบรายการเมนู:', error);
      throw error;
    }
  }

  // ดึงรายการเมนูทั้งหมด
  async getMenuItems(query: {
    siteId?: string;
    type?: string;
    isActive?: boolean;
    parentId?: string;
    visibility?: string;
    showInHeader?: boolean;
    showInFooter?: boolean;
    showInMobile?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{ menuItems: IMenuItem[]; total: number; page: number; totalPages: number }> {
    try {
      const page = query.page || 1;
      const limit = query.limit || 50;
      const skip = (page - 1) * limit;

      const filter: any = {};
      
      if (query.siteId) filter.siteId = query.siteId;
      if (query.type) filter.type = query.type;
      if (query.isActive !== undefined) filter.isActive = query.isActive;
      if (query.parentId !== undefined) filter.parentId = query.parentId;
      if (query.visibility) filter.visibility = query.visibility;
      if (query.showInHeader !== undefined) filter.showInHeader = query.showInHeader;
      if (query.showInFooter !== undefined) filter.showInFooter = query.showInFooter;
      if (query.showInMobile !== undefined) filter.showInMobile = query.showInMobile;

      const [menuItems, total] = await Promise.all([
        MenuItem.find(filter)
          .sort({ order: 1, createdAt: 1 })
          .skip(skip)
          .limit(limit)
          .populate('children')
          .lean(),
        MenuItem.countDocuments(filter)
      ]);

      return {
        menuItems: menuItems as IMenuItem[],
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงรายการเมนู:', error);
      throw error;
    }
  }

  // ดึงโครงสร้างเมนูแบบ hierarchical
  async getMenuTree(siteId: string, location?: 'header' | 'footer' | 'mobile'): Promise<IMenuItem[]> {
    try {
      const filter: any = { 
        siteId, 
        isActive: true,
        parentId: { $exists: false }
      };

      // กรองตามตำแหน่งที่แสดง
      if (location === 'header') filter.showInHeader = true;
      if (location === 'footer') filter.showInFooter = true;
      if (location === 'mobile') filter.showInMobile = true;

      const menuItems = await MenuItem.find(filter)
        .sort({ order: 1 })
        .populate({
          path: 'children',
          match: { isActive: true },
          options: { sort: { order: 1 } },
          populate: {
            path: 'children',
            match: { isActive: true },
            options: { sort: { order: 1 } }
          }
        })
        .lean();

      return menuItems as IMenuItem[];
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงโครงสร้างเมนู:', error);
      throw error;
    }
  }

  // จัดเรียงลำดับเมนู
  async reorderMenuItems(items: Array<{ id: string; order: number; parentId?: string }>): Promise<boolean> {
    try {
      const bulkOps = items.map(item => ({
        updateOne: {
          filter: { _id: item.id },
          update: { 
            order: item.order,
            ...(item.parentId !== undefined ? { parentId: item.parentId } : {})
          }
        }
      }));

      await MenuItem.bulkWrite(bulkOps);
      
      logger.info(`จัดเรียงลำดับเมนู ${items.length} รายการ`);
      
      return true;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการจัดเรียงลำดับเมนู:', error);
      throw error;
    }
  }

  // ===== PAGE METHODS =====

  // สร้างหน้าเว็บ
  async createPage(data: Partial<IPage>): Promise<IPage> {
    try {
      // ตรวจสอบว่า slug ซ้ำหรือไม่
      if (data.slug) {
        const existingPage = await Page.findOne({
          siteId: data.siteId,
          slug: data.slug
        });
        if (existingPage) {
          throw new Error('Slug นี้ถูกใช้แล้ว กรุณาเลือก slug อื่น');
        }
      }

      // ตรวจสอบว่า parent page มีอยู่จริง (ถ้าระบุ)
      if (data.parentId) {
        const parentPage = await Page.findOne({
          _id: data.parentId,
          siteId: data.siteId
        });
        if (!parentPage) {
          throw new Error('ไม่พบหน้าเว็บหลักที่ระบุ');
        }
      }

      const page = new Page(data);
      await page.save();
      
      logger.info(`สร้างหน้าเว็บ: ${page.title} (${page.slug}) สำหรับเว็บไซต์ ${data.siteId}`);
      
      return page;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการสร้างหน้าเว็บ:', error);
      throw error;
    }
  }

  // อัพเดทหน้าเว็บ
  async updatePage(pageId: string, siteId: string, data: Partial<IPage>): Promise<IPage | null> {
    try {
      // ตรวจสอบว่า slug ซ้ำหรือไม่ (ถ้ามีการเปลี่ยน)
      if (data.slug) {
        const existingPage = await Page.findOne({
          siteId,
          slug: data.slug,
          _id: { $ne: pageId }
        });
        if (existingPage) {
          throw new Error('Slug นี้ถูกใช้แล้ว กรุณาเลือก slug อื่น');
        }
      }

      const page = await Page.findOneAndUpdate(
        { _id: pageId, siteId },
        data,
        { new: true }
      );

      if (!page) {
        throw new Error('ไม่พบหน้าเว็บที่ระบุ');
      }

      logger.info(`อัพเดทหน้าเว็บ: ${page.title}`);
      
      return page;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการอัพเดทหน้าเว็บ:', error);
      throw error;
    }
  }

  // ลบหน้าเว็บ
  async deletePage(pageId: string, siteId: string): Promise<boolean> {
    try {
      // ตรวจสอบว่ามี child pages หรือไม่
      const hasChildren = await Page.countDocuments({
        parentId: pageId,
        siteId
      });

      if (hasChildren > 0) {
        throw new Error('ไม่สามารถลบหน้าเว็บที่มีหน้าย่อยได้ กรุณาลบหน้าย่อยก่อน');
      }

      // ตรวจสอบว่ามีเมนูที่ลิงก์ไปหน้านี้หรือไม่
      const linkedMenus = await MenuItem.countDocuments({
        siteId,
        pageId,
        type: 'page'
      });

      if (linkedMenus > 0) {
        throw new Error('ไม่สามารถลบหน้าเว็บที่มีเมนูลิงก์อยู่ได้ กรุณาลบหรือแก้ไขเมนูก่อน');
      }

      const result = await Page.findOneAndDelete({ _id: pageId, siteId });
      
      if (!result) {
        throw new Error('ไม่พบหน้าเว็บที่ระบุ');
      }

      logger.info(`ลบหน้าเว็บ: ${result.title}`);
      
      return true;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการลบหน้าเว็บ:', error);
      throw error;
    }
  }

  // ดึงรายการหน้าเว็บ
  async getPages(query: {
    siteId?: string;
    isActive?: boolean;
    isPublic?: boolean;
    template?: string;
    visibility?: string;
    parentId?: string;
    showInMenu?: boolean;
    showInSitemap?: boolean;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ pages: IPage[]; total: number; page: number; totalPages: number }> {
    try {
      const page = query.page || 1;
      const limit = query.limit || 20;
      const skip = (page - 1) * limit;

      const filter: any = {};
      
      if (query.siteId) filter.siteId = query.siteId;
      if (query.isActive !== undefined) filter.isActive = query.isActive;
      if (query.isPublic !== undefined) filter.isPublic = query.isPublic;
      if (query.template) filter.template = query.template;
      if (query.visibility) filter.visibility = query.visibility;
      if (query.parentId !== undefined) filter.parentId = query.parentId;
      if (query.showInMenu !== undefined) filter.showInMenu = query.showInMenu;
      if (query.showInSitemap !== undefined) filter.showInSitemap = query.showInSitemap;
      
      // ค้นหาในชื่อและเนื้อหา
      if (query.search) {
        filter.$or = [
          { title: { $regex: query.search, $options: 'i' } },
          { content: { $regex: query.search, $options: 'i' } },
          { slug: { $regex: query.search, $options: 'i' } }
        ];
      }

      const [pages, total] = await Promise.all([
        Page.find(filter)
          .sort({ order: 1, publishedAt: -1, createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate('children')
          .lean(),
        Page.countDocuments(filter)
      ]);

      return {
        pages: pages as IPage[],
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงรายการหน้าเว็บ:', error);
      throw error;
    }
  }

  // ดึงหน้าเว็บตาม slug
  async getPageBySlug(siteId: string, slug: string): Promise<IPage | null> {
    try {
      const page = await Page.findOne({
        siteId,
        slug,
        isActive: true,
        isPublic: true
      }).lean();

      // เพิ่มจำนวนการดู
      if (page) {
        await Page.findByIdAndUpdate(page._id, { $inc: { viewCount: 1 } });
      }

      return page as IPage | null;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงหน้าเว็บ:', error);
      throw error;
    }
  }

  // ดึงหน้าเว็บตาม ID
  async getPageById(pageId: string, siteId: string): Promise<IPage | null> {
    try {
      return await Page.findOne({ _id: pageId, siteId }).lean() as IPage | null;
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงหน้าเว็บ:', error);
      throw error;
    }
  }

  // สร้าง sitemap
  async generateSitemap(siteId: string): Promise<IPage[]> {
    try {
      return await Page.find({
        siteId,
        isActive: true,
        isPublic: true,
        showInSitemap: true
      })
      .select('title slug updatedAt')
      .sort({ order: 1 })
      .lean() as IPage[];
    } catch (error) {
      logger.error('เกิดข้อผิดพลาดในการสร้าง sitemap:', error);
      throw error;
    }
  }
}