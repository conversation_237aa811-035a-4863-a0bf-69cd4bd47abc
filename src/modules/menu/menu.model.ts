import { generateFileId } from '@/core/utils/idGenerator';
import { Schema, model } from 'mongoose';

export interface IMenuItemBase {
  _id: string;
  siteId: string;
  title: string;
  url: string;
  type: 'internal' | 'external' | 'page' | 'category' | 'product';
  pageId?: string;
  categoryId?: string;
  productId?: string;
  order: number;
  isActive: boolean;
  parentId?: string;
  icon?: string;
  description?: string;
  target: '_self' | '_blank';
  cssClass?: string;
  permissions?: string[];
  visibility: 'public' | 'customer' | 'admin';
  showInHeader: boolean;
  showInFooter: boolean;
  showInMobile: boolean;
}

export interface IMenuItem extends IMenuItemBase {
  createdAt: Date;
  updatedAt: Date;
}

const menuItemSchema = new Schema<IMenuItem>(
  {
    _id: {
      type: String,
      required: true,
      trim: true,
      default: () => generateFileId(8),
    },
    siteId: {
      type: String,
      required: true,
      ref: 'Site',
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    url: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['internal', 'external', 'page', 'category', 'product'],
      default: 'internal',
    },
    pageId: {
      type: String,
      ref: 'Page',
    },
    categoryId: {
      type: String,
      ref: 'Category',
    },
    productId: {
      type: String,
      ref: 'Product',
    },
    order: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    parentId: {
      type: String,
      ref: 'MenuItem',
    },
    icon: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    target: {
      type: String,
      enum: ['_self', '_blank'],
      default: '_self',
    },
    cssClass: {
      type: String,
      default: '',
    },
    permissions: [{
      type: String,
    }],
    visibility: {
      type: String,
      enum: ['public', 'customer', 'admin'],
      default: 'public',
    },
    showInHeader: {
      type: Boolean,
      default: true,
    },
    showInFooter: {
      type: Boolean,
      default: false,
    },
    showInMobile: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
  }
);

// Indexes for better performance
menuItemSchema.index({ siteId: 1, order: 1 });
menuItemSchema.index({ siteId: 1, parentId: 1 });
menuItemSchema.index({ siteId: 1, isActive: 1 });
menuItemSchema.index({ siteId: 1, type: 1 });

// Virtual for children
menuItemSchema.virtual('children', {
  ref: 'MenuItem',
  localField: '_id',
  foreignField: 'parentId',
});

export const MenuItem = model<IMenuItem>('MenuItem', menuItemSchema);