import mongoose, { Schema, type Document } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet(
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
    12
  )();
  return `${timestamp}${nanoid}`;
};

export interface IWishlist extends Document {
  _id: string;
  siteId: string;
  userId: string;
  userType: 'user' | 'customer';
  name: string;
  description?: string;
  isPublic: boolean;
  isDefault: boolean;
  items: Array<{
    productId: string;
    addedAt: Date;
    notes?: string;
    priority: 'low' | 'medium' | 'high';
    priceWhenAdded?: number;
  }>;
  settings: {
    allowNotifications: boolean;
    notifyOnSale: boolean;
    notifyOnStock: boolean;
    notifyOnPriceDrop: boolean;
  };
  stats: {
    totalItems: number;
    totalValue: number;
    lastUpdated: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IWishlistShare extends Document {
  _id: string;
  siteId: string;
  wishlistId: string;
  sharedBy: string;
  sharedWith: string;
  permissions: 'view' | 'edit' | 'admin';
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const WishlistSchema = new Schema<IWishlist>({
  _id: { type: String, default: generateId },
  siteId: { type: String, required: true, index: true },
  userId: { type: String, required: true, index: true },
  userType: { 
    type: String, 
    required: true, 
    enum: ['user', 'customer']
  },
  name: { type: String, required: true },
  description: { type: String },
  isPublic: { type: Boolean, default: false },
  isDefault: { type: Boolean, default: false },
  items: [{
    productId: { type: String, required: true },
    addedAt: { type: Date, default: Date.now },
    notes: { type: String },
    priority: { 
      type: String, 
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    priceWhenAdded: { type: Number }
  }],
  settings: {
    allowNotifications: { type: Boolean, default: true },
    notifyOnSale: { type: Boolean, default: true },
    notifyOnStock: { type: Boolean, default: true },
    notifyOnPriceDrop: { type: Boolean, default: true }
  },
  stats: {
    totalItems: { type: Number, default: 0 },
    totalValue: { type: Number, default: 0 },
    lastUpdated: { type: Date, default: Date.now }
  }
}, {
  timestamps: true,
  versionKey: false
});

const WishlistShareSchema = new Schema<IWishlistShare>({
  _id: { type: String, default: generateId },
  siteId: { type: String, required: true, index: true },
  wishlistId: { type: String, required: true, index: true },
  sharedBy: { type: String, required: true, index: true },
  sharedWith: { type: String, required: true, index: true },
  permissions: { 
    type: String, 
    required: true, 
    enum: ['view', 'edit', 'admin'],
    default: 'view'
  },
  expiresAt: { type: Date },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  versionKey: false
});

// Indexes
WishlistSchema.index({ siteId: 1, userId: 1 });
WishlistSchema.index({ siteId: 1, isPublic: 1 });
WishlistSchema.index({ 'items.productId': 1 });
WishlistSchema.index({ createdAt: -1 });

WishlistShareSchema.index({ siteId: 1, wishlistId: 1 });
WishlistShareSchema.index({ siteId: 1, sharedWith: 1 });
WishlistShareSchema.index({ isActive: 1 });

// Static methods
WishlistSchema.statics.findByUser = async function(siteId: string, userId: string) {
  return this.find({ siteId, userId }).sort({ createdAt: -1 });
};

WishlistSchema.statics.findDefault = async function(siteId: string, userId: string) {
  return this.findOne({ siteId, userId, isDefault: true });
};

WishlistSchema.statics.findPublic = async function(siteId: string) {
  return this.find({ siteId, isPublic: true }).sort({ createdAt: -1 });
};

WishlistSchema.statics.addItem = async function(siteId: string, wishlistId: string, productId: string, data: any) {
  return this.findOneAndUpdate(
    { siteId, _id: wishlistId },
    { 
      $addToSet: { 
        items: { 
          productId, 
          addedAt: new Date(),
          ...data
        } 
      },
      $inc: { 'stats.totalItems': 1 },
      $set: { 'stats.lastUpdated': new Date() }
    },
    { new: true }
  );
};

WishlistSchema.statics.removeItem = async function(siteId: string, wishlistId: string, productId: string) {
  return this.findOneAndUpdate(
    { siteId, _id: wishlistId },
    { 
      $pull: { items: { productId } },
      $inc: { 'stats.totalItems': -1 },
      $set: { 'stats.lastUpdated': new Date() }
    },
    { new: true }
  );
};

WishlistShareSchema.statics.findSharedWith = async function(siteId: string, userId: string) {
  return this.find({ siteId, sharedWith: userId, isActive: true });
};

WishlistShareSchema.statics.findSharedBy = async function(siteId: string, userId: string) {
  return this.find({ siteId, sharedBy: userId, isActive: true });
};

export const Wishlist = mongoose.model<IWishlist>('Wishlist', WishlistSchema);
export const WishlistShare = mongoose.model<IWishlistShare>('WishlistShare', WishlistShareSchema); 