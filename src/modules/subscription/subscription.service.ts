import { Subscription, SubscriptionDiscount, SubscriptionNotification } from './subscription.model';
import { Site } from '@/modules/site/site.model';
import { User } from '@/modules/user/user.model';
import { HttpError } from '@/core/utils/error';
import { logger } from '@/core/utils/logger';

// แพ็คเกจและราคา (เหมือนกับใน site.service.ts)
const PACKAGE_PRICES = {
  monthly: { moneyPoint: 5, days: 30 }, // ✅ ลดจาก 99 เป็น 5 สำหรับทดสอบ
  yearly: { moneyPoint: 50, days: 365 }, // ✅ ลดจาก 999 เป็น 50
  permanent: { moneyPoint: 100, days: 36500 }, // ✅ ลดจาก 9999 เป็น 100 (100 ปี)
} as const;

type PackageType = keyof typeof PACKAGE_PRICES;

/**
 * สร้าง subscription สำหรับ site owner
 */
export async function createSiteSubscription(
  siteId: string,
  userId: string,
  packageType: PackageType,
  options: {
    autoRenew?: boolean;
    paymentMethod?: string;
    discountCode?: string;
  } = {}
) {
  try {
    const { autoRenew = false, paymentMethod = 'moneyPoint', discountCode } = options;

    // ตรวจสอบว่าเป็น owner ของ site หรือไม่
    const site = await Site.findOne({ _id: siteId, userId });
    if (!site) {
      throw new HttpError(403, 'คุณไม่มีสิทธิ์จัดการไซต์นี้');
    }

    // ตรวจสอบ user
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpError(404, 'ไม่พบข้อมูลผู้ใช้');
    }

    // ดึงข้อมูลแพ็คเกจ
    const packageInfo = PACKAGE_PRICES[packageType];
    if (!packageInfo) {
      throw new HttpError(400, 'แพ็คเกจไม่ถูกต้อง');
    }

    let finalAmount = packageInfo.moneyPoint;
    let originalPrice = packageInfo.moneyPoint;
    let discount = 0;
    let discountType: 'percentage' | 'fixed' = 'fixed';

    // ตรวจสอบ discount code
    if (discountCode) {
      const now = new Date();
      const discountObj = await SubscriptionDiscount.findOne({
        code: discountCode.toUpperCase(),
        isActive: true,
        validFrom: { $lte: now },
        validTo: { $gte: now },
        packageTypes: packageType,
        $expr: {
          $or: [
            { $eq: ['$usageLimit', null] },
            { $lt: ['$usedCount', '$usageLimit'] }
          ]
        }
      });
      if (discountObj) {
        if (discountObj.type === 'percentage') {
          discount = Math.min(
            (originalPrice * discountObj.value) / 100,
            discountObj.maxDiscount || originalPrice
          );
          discountType = 'percentage';
        } else {
          discount = Math.min(discountObj.value, originalPrice);
          discountType = 'fixed';
        }

        if (originalPrice >= (discountObj.minAmount || 0)) {
          finalAmount = originalPrice - discount;

          // อัปเดตการใช้งาน discount
          discountObj.usedCount += 1;
          await discountObj.save();
        }
      }
    }

    // ตรวจสอบยอดเงิน
    if (user.moneyPoint < finalAmount) {
      throw new HttpError(400, `ยอดเงินไม่เพียงพอ ต้องการ ${finalAmount} แต่มี ${user.moneyPoint}`);
    }

    // คำนวณวันที่
    const startDate = new Date();
    const currentExpiry = site.expiredAt > new Date() ? site.expiredAt : new Date();
    const newExpiry = new Date(currentExpiry.getTime() + packageInfo.days * 24 * 60 * 60 * 1000);

    let nextRenewalDate: Date | undefined;
    if (autoRenew && packageType !== 'permanent') {
      nextRenewalDate = new Date(newExpiry);
    }

    // สร้าง subscription
    const subscription = await Subscription.create({
      siteId,
      userId,
      packageType,
      status: 'active',
      autoRenew,
      startDate,
      endDate: packageType === 'permanent' ? undefined : newExpiry,
      nextRenewalDate,
      pricing: {
        amount: finalAmount,
        currency: 'THB',
        originalPrice,
        discount,
        discountType
      },
      billing: {
        paymentMethod,
        billingHistory: [{
          date: new Date(),
          amount: finalAmount,
          status: 'success',
          reference: `SUB-${Date.now()}`
        }]
      },
      renewalHistory: [{
        renewedAt: new Date(),
        packageType,
        daysAdded: packageInfo.days,
        amount: finalAmount,
        method: 'manual',
        previousExpiry: currentExpiry,
        newExpiry
      }],
      stats: {
        totalRenewals: 1,
        totalSpent: finalAmount,
        averageRenewalAmount: finalAmount,
        longestActiveStreak: packageInfo.days,
        currentActiveStreak: packageInfo.days
      }
    });

    // หักเงินจาก user
    user.moneyPoint -= finalAmount;
    await user.save();

    // อัปเดต site expiry
    site.expiredAt = newExpiry;
    await site.save();

    // สร้างการแจ้งเตือน
    await createNotification({
      siteId,
      userId,
      subscriptionId: subscription._id,
      type: 'renewal_success',
      title: 'ต่ออายุแพ็คเกจสำเร็จ',
      message: `ต่ออายุแพ็คเกจ ${packageType} สำเร็จ ไซต์ของคุณจะหมดอายุวันที่ ${newExpiry.toLocaleDateString('th-TH')}`,
      data: {
        packageType,
        amount: finalAmount,
        newExpiry: newExpiry.toISOString()
      }
    });

    logger.info('Site subscription created', {
      siteId,
      userId,
      subscriptionId: subscription._id,
      packageType,
      amount: finalAmount,
      autoRenew
    });

    return subscription;
  } catch (error) {
    logger.error('Failed to create site subscription', {
      siteId,
      userId,
      packageType,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * ดึงรายการ subscription ของ site owner
 */
export async function getUserSubscriptions(
  userId: string,
  options: {
    page?: number;
    limit?: number;
    status?: string;
    siteId?: string;
  } = {}
) {
  try {
    const { page = 1, limit = 20, status, siteId } = options;
    const skip = (page - 1) * limit;

    // สร้าง filter
    const filter: any = { userId };
    if (status) filter.status = status;
    if (siteId) filter.siteId = siteId;

    const subscriptions = await Subscription.find(filter)
      .populate('siteId', 'name fullDomain expiredAt isActive')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Subscription.countDocuments(filter);

    return {
      subscriptions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    logger.error('Failed to get user subscriptions', {
      userId,
      options,
      error: error instanceof Error ? error.message : String(error)
    });
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล subscription');
  }
}

/**
 * ต่ออายุ subscription อัตโนมัติ
 */
export async function renewSubscription(subscriptionId: string) {
  try {
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      throw new HttpError(404, 'ไม่พบ subscription');
    }

    if (subscription.status !== 'active' || !subscription.autoRenew) {
      throw new HttpError(400, 'Subscription ไม่สามารถต่ออายุอัตโนมัติได้');
    }

    // ดึงข้อมูล user และ site
    const [user, site] = await Promise.all([
      User.findById(subscription.userId),
      Site.findById(subscription.siteId)
    ]);

    if (!user || !site) {
      throw new HttpError(404, 'ไม่พบข้อมูล user หรือ site');
    }

    // ดึงข้อมูลแพ็คเกจ
    const packageInfo = PACKAGE_PRICES[subscription.packageType as PackageType];
    const renewalAmount = subscription.pricing.amount; // ใช้ราคาเดิม

    // ตรวจสอบยอดเงิน
    if (user.moneyPoint < renewalAmount) {
      // ปิด auto renew และสร้างการแจ้งเตือน
      subscription.autoRenew = false;
      await subscription.save();

      await createNotification({
        siteId: subscription.siteId,
        userId: subscription.userId,
        subscriptionId: subscription._id,
        type: 'renewal_failure',
        title: 'ต่ออายุแพ็คเกจล้มเหลว',
        message: `ยอดเงินไม่เพียงพอสำหรับการต่ออายุอัตโนมัติ ต้องการ ${renewalAmount} แต่มี ${user.moneyPoint}`,
        data: {
          requiredAmount: renewalAmount,
          currentAmount: user.moneyPoint
        }
      });

      throw new HttpError(400, 'ยอดเงินไม่เพียงพอสำหรับการต่ออายุอัตโนมัติ');
    }

    // คำนวณวันที่ใหม่
    const currentExpiry = site.expiredAt > new Date() ? site.expiredAt : new Date();
    const newExpiry = new Date(currentExpiry.getTime() + packageInfo.days * 24 * 60 * 60 * 1000);
    const nextRenewalDate = new Date(newExpiry);

    // อัปเดต subscription
    subscription.lastRenewalDate = new Date();
    subscription.nextRenewalDate = nextRenewalDate;
    subscription.endDate = newExpiry;

    // เพิ่มประวัติการต่ออายุ
    subscription.renewalHistory.push({
      renewedAt: new Date(),
      packageType: subscription.packageType,
      daysAdded: packageInfo.days,
      amount: renewalAmount,
      method: 'auto',
      previousExpiry: currentExpiry,
      newExpiry
    });

    // เพิ่มประวัติการชำระเงิน
    subscription.billing.billingHistory.push({
      date: new Date(),
      amount: renewalAmount,
      status: 'success',
      reference: `AUTO-${Date.now()}`
    });

    // อัปเดตสถิติ
    subscription.stats.totalRenewals += 1;
    subscription.stats.totalSpent += renewalAmount;
    subscription.stats.averageRenewalAmount = subscription.stats.totalSpent / subscription.stats.totalRenewals;
    subscription.stats.currentActiveStreak += packageInfo.days;

    if (subscription.stats.currentActiveStreak > subscription.stats.longestActiveStreak) {
      subscription.stats.longestActiveStreak = subscription.stats.currentActiveStreak;
    }

    await subscription.save();

    // หักเงินจาก user
    user.moneyPoint -= renewalAmount;
    await user.save();

    // อัปเดต site expiry
    site.expiredAt = newExpiry;
    await site.save();

    // สร้างการแจ้งเตือน
    await createNotification({
      siteId: subscription.siteId,
      userId: subscription.userId,
      subscriptionId: subscription._id,
      type: 'renewal_success',
      title: 'ต่ออายุแพ็คเกจอัตโนมัติสำเร็จ',
      message: `ต่ออายุแพ็คเกจ ${subscription.packageType} อัตโนมัติสำเร็จ ไซต์ของคุณจะหมดอายุวันที่ ${newExpiry.toLocaleDateString('th-TH')}`,
      data: {
        packageType: subscription.packageType,
        amount: renewalAmount,
        newExpiry: newExpiry.toISOString(),
        method: 'auto'
      }
    });

    logger.info('Subscription auto-renewed', {
      subscriptionId: subscription._id,
      siteId: subscription.siteId,
      userId: subscription.userId,
      amount: renewalAmount,
      newExpiry: newExpiry.toISOString()
    });

    return subscription;
  } catch (error) {
    logger.error('Failed to renew subscription', {
      subscriptionId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * ยกเลิก subscription
 */
export async function cancelSubscription(siteId: string, subscriptionId: string, userId: string) {
  try {
    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      siteId,
      userId
    });

    if (!subscription) {
      throw new HttpError(404, 'ไม่พบ subscription');
    }

    subscription.status = 'cancelled';
    subscription.autoRenew = false;
    subscription.nextRenewalDate = undefined;
    await subscription.save();

    // สร้างการแจ้งเตือน
    await createNotification({
      siteId,
      userId,
      subscriptionId: subscription._id,
      type: 'auto_renewal_disabled',
      title: 'ยกเลิก Subscription',
      message: `ยกเลิก subscription แพ็คเกจ ${subscription.packageType} เรียบร้อยแล้ว`,
      data: {
        packageType: subscription.packageType,
        cancelledAt: new Date().toISOString()
      }
    });

    logger.info('Subscription cancelled', {
      subscriptionId: subscription._id,
      siteId,
      userId
    });

    return subscription;
  } catch (error) {
    logger.error('Failed to cancel subscription', {
      subscriptionId,
      siteId,
      userId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * เปิด/ปิด auto renew
 */
export async function toggleAutoRenew(siteId: string, subscriptionId: string, userId: string, autoRenew: boolean) {
  try {
    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      siteId,
      userId,
      status: 'active'
    });

    if (!subscription) {
      throw new HttpError(404, 'ไม่พบ subscription ที่ active');
    }

    subscription.autoRenew = autoRenew;

    if (autoRenew && subscription.endDate) {
      subscription.nextRenewalDate = new Date(subscription.endDate);
    } else {
      subscription.nextRenewalDate = undefined;
    }

    await subscription.save();

    // สร้างการแจ้งเตือน
    await createNotification({
      siteId,
      userId,
      subscriptionId: subscription._id,
      type: autoRenew ? 'renewal_success' : 'auto_renewal_disabled',
      title: autoRenew ? 'เปิดการต่ออายุอัตโนมัติ' : 'ปิดการต่ออายุอัตโนมัติ',
      message: `${autoRenew ? 'เปิด' : 'ปิด'}การต่ออายุอัตโนมัติสำหรับแพ็คเกจ ${subscription.packageType}`,
      data: {
        autoRenew,
        packageType: subscription.packageType
      }
    });

    logger.info('Auto renew toggled', {
      subscriptionId: subscription._id,
      siteId,
      userId,
      autoRenew
    });

    return subscription;
  } catch (error) {
    logger.error('Failed to toggle auto renew', {
      subscriptionId,
      siteId,
      userId,
      autoRenew,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * สร้างการแจ้งเตือน
 */
async function createNotification(data: {
  siteId: string;
  userId: string;
  subscriptionId?: string;
  type: 'expiry_warning' | 'renewal_success' | 'renewal_failure' | 'auto_renewal_disabled' | 'payment_failed';
  title: string;
  message: string;
  data?: any;
}) {
  try {
    await SubscriptionNotification.create({
      ...data,
      sentAt: new Date()
    });
  } catch (error) {
    logger.error('Failed to create notification', {
      data,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * ดึงการแจ้งเตือน
 */
export async function getNotifications(
  siteId: string,
  userId: string,
  options: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
  } = {}
) {
  try {
    const { page = 1, limit = 20, unreadOnly = false } = options;
    const skip = (page - 1) * limit;

    const filter: any = { siteId, userId };
    if (unreadOnly) filter.isRead = false;

    const notifications = await SubscriptionNotification.find(filter)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await SubscriptionNotification.countDocuments(filter);

    return {
      notifications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    logger.error('Failed to get notifications', {
      siteId,
      userId,
      options,
      error: error instanceof Error ? error.message : String(error)
    });
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการแจ้งเตือน');
  }
}

/**
 * อ่านการแจ้งเตือน
 */
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    const notification = await SubscriptionNotification.findOneAndUpdate(
      { _id: notificationId, userId },
      { isRead: true },
      { new: true }
    );

    if (!notification) {
      throw new HttpError(404, 'ไม่พบการแจ้งเตือน');
    }

    return notification;
  } catch (error) {
    logger.error('Failed to mark notification as read', {
      notificationId,
      userId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * ดึงข้อมูลแพ็คเกจทั้งหมด
 */
export function getPackageInfo() {
  return {
    packages: Object.entries(PACKAGE_PRICES).map(([id, info]) => ({
      id,
      name: getPackageName(id as PackageType),
      moneyPoint: info.moneyPoint,
      days: info.days,
      description: getPackageDescription(id as PackageType)
    }))
  };
}

function getPackageName(packageType: PackageType): string {
  const names = {
    monthly: 'รายเดือน',
    yearly: 'รายปี',
    permanent: 'ถาวร'
  };
  return names[packageType];
}

function getPackageDescription(packageType: PackageType): string {
  const descriptions = {
    monthly: 'เช่าเว็บไซต์รายเดือน',
    yearly: 'เช่าเว็บไซต์รายปี',
    permanent: 'เช่าเว็บไซต์ถาวร'
  };
  return descriptions[packageType];
} 