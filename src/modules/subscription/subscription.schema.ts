import { t } from 'elysia';

// Site Package Subscription Schema
export const SiteSubscriptionCreateSchema = t.Object({
  packageType: t.Union([
    t.Literal('monthly', { error: 'ต้องเป็น monthly เท่านั้น' }),
    t.Literal('yearly', { error: 'ต้องเป็น yearly เท่านั้น' }),
    t.Literal('permanent', { error: 'ต้องเป็น permanent เท่านั้น' })
  ], { error: 'ประเภทแพ็คเกจไม่ถูกต้อง' }),
  autoRenew: t.<PERSON>tional(t.<PERSON>({ error: 'autoRenew ต้องเป็น true หรือ false เท่านั้น' })),
  paymentMethod: t.<PERSON>tional(t.String({ error: 'paymentMethod ต้องเป็นข้อความ' })),
  discountCode: t.Optional(t.String({ error: 'discountCode ต้องเป็นข้อความ' }))
});

export const AutoRenewToggleSchema = t.Object({
  autoRenew: t.<PERSON>({ error: 'autoRenew ต้องเป็น true หรือ false เท่านั้น' })
});

// Discount Schema
export const DiscountCreateSchema = t.Object({
  code: t.String({
    minLength: 3,
    maxLength: 20,
    error: 'รหัสส่วนลดต้องมีความยาว 3-20 ตัวอักษร'
  }),
  name: t.String({ error: 'ชื่อส่วนลดต้องเป็นข้อความ' }),
  description: t.String({ error: 'คำอธิบายต้องเป็นข้อความ' }),
  type: t.Union([
    t.Literal('percentage', { error: 'ต้องเป็น percentage เท่านั้น' }),
    t.Literal('fixed', { error: 'ต้องเป็น fixed เท่านั้น' })
  ], { error: 'ประเภทส่วนลดไม่ถูกต้อง' }),
  value: t.Number({
    minimum: 0,
    error: 'ค่าส่วนลดต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  }),
  packageTypes: t.Array(t.Union([
    t.Literal('monthly'),
    t.Literal('yearly'),
    t.Literal('permanent')
  ]), { error: 'ประเภทแพ็คเกจไม่ถูกต้อง' }),
  minAmount: t.Optional(t.Number({
    minimum: 0,
    error: 'จำนวนเงินขั้นต่ำต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  })),
  maxDiscount: t.Optional(t.Number({
    minimum: 0,
    error: 'ส่วนลดสูงสุดต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  })),
  usageLimit: t.Optional(t.Number({
    minimum: 1,
    error: 'จำกัดการใช้งานต้องเป็นตัวเลขที่มากกว่า 0'
  })),
  validFrom: t.String({ error: 'วันที่เริ่มใช้งานต้องเป็นข้อความ' }),
  validTo: t.String({ error: 'วันที่สิ้นสุดต้องเป็นข้อความ' })
});

export const DiscountUpdateSchema = t.Object({
  name: t.Optional(t.String({ error: 'ชื่อส่วนลดต้องเป็นข้อความ' })),
  description: t.Optional(t.String({ error: 'คำอธิบายต้องเป็นข้อความ' })),
  value: t.Optional(t.Number({
    minimum: 0,
    error: 'ค่าส่วนลดต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  })),
  packageTypes: t.Optional(t.Array(t.Union([
    t.Literal('monthly'),
    t.Literal('yearly'),
    t.Literal('permanent')
  ]), { error: 'ประเภทแพ็คเกจไม่ถูกต้อง' })),
  minAmount: t.Optional(t.Number({
    minimum: 0,
    error: 'จำนวนเงินขั้นต่ำต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  })),
  maxDiscount: t.Optional(t.Number({
    minimum: 0,
    error: 'ส่วนลดสูงสุดต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  })),
  usageLimit: t.Optional(t.Number({
    minimum: 1,
    error: 'จำกัดการใช้งานต้องเป็นตัวเลขที่มากกว่า 0'
  })),
  validFrom: t.Optional(t.String({ error: 'วันที่เริ่มใช้งานต้องเป็นข้อความ' })),
  validTo: t.Optional(t.String({ error: 'วันที่สิ้นสุดต้องเป็นข้อความ' })),
  isActive: t.Optional(t.Boolean({ error: 'สถานะต้องเป็น true หรือ false เท่านั้น' }))
});

// Query Schemas
export const SubscriptionListQuerySchema = t.Object({
  page: t.Optional(t.String({ error: 'หน้าต้องเป็นตัวเลข' })),
  limit: t.Optional(t.String({ error: 'จำกัดจำนวนต้องเป็นตัวเลข' })),
  status: t.Optional(t.Union([
    t.Literal('active'),
    t.Literal('paused'),
    t.Literal('cancelled'),
    t.Literal('expired')
  ], { error: 'สถานะไม่ถูกต้อง' })),
  siteId: t.Optional(t.String({ error: 'siteId ต้องเป็นข้อความ' }))
});

export const NotificationListQuerySchema = t.Object({
  page: t.Optional(t.String({ error: 'หน้าต้องเป็นตัวเลข' })),
  limit: t.Optional(t.String({ error: 'จำกัดจำนวนต้องเป็นตัวเลข' })),
  unreadOnly: t.Optional(t.String({ error: 'unreadOnly ต้องเป็น true หรือ false' }))
});

export const DiscountValidateSchema = t.Object({
  code: t.String({ error: 'รหัสส่วนลดต้องเป็นข้อความ' }),
  packageType: t.Union([
    t.Literal('monthly'),
    t.Literal('yearly'),
    t.Literal('permanent')
  ], { error: 'ประเภทแพ็คเกจไม่ถูกต้อง' }),
  amount: t.Number({
    minimum: 0,
    error: 'จำนวนเงินต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0'
  })
}); 