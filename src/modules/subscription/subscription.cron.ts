import { Subscription, SubscriptionNotification } from './subscription.model';
import { renewSubscription } from './subscription.service';
import { logger } from '@/core/utils/logger';

/**
 * Cron job สำหรับตรวจสอบและต่ออายุ subscription อัตโนมัติ
 */
export async function processAutoRenewals() {
    try {
        logger.info('Starting auto renewal process');

        // หา subscription ที่ต้องต่ออายุ
        const now = new Date();
        const subscriptionsToRenew = await Subscription.find({
            status: 'active',
            autoRenew: true,
            nextRenewalDate: { $lte: now }
        });

        logger.info(`Found ${subscriptionsToRenew.length} subscriptions to auto-renew`);

        let successCount = 0;
        let failureCount = 0;

        for (const subscription of subscriptionsToRenew) {
            try {
                await renewSubscription(subscription._id);
                successCount++;

                logger.info('Auto renewal successful', {
                    subscriptionId: subscription._id,
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    packageType: subscription.packageType
                });
            } catch (error) {
                failureCount++;

                logger.error('Auto renewal failed', {
                    subscriptionId: subscription._id,
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    packageType: subscription.packageType,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }

        logger.info('Auto renewal process completed', {
            total: subscriptionsToRenew.length,
            success: successCount,
            failure: failureCount
        });

        return {
            total: subscriptionsToRenew.length,
            success: successCount,
            failure: failureCount
        };
    } catch (error) {
        logger.error('Auto renewal process failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}

/**
 * Cron job สำหรับส่งการแจ้งเตือนก่อนหมดอายุ
 */
export async function sendExpiryWarnings(days: number = 7) {
    try {
        logger.info(`Starting expiry warning process for ${days} days`);

        // หา subscription ที่จะหมดอายุใน X วัน
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + days);

        const expiringSubscriptions = await Subscription.find({
            status: 'active',
            nextRenewalDate: {
                $lte: futureDate,
                $gte: new Date()
            }
        });

        logger.info(`Found ${expiringSubscriptions.length} subscriptions expiring in ${days} days`);

        let notificationCount = 0;

        for (const subscription of expiringSubscriptions) {
            try {
                // ตรวจสอบว่าส่งการแจ้งเตือนแล้วหรือยัง
                const existingNotification = await SubscriptionNotification.findOne({
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    subscriptionId: subscription._id,
                    type: 'expiry_warning',
                    createdAt: {
                        $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // ภายใน 24 ชั่วโมงที่ผ่านมา
                    }
                });

                if (existingNotification) {
                    continue; // ส่งแล้ว ข้าม
                }

                const daysUntilExpiry = Math.ceil(
                    (subscription.nextRenewalDate!.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
                );

                await SubscriptionNotification.create({
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    subscriptionId: subscription._id,
                    type: 'expiry_warning',
                    title: 'แพ็คเกจใกล้หมดอายุ',
                    message: `แพ็คเกจ ${subscription.packageType} ของไซต์คุณจะหมดอายุในอีก ${daysUntilExpiry} วัน${subscription.autoRenew ? ' (จะต่ออายุอัตโนมัติ)' : ''}`,
                    data: {
                        packageType: subscription.packageType,
                        daysUntilExpiry,
                        autoRenew: subscription.autoRenew,
                        expiryDate: subscription.nextRenewalDate?.toISOString()
                    },
                    sentAt: new Date()
                });

                notificationCount++;

                logger.info('Expiry warning sent', {
                    subscriptionId: subscription._id,
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    daysUntilExpiry
                });
            } catch (error) {
                logger.error('Failed to send expiry warning', {
                    subscriptionId: subscription._id,
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }

        logger.info('Expiry warning process completed', {
            total: expiringSubscriptions.length,
            sent: notificationCount
        });

        return {
            total: expiringSubscriptions.length,
            sent: notificationCount
        };
    } catch (error) {
        logger.error('Expiry warning process failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}

/**
 * Cron job สำหรับทำความสะอาด subscription ที่หมดอายุ
 */
export async function cleanupExpiredSubscriptions() {
    try {
        logger.info('Starting expired subscription cleanup');

        // หา subscription ที่หมดอายุแล้วและไม่ได้ต่ออายุอัตโนมัติ
        const expiredSubscriptions = await Subscription.find({
            status: 'active',
            autoRenew: false,
            nextRenewalDate: { $lt: new Date() }
        });

        logger.info(`Found ${expiredSubscriptions.length} expired subscriptions to cleanup`);

        let cleanupCount = 0;

        for (const subscription of expiredSubscriptions) {
            try {
                subscription.status = 'expired';
                subscription.nextRenewalDate = undefined;
                await subscription.save();

                // ส่งการแจ้งเตือน
                await SubscriptionNotification.create({
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    subscriptionId: subscription._id,
                    type: 'expiry_warning',
                    title: 'แพ็คเกจหมดอายุแล้ว',
                    message: `แพ็คเกจ ${subscription.packageType} ของไซต์คุณหมดอายุแล้ว กรุณาต่ออายุเพื่อใช้งานต่อ`,
                    data: {
                        packageType: subscription.packageType,
                        expiredAt: new Date().toISOString()
                    },
                    sentAt: new Date()
                });

                cleanupCount++;

                logger.info('Expired subscription cleaned up', {
                    subscriptionId: subscription._id,
                    siteId: subscription.siteId,
                    userId: subscription.userId
                });
            } catch (error) {
                logger.error('Failed to cleanup expired subscription', {
                    subscriptionId: subscription._id,
                    siteId: subscription.siteId,
                    userId: subscription.userId,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }

        logger.info('Expired subscription cleanup completed', {
            total: expiredSubscriptions.length,
            cleaned: cleanupCount
        });

        return {
            total: expiredSubscriptions.length,
            cleaned: cleanupCount
        };
    } catch (error) {
        logger.error('Expired subscription cleanup failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}

/**
 * รัน cron jobs ทั้งหมด
 */
export async function runSubscriptionCronJobs() {
    try {
        logger.info('Starting subscription cron jobs');

        const results = await Promise.allSettled([
            processAutoRenewals(),
            sendExpiryWarnings(7), // แจ้งเตือน 7 วันก่อนหมดอายุ
            sendExpiryWarnings(3), // แจ้งเตือน 3 วันก่อนหมดอายุ
            sendExpiryWarnings(1), // แจ้งเตือน 1 วันก่อนหมดอายุ
            cleanupExpiredSubscriptions()
        ]);

        const summary = {
            autoRenewals: results[0].status === 'fulfilled' ? results[0].value : { error: results[0].reason },
            expiryWarnings7Days: results[1].status === 'fulfilled' ? results[1].value : { error: results[1].reason },
            expiryWarnings3Days: results[2].status === 'fulfilled' ? results[2].value : { error: results[2].reason },
            expiryWarnings1Day: results[3].status === 'fulfilled' ? results[3].value : { error: results[3].reason },
            cleanup: results[4].status === 'fulfilled' ? results[4].value : { error: results[4].reason }
        };

        logger.info('Subscription cron jobs completed', { summary });

        return summary;
    } catch (error) {
        logger.error('Subscription cron jobs failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}