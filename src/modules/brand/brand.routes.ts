import { Elysia } from 'elysia';
import { 
  createBrand,
  getBrandById,
  getBrandBySlug,
  getFeaturedBrands,
  getActiveBrands,
  updateBrand,
  deleteBrand,
  updateBrandStats,
  getBrandsByStatus,
  getBrandStats,
  getTopBrands
} from './brand.service';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import {
  createBrandSchema,
  updateBrandSchema,
  brandFilterSchema,
  brandResponseSchema,
  brandListResponseSchema,
  brandStatsResponseSchema
} from './brand.schema';

export const brandRoutes = new Elysia({ prefix: '/brand' })
  .use(userAuthPlugin)

  // สร้าง brand ใหม่
  .post('/create', async ({ body, user }: any) => {
    const { siteId, ...brandData } = body;
    
    const result = await createBrand({
      siteId,
      ...brandData
    });
    
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: createBrandSchema,
    response: brandResponseSchema
  })

  // ดึงข้อมูล brand เฉพาะ
  .get('/:brandId', async ({ params }: any) => {
    const { brandId } = params;
    const brand = await getBrandById(brandId);
    
    return {
      success: true,
      message: 'ดึงข้อมูล brand สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: brand
    };
  }, {
    response: brandResponseSchema
  })

  // ดึง brand ตาม slug
  .get('/slug/:slug', async ({ params, query }: any) => {
    const { slug } = params;
    const { siteId } = query;
    
    const brand = await getBrandBySlug(slug, siteId);
    
    return {
      success: true,
      message: 'ดึงข้อมูล brand สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: brand
    };
  }, {
    response: brandResponseSchema
  })

  // ดึง featured brands
  .get('/featured', async ({ query }: any) => {
    const { siteId } = query;
    const brands = await getFeaturedBrands(siteId);
    
    return {
      success: true,
      message: 'ดึง featured brands สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: brands
    };
  }, {
    response: brandResponseSchema
  })

  // ดึง active brands
  .get('/active', async ({ query }: any) => {
    const { siteId } = query;
    const brands = await getActiveBrands(siteId);
    
    return {
      success: true,
      message: 'ดึง active brands สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: brands
    };
  }, {
    response: brandResponseSchema
  })

  // อัปเดต brand
  .put('/:brandId', async ({ params, body }: any) => {
    const { brandId } = params;
    
    const result = await updateBrand(brandId, body);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: updateBrandSchema,
    response: brandResponseSchema
  })

  // ลบ brand
  .delete('/:brandId', async ({ params }: any) => {
    const { brandId } = params;
    
    const result = await deleteBrand(brandId);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    response: brandResponseSchema
  })

  // อัปเดตสถิติ brand
  .post('/:brandId/stats', async ({ params }: any) => {
    const { brandId } = params;
    
    const result = await updateBrandStats(brandId);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    response: brandResponseSchema
  })

  // ดึง brands ตามสถานะ
  .get('/status/:status', async ({ params, query }: any) => {
    const { status } = params;
    const { siteId } = query;
    
    const brands = await getBrandsByStatus(siteId, status);
    
    return {
      success: true,
      message: 'ดึง brands ตามสถานะสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: brands
    };
  }, {
    response: brandResponseSchema
  })

  // ดึง top brands
  .get('/top', async ({ query }: any) => {
    const { siteId, limit = 10 } = query;
    const brands = await getTopBrands(siteId, parseInt(limit));
    
    return {
      success: true,
      message: 'ดึง top brands สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: brands
    };
  }, {
    response: brandResponseSchema
  })

  // สถิติ brand
  .get('/stats/:siteId', async ({ params }: any) => {
    const { siteId } = params;
    const stats = await getBrandStats(siteId);
    
    return {
      success: true,
      message: 'ดึงสถิติ brand สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: stats
    };
  }, {
    response: brandStatsResponseSchema
  }); 