import { t } from 'elysia';

// Create Brand Schema
export const createBrandSchema = t.Object({
  name: t.String({ minLength: 1, error: 'ชื่อที่ต้องการต้องมีความยาวอย่างน้อย 1 ตัวอักษร' }),
  slug: t.String({ minLength: 1, error: 'Slug ที่ต้องการต้องมีความยาวอย่างน้อย 1 ตัวอักษร' }),
  description: t.Optional(t.String({ error: 'คำอธิบายที่ต้องการไม่สามารถว่างได้' })),
  logo: t.Optional(t.String({ error: 'Logo ที่ต้องการไม่สามารถว่างได้' })),
  banner: t.Optional(t.String({ error: 'Banner ที่ต้องการไม่สามารถว่างได้' })),
  website: t.Optional(t.String({ error: 'เว็บไซต์ที่ต้องการไม่สามารถว่างได้' })),
  email: t.Optional(t.String({ error: 'อีเมลที่ต้องการไม่สามารถว่างได้' })),
  phone: t.Optional(t.String({ error: 'เบอร์โทรที่ต้องการไม่สามารถว่างได้' })),
  address: t.Optional(t.String({ error: 'ที่อยู่ที่ต้องการไม่สามารถว่างได้' })),
  facebook: t.Optional(t.String({ error: 'Facebook URL ที่ต้องการไม่สามารถว่างได้' })),
  instagram: t.Optional(t.String({ error: 'Instagram URL ที่ต้องการไม่สามารถว่างได้' })),
  twitter: t.Optional(t.String({ error: 'Twitter URL ที่ต้องการไม่สามารถว่างได้' })),
  youtube: t.Optional(t.String({ error: 'YouTube URL ที่ต้องการไม่สามารถว่างได้' })),
  metaTitle: t.Optional(t.String({ error: 'Meta Title ที่ต้องการไม่สามารถว่างได้' })),
  metaDescription: t.Optional(t.String({ error: 'Meta Description ที่ต้องการไม่สามารถว่างได้' })),
  metaKeywords: t.Optional(t.Array(t.String({ error: 'Meta Keywords ที่ต้องการต้องเป็นอาร์เรย์ของสตริง' }))),
  featured: t.Optional(t.Boolean({ error: 'Featured ที่ต้องการต้องเป็น Boolean' })),
  sortOrder: t.Optional(t.Number({ minimum: 0, error: 'Sort Order ที่ต้องการต้องเป็นจำนวนที่ไม่น้อยกว่าหรือเท่ากับ 0' }))
});

// Update Brand Schema
export const updateBrandSchema = t.Object({
  name: t.Optional(t.String({ minLength: 1, error: 'ชื่อที่ต้องการต้องมีความยาวอย่างน้อย 1 ตัวอักษร' })),
  slug: t.Optional(t.String({ minLength: 1, error: 'Slug ที่ต้องการต้องมีความยาวอย่างน้อย 1 ตัวอักษร' })),
  description: t.Optional(t.String({ error: 'คำอธิบายที่ต้องการไม่สามารถว่างได้' })),
  logo: t.Optional(t.String({ error: 'Logo ที่ต้องการไม่สามารถว่างได้' })),
  banner: t.Optional(t.String({ error: 'Banner ที่ต้องการไม่สามารถว่างได้' })),
  website: t.Optional(t.String({ error: 'เว็บไซต์ที่ต้องการไม่สามารถว่างได้' })),
  email: t.Optional(t.String({ error: 'อีเมลที่ต้องการไม่สามารถว่างได้' })),
  phone: t.Optional(t.String({ error: 'เบอร์โทรที่ต้องการไม่สามารถว่างได้' })),
  address: t.Optional(t.String({ error: 'ที่อยู่ที่ต้องการไม่สามารถว่างได้' })),
  facebook: t.Optional(t.String({ error: 'Facebook URL ที่ต้องการไม่สามารถว่างได้' })),
  instagram: t.Optional(t.String({ error: 'Instagram URL ที่ต้องการไม่สามารถว่างได้' })),
  twitter: t.Optional(t.String({ error: 'Twitter URL ที่ต้องการไม่สามารถว่างได้' })),
  youtube: t.Optional(t.String({ error: 'YouTube URL ที่ต้องการไม่สามารถว่างได้' })),
  metaTitle: t.Optional(t.String({ error: 'Meta Title ที่ต้องการไม่สามารถว่างได้' })),
  metaDescription: t.Optional(t.String({ error: 'Meta Description ที่ต้องการไม่สามารถว่างได้' })),
  metaKeywords: t.Optional(t.Array(t.String({ error: 'Meta Keywords ที่ต้องการต้องเป็นอาร์เรย์ของสตริง' }))),
  status: t.Optional(t.Union([
    t.Literal('active', { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' }),
    t.Literal('inactive', { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' }),
    t.Literal('pending', { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' })
  ], { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' })),
  featured: t.Optional(t.Boolean({ error: 'Featured ที่ต้องการต้องเป็น Boolean' })),
  sortOrder: t.Optional(t.Number({ minimum: 0, error: 'Sort Order ที่ต้องการต้องเป็นจำนวนที่ไม่น้อยกว่าหรือเท่ากับ 0' }))
});

// Brand Filter Schema
export const brandFilterSchema = t.Object({
  status: t.Optional(t.Union([
    t.Literal('active', { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' }),
    t.Literal('inactive', { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' }),
    t.Literal('pending', { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' })
  ], { error: 'สถานะที่ต้องการต้องเป็น "active", "inactive" หรือ "pending"' })),
  featured: t.Optional(t.Boolean({ error: 'Featured ที่ต้องการต้องเป็น Boolean' })),
  search: t.Optional(t.String({ error: 'คำค้นหาที่ต้องการไม่สามารถว่างได้' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'หน้าที่ต้องการต้องเป็นจำนวนที่ไม่น้อยกว่าหรือเท่ากับ 1' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'จำนวนที่ต้องการต้องเป็นจำนวนที่ไม่น้อยกว่าหรือเท่ากับ 1 และไม่มากกว่า 100' }))
});

// Brand Response Schema
export const brandResponseSchema = t.Object({
  success: t.Boolean({ error: 'สถานะสำเร็จที่ต้องการต้องเป็น Boolean' }),
  message: t.String({ error: 'ข้อความที่ต้องการไม่สามารถว่างได้' }),
  statusMessage: t.String({ error: 'ข้อความสถานะที่ต้องการไม่สามารถว่างได้' }),
  timestamp: t.String({ error: 'เวลาที่ต้องการไม่สามารถว่างได้' }),
  data: t.Optional(t.Any({ error: 'ข้อมูลที่ต้องการไม่สามารถว่างได้' }))
});

// Brand List Response Schema
export const brandListResponseSchema = t.Object({
  success: t.Boolean({ error: 'สถานะสำเร็จที่ต้องการต้องเป็น Boolean' }),
  message: t.String({ error: 'ข้อความที่ต้องการไม่สามารถว่างได้' }),
  statusMessage: t.String({ error: 'ข้อความสถานะที่ต้องการไม่สามารถว่างได้' }),
  timestamp: t.String({ error: 'เวลาที่ต้องการไม่สามารถว่างได้' }),
  data: t.Array(t.Any({ error: 'ข้อมูลที่ต้องการไม่สามารถว่างได้' })),
  total: t.Number({ error: 'จำนวนทั้งหมดที่ต้องการต้องเป็นจำนวน' }),
  page: t.Number({ error: 'หน้าที่ต้องการต้องเป็นจำนวน' }),
  limit: t.Number({ error: 'จำนวนที่ต้องการต้องเป็นจำนวน' })
});

// Brand Stats Response Schema
export const brandStatsResponseSchema = t.Object({
  success: t.Boolean({ error: 'สถานะสำเร็จที่ต้องการต้องเป็น Boolean' }),
  message: t.String({ error: 'ข้อความที่ต้องการไม่สามารถว่างได้' }),
  statusMessage: t.String({ error: 'ข้อความสถานะที่ต้องการไม่สามารถว่างได้' }),
  timestamp: t.String({ error: 'เวลาที่ต้องการไม่สามารถว่างได้' }),
  data: t.Object({
    totalBrands: t.Number({ error: 'จำนวนทั้งหมดของแบรนด์ที่ต้องการต้องเป็นจำนวน' }),
    activeBrands: t.Number({ error: 'จำนวนของแบรนด์ที่เป็นสถานะ "active" ที่ต้องการต้องเป็นจำนวน' }),
    featuredBrands: t.Number({ error: 'จำนวนของแบรนด์ที่เป็นสถานะ "featured" ที่ต้องการต้องเป็นจำนวน' }),
    totalProducts: t.Number({ error: 'จำนวนทั้งหมดของสินค้าที่ต้องการต้องเป็นจำนวน' })
  })
}); 