import { Brand, IBrand, type BrandStatus } from './brand.model';
import { HttpError } from '@/core/utils/error';

// Brand Service
export async function createBrand(brandData: {
  siteId: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  banner?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  featured?: boolean;
  sortOrder?: number;
}) {
  try {
    const { siteId, name, slug, description, logo, banner, website, email, phone, address, facebook, instagram, twitter, youtube, metaTitle, metaDescription, metaKeywords, featured, sortOrder } = brandData;

    // ตรวจสอบ slug ซ้ำ
    const existingBrand = await Brand.findOne({ slug, siteId });
    if (existingBrand) {
      throw new HttpError(400, 'Brand slug นี้ถูกใช้แล้ว');
    }

    const brand = await Brand.create({
      siteId,
      name,
      slug,
      description,
      logo,
      banner,
      website,
      email,
      phone,
      address,
      facebook,
      instagram,
      twitter,
      youtube,
      metaTitle,
      metaDescription,
      metaKeywords,
      featured: featured || false,
      sortOrder: sortOrder || 0
    });

    return {
      success: true,
      message: 'สร้าง brand สำเร็จ',
      data: brand
    };
  } catch (err: any) {
    console.error('Error in createBrand:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง brand');
  }
}

export async function getBrandById(brandId: string) {
  try {
    const brand = await Brand.findById(brandId);
    if (!brand) {
      throw new HttpError(404, 'ไม่พบ brand');
    }
    return brand;
  } catch (err: any) {
    console.error('Error in getBrandById:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล brand');
  }
}

export async function getBrandBySlug(slug: string, siteId: string) {
  try {
    const brand = await (Brand as any).getBrandBySlug(slug, siteId);
    if (!brand) {
      throw new HttpError(404, 'ไม่พบ brand');
    }
    return brand;
  } catch (err: any) {
    console.error('Error in getBrandBySlug:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล brand');
  }
}

export async function getFeaturedBrands(siteId: string) {
  try {
    const brands = await (Brand as any).getFeaturedBrands(siteId);
    return brands;
  } catch (err: any) {
    console.error('Error in getFeaturedBrands:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง featured brands');
  }
}

export async function getActiveBrands(siteId: string) {
  try {
    const brands = await (Brand as any).getActiveBrands(siteId);
    return brands;
  } catch (err: any) {
    console.error('Error in getActiveBrands:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง active brands');
  }
}

export async function updateBrand(brandId: string, updateData: {
  name?: string;
  slug?: string;
  description?: string;
  logo?: string;
  banner?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  status?: BrandStatus;
  featured?: boolean;
  sortOrder?: number;
}) {
  try {
    const brand = await Brand.findById(brandId);
    if (!brand) {
      throw new HttpError(404, 'ไม่พบ brand');
    }

    // ตรวจสอบ slug ซ้ำถ้ามีการอัปเดต
    if (updateData.slug && updateData.slug !== brand.slug) {
      const existingBrand = await Brand.findOne({ slug: updateData.slug, siteId: brand.siteId });
      if (existingBrand) {
        throw new HttpError(400, 'Brand slug นี้ถูกใช้แล้ว');
      }
    }

    // อัปเดตข้อมูล
    Object.assign(brand, updateData);
    await brand.save();

    return {
      success: true,
      message: 'อัปเดต brand สำเร็จ',
      data: brand
    };
  } catch (err: any) {
    console.error('Error in updateBrand:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต brand');
  }
}

export async function deleteBrand(brandId: string) {
  try {
    const brand = await Brand.findById(brandId);
    if (!brand) {
      throw new HttpError(404, 'ไม่พบ brand');
    }

    await Brand.findByIdAndDelete(brandId);

    return {
      success: true,
      message: 'ลบ brand สำเร็จ'
    };
  } catch (err: any) {
    console.error('Error in deleteBrand:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ brand');
  }
}

export async function updateBrandStats(brandId: string) {
  try {
    const brand = await Brand.findById(brandId);
    if (!brand) {
      throw new HttpError(404, 'ไม่พบ brand');
    }

    // อัปเดตสถิติ
    await Promise.all([
      (brand as any).updateProductCount(),
      (brand as any).updateSalesStats(),
      (brand as any).updateRatingStats()
    ]);

    return {
      success: true,
      message: 'อัปเดตสถิติ brand สำเร็จ',
      data: brand
    };
  } catch (err: any) {
    console.error('Error in updateBrandStats:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถิติ brand');
  }
}

export async function getBrandsByStatus(siteId: string, status: BrandStatus) {
  try {
    const brands = await Brand.find({ siteId, status }).sort({ sortOrder: 1, name: 1 });
    return brands;
  } catch (err: any) {
    console.error('Error in getBrandsByStatus:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง brands');
  }
}

// Analytics functions
export async function getBrandStats(siteId: string) {
  try {
    const [totalBrands, activeBrands, featuredBrands, totalProducts] = await Promise.all([
      Brand.countDocuments({ siteId }),
      Brand.countDocuments({ siteId, status: 'active' }),
      Brand.countDocuments({ siteId, status: 'active', featured: true }),
      Brand.aggregate([
        { $match: { siteId } },
        { $group: { _id: null, total: { $sum: '$productCount' } } }
      ])
    ]);

    return {
      totalBrands,
      activeBrands,
      featuredBrands,
      totalProducts: totalProducts[0]?.total || 0
    };
  } catch (err: any) {
    console.error('Error in getBrandStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ brand');
  }
}

export async function getTopBrands(siteId: string, limit: number = 10) {
  try {
    const topBrands = await Brand.find({ siteId, status: 'active' })
      .sort({ totalSales: -1, averageRating: -1 })
      .limit(limit);

    return topBrands;
  } catch (err: any) {
    console.error('Error in getTopBrands:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง top brands');
  }
} 