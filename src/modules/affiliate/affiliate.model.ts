import mongoose, { Schema, type Document } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet(
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
    12
  )();
  return `${timestamp}${nanoid}`;
};

const generateAffiliateCode = () => {
  const nanoid = customAlphabet(
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    8
  )();
  return nanoid;
};

export interface IAffiliate extends Document {
  _id: string;
  siteId: string;
  customerId: string;
  affiliateCode: string;
  status: 'active' | 'inactive' | 'suspended';
  commission: {
    rate: number; // เปอร์เซ็นต์
    minAmount: number;
    maxAmount: number;
  };
  performance: {
    totalSales: number;
    totalRevenue: number;
    totalCommission: number;
    totalOrders: number;
    conversionRate: number;
    clicks: number;
    impressions: number;
  };
  products: Array<{
    productId: string;
    commissionRate: number;
    isActive: boolean;
  }>;
  tracking: {
    lastActivity: Date;
    totalClicks: number;
    uniqueClicks: number;
    totalImpressions: number;
  };
  settings: {
    autoApprove: boolean;
    requireApproval: boolean;
    minPayout: number;
    payoutSchedule: 'weekly' | 'monthly' | 'quarterly';
    paymentMethod: 'bank' | 'paypal' | 'crypto';
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IAffiliateOrder extends Document {
  _id: string;
  siteId: string;
  affiliateId: string;
  orderId: string;
  productId: string;
  customerId: string;
  orderAmount: number;
  commissionAmount: number;
  commissionRate: number;
  status: 'pending' | 'approved' | 'paid' | 'cancelled';
  trackingData: {
    clickTime: Date;
    orderTime: Date;
    ipAddress: string;
    userAgent: string;
    referrer: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IAffiliateClick extends Document {
  _id: string;
  siteId: string;
  affiliateId: string;
  productId: string;
  customerId?: string;
  ipAddress: string;
  userAgent: string;
  referrer: string;
  clickedAt: Date;
  converted: boolean;
  orderId?: string;
}

const AffiliateSchema = new Schema<IAffiliate>({
  _id: { type: String, default: generateId },
  siteId: { type: String, required: true, index: true },
  customerId: { type: String, required: true, index: true },
  affiliateCode: { 
    type: String, 
    required: true, 
    unique: true,
    default: generateAffiliateCode
  },
  status: { 
    type: String, 
    required: true, 
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  commission: {
    rate: { type: Number, required: true, default: 10, min: 0, max: 100 },
    minAmount: { type: Number, default: 0 },
    maxAmount: { type: Number, default: 0 }
  },
  performance: {
    totalSales: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    totalCommission: { type: Number, default: 0 },
    totalOrders: { type: Number, default: 0 },
    conversionRate: { type: Number, default: 0 },
    clicks: { type: Number, default: 0 },
    impressions: { type: Number, default: 0 }
  },
  products: [{
    productId: { type: String, required: true },
    commissionRate: { type: Number, required: true, min: 0, max: 100 },
    isActive: { type: Boolean, default: true }
  }],
  tracking: {
    lastActivity: { type: Date, default: Date.now },
    totalClicks: { type: Number, default: 0 },
    uniqueClicks: { type: Number, default: 0 },
    totalImpressions: { type: Number, default: 0 }
  },
  settings: {
    autoApprove: { type: Boolean, default: false },
    requireApproval: { type: Boolean, default: true },
    minPayout: { type: Number, default: 100 },
    payoutSchedule: { 
      type: String, 
      enum: ['weekly', 'monthly', 'quarterly'],
      default: 'monthly'
    },
    paymentMethod: { 
      type: String, 
      enum: ['bank', 'paypal', 'crypto'],
      default: 'bank'
    }
  }
}, {
  timestamps: true,
  versionKey: false
});

const AffiliateOrderSchema = new Schema<IAffiliateOrder>({
  _id: { type: String, default: generateId },
  siteId: { type: String, required: true, index: true },
  affiliateId: { type: String, required: true, index: true },
  orderId: { type: String, required: true, index: true },
  productId: { type: String, required: true, index: true },
  customerId: { type: String, required: true, index: true },
  orderAmount: { type: Number, required: true },
  commissionAmount: { type: Number, required: true },
  commissionRate: { type: Number, required: true },
  status: { 
    type: String, 
    required: true, 
    enum: ['pending', 'approved', 'paid', 'cancelled'],
    default: 'pending'
  },
  trackingData: {
    clickTime: { type: Date, required: true },
    orderTime: { type: Date, required: true },
    ipAddress: { type: String },
    userAgent: { type: String },
    referrer: { type: String }
  }
}, {
  timestamps: true,
  versionKey: false
});

const AffiliateClickSchema = new Schema<IAffiliateClick>({
  _id: { type: String, default: generateId },
  siteId: { type: String, required: true, index: true },
  affiliateId: { type: String, required: true, index: true },
  productId: { type: String, required: true, index: true },
  customerId: { type: String, index: true },
  ipAddress: { type: String, required: true },
  userAgent: { type: String },
  referrer: { type: String },
  clickedAt: { type: Date, default: Date.now },
  converted: { type: Boolean, default: false },
  orderId: { type: String, index: true }
}, {
  timestamps: true,
  versionKey: false
});

// Indexes
// AffiliateSchema.index({ siteId: 1, customerId: 1 });
// AffiliateSchema.index({ affiliateCode: 1 });
// AffiliateSchema.index({ status: 1 });
// AffiliateSchema.index({ createdAt: -1 });

// AffiliateOrderSchema.index({ siteId: 1, affiliateId: 1 });
// AffiliateOrderSchema.index({ orderId: 1 });
// AffiliateOrderSchema.index({ status: 1 });
// AffiliateOrderSchema.index({ createdAt: -1 });

// AffiliateClickSchema.index({ siteId: 1, affiliateId: 1 });
// AffiliateClickSchema.index({ productId: 1 });
// AffiliateClickSchema.index({ converted: 1 });
// AffiliateClickSchema.index({ clickedAt: -1 });

// Static methods
AffiliateSchema.statics.findByCode = async function(affiliateCode: string) {
  return this.findOne({ affiliateCode, status: 'active' });
};

AffiliateSchema.statics.findByCustomer = async function(siteId: string, customerId: string) {
  return this.findOne({ siteId, customerId });
};

AffiliateSchema.statics.getTopPerformers = async function(siteId: string, limit: number = 10) {
  return this.find({ siteId, status: 'active' })
    .sort({ 'performance.totalRevenue': -1 })
    .limit(limit);
};

AffiliateOrderSchema.statics.findByAffiliate = async function(siteId: string, affiliateId: string) {
  return this.find({ siteId, affiliateId }).sort({ createdAt: -1 });
};

AffiliateOrderSchema.statics.getPendingCommissions = async function(siteId: string) {
  return this.find({ siteId, status: 'pending' });
};

AffiliateClickSchema.statics.getConversionStats = async function(siteId: string, affiliateId: string) {
  return this.aggregate([
    { $match: { siteId, affiliateId } },
    { $group: {
      _id: null,
      totalClicks: { $sum: 1 },
      uniqueClicks: { $sum: { $cond: [{ $eq: ['$converted', false] }, 1, 0] } },
      conversions: { $sum: { $cond: [{ $eq: ['$converted', true] }, 1, 0] } }
    }}
  ]);
};

export const Affiliate = mongoose.model<IAffiliate>('Affiliate', AffiliateSchema);
export const AffiliateOrder = mongoose.model<IAffiliateOrder>('AffiliateOrder', AffiliateOrderSchema);
export const AffiliateClick = mongoose.model<IAffiliateClick>('AffiliateClick', AffiliateClickSchema); 