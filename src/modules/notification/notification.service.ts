import { Notification, NotificationTemplate, NotificationSettings, NotificationRule, INotification, INotificationTemplate, INotificationSettings, type NotificationType } from './notification.model';
import { User } from '@/modules/user/user.model';
import { Customer } from '@/modules/customer/customer.model';
import { HttpError } from '@/core/utils/error';

// Notification Service
export async function createNotification(siteId: string, notificationData: any) {
  try {
    const notification = await Notification.create({
      siteId,
      ...notificationData
    });

    return notification;
  } catch (err: any) {
    console.error('Error in createNotification:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างการแจ้งเตือน');
  }
}

export async function getNotifications(siteId: string, userId: string, page: number = 1, limit: number = 20, type?: NotificationType, status?: string) {
  try {
    const skip = (page - 1) * limit;
    const query: any = { siteId, recipientId: userId };

    if (type) query.type = type;
    if (status) query.status = status;

    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Notification.countDocuments(query);
    const unreadCount = await (Notification as any).getUnreadCount(siteId, userId);

    return {
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    };
  } catch (err: any) {
    console.error('Error in getNotifications:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการแจ้งเตือน');
  }
}

export async function markAsRead(siteId: string, notificationIds: string[]) {
  try {
    const result = await (Notification as any).markAsRead(siteId, notificationIds);
    return result;
  } catch (err: any) {
    console.error('Error in markAsRead:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะทำเครื่องหมายการแจ้งเตือน');
  }
}

export async function getUnreadCount(siteId: string, userId: string) {
  try {
    const count = await (Notification as any).getUnreadCount(siteId, userId);
    return count;
  } catch (err: any) {
    console.error('Error in getUnreadCount:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงจำนวนการแจ้งเตือน');
  }
}

export async function createNotificationTemplate(siteId: string, templateData: any) {
  try {
    const template = await NotificationTemplate.create({
      siteId,
      ...templateData
    });

    return template;
  } catch (err: any) {
    console.error('Error in createNotificationTemplate:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง template');
  }
}

export async function getNotificationTemplates(siteId: string, type?: NotificationType) {
  try {
    const query: any = { siteId, isActive: true };
    if (type) query.type = type;

    const templates = await NotificationTemplate.find(query);
    return templates;
  } catch (err: any) {
    console.error('Error in getNotificationTemplates:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง templates');
  }
}

export async function getNotificationSettings(siteId: string, userId: string) {
  try {
    let settings = await (NotificationSettings as any).findByUser(siteId, userId);

    if (!settings) {
      // สร้าง settings เริ่มต้น
      settings = await NotificationSettings.create({
        siteId,
        userId,
        userType: 'user' // หรือดึงจาก user data
      });
    }

    return settings;
  } catch (err: any) {
    console.error('Error in getNotificationSettings:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการตั้งค่าการแจ้งเตือน');
  }
}

export async function updateNotificationSettings(siteId: string, userId: string, updates: any) {
  try {
    const settings = await NotificationSettings.findOneAndUpdate(
      { siteId, userId },
      { $set: updates },
      { new: true, upsert: true }
    );

    return settings;
  } catch (err: any) {
    console.error('Error in updateNotificationSettings:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตการตั้งค่าการแจ้งเตือน');
  }
}

export async function sendBulkNotifications(siteId: string, recipients: string[], notificationData: any) {
  try {
    const notifications = [];

    for (const recipientId of recipients) {
      // ตรวจสอบการตั้งค่าการแจ้งเตือน
      const settings = await (NotificationSettings as any).findByUser(siteId, recipientId);

      if (settings && settings.preferences[getPreferenceKey(notificationData.type)]) {
        notifications.push({
          siteId,
          recipientId,
          ...notificationData
        });
      }
    }

    if (notifications.length > 0) {
      const result = await Notification.insertMany(notifications);
      return result;
    }

    return [];
  } catch (err: any) {
    console.error('Error in sendBulkNotifications:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนจำนวนมาก');
  }
}

export async function deleteNotification(siteId: string, notificationId: string, userId: string) {
  try {
    const notification = await Notification.findOneAndDelete({
      siteId,
      _id: notificationId,
      recipientId: userId
    });

    if (!notification) {
      throw new HttpError(404, 'ไม่พบการแจ้งเตือน');
    }

    return { message: 'ลบการแจ้งเตือนสำเร็จ' };
  } catch (err: any) {
    console.error('Error in deleteNotification:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบการแจ้งเตือน');
  }
}

export async function getNotificationStats(siteId: string, userId: string) {
  try {
    const stats = await Notification.aggregate([
      { $match: { siteId, recipientId: userId } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          unread: { $sum: { $cond: [{ $eq: ['$status', 'unread'] }, 1, 0] } }
        }
      }
    ]);

    return stats;
  } catch (err: any) {
    console.error('Error in getNotificationStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติการแจ้งเตือน');
  }
}

// ฟังก์ชันสำหรับการแจ้งเตือนเฉพาะ
export async function notifyTopup(siteId: string, userId: string, amount: number, balance: number) {
  try {
    const template = await (NotificationTemplate as any).findByType(siteId, 'topup');

    const notification = {
      recipientId: userId,
      recipientType: 'user',
      type: 'topup' as NotificationType,
      title: template?.title || 'เติมเงินสำเร็จ',
      message: template?.message.replace('{{amount}}', amount.toString()).replace('{{balance}}', balance.toString()) || `เติมเงิน ${amount} บาท สำเร็จ ยอดคงเหลือ ${balance} บาท`,
      data: { amount, balance },
      priority: 'medium' as const
    };

    return await createNotification(siteId, notification);
  } catch (err: any) {
    console.error('Error in notifyTopup:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนเติมเงิน');
  }
}

export async function notifyNewMember(siteId: string, adminUsers: string[], customerName: string, customerId: string) {
  try {
    const template = await (NotificationTemplate as any).findByType(siteId, 'membership');

    const notifications = adminUsers.map(adminId => ({
      recipientId: adminId,
      recipientType: 'admin',
      type: 'membership' as NotificationType,
      title: template?.title || 'สมาชิกใหม่',
      message: template?.message.replace('{{customerName}}', customerName) || `มีสมาชิกใหม่ ${customerName} สมัครเข้าร่วม`,
      data: { customerId, customerName },
      priority: 'low' as const
    }));

    return await Promise.all(notifications.map(n => createNotification(siteId, n)));
  } catch (err: any) {
    console.error('Error in notifyNewMember:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนสมาชิกใหม่');
  }
}

export async function notifyExpiry(siteId: string, userId: string, expiryDate: Date, daysLeft: number) {
  try {
    const template = await (NotificationTemplate as any).findByType(siteId, 'expiry');

    const notification = {
      recipientId: userId,
      recipientType: 'user',
      type: 'expiry' as NotificationType,
      title: template?.title || 'แจ้งเตือนวันหมดอายุ',
      message: template?.message.replace('{{daysLeft}}', daysLeft.toString()) || `บัญชีของคุณจะหมดอายุในอีก ${daysLeft} วัน`,
      data: { expiryDate, daysLeft },
      priority: daysLeft <= 3 ? 'urgent' as const : 'high' as const
    };

    return await createNotification(siteId, notification);
  } catch (err: any) {
    console.error('Error in notifyExpiry:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนวันหมดอายุ');
  }
}

export async function notifyLowStock(siteId: string, adminUsers: string[], productName: string, productId: string, stockLevel: number, threshold: number) {
  try {
    const template = await (NotificationTemplate as any).findByType(siteId, 'inventory');

    const notifications = adminUsers.map(adminId => ({
      recipientId: adminId,
      recipientType: 'admin',
      type: 'inventory' as NotificationType,
      title: template?.title || 'สต็อกสินค้าต่ำ',
      message: template?.message.replace('{{productName}}', productName).replace('{{stockLevel}}', stockLevel.toString()) || `สินค้า ${productName} เหลือเพียง ${stockLevel} ชิ้น`,
      data: { productId, productName, stockLevel, threshold },
      priority: 'high' as const
    }));

    return await Promise.all(notifications.map(n => createNotification(siteId, n)));
  } catch (err: any) {
    console.error('Error in notifyLowStock:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนสต็อกต่ำ');
  }
}

export async function notifyNewProduct(siteId: string, customerIds: string[], productName: string, productId: string, productImage?: string) {
  try {
    const template = await (NotificationTemplate as any).findByType(siteId, 'product');

    const notifications = customerIds.map(customerId => ({
      recipientId: customerId,
      recipientType: 'customer',
      type: 'product' as NotificationType,
      title: template?.title || 'สินค้าใหม่',
      message: template?.message.replace('{{productName}}', productName) || `มีสินค้าใหม่ ${productName} เข้ามาแล้ว`,
      data: { productId, productName, image: productImage },
      priority: 'low' as const
    }));

    return await sendBulkNotifications(siteId, customerIds, notifications[0]);
  } catch (err: any) {
    console.error('Error in notifyNewProduct:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนสินค้าใหม่');
  }
}

export async function notifyOrderPurchased(siteId: string, adminUsers: string[], customerName: string, orderId: string, amount: number) {
  try {
    const template = await (NotificationTemplate as any).findByType(siteId, 'order');

    const notifications = adminUsers.map(adminId => ({
      recipientId: adminId,
      recipientType: 'admin',
      type: 'order' as NotificationType,
      title: template?.title || 'มีคำสั่งซื้อใหม่',
      message: template?.message.replace('{{customerName}}', customerName).replace('{{amount}}', amount.toString()) || `${customerName} สั่งซื้อสินค้า มูลค่า ${amount} บาท`,
      data: { orderId, customerName, amount },
      priority: 'medium' as const
    }));

    return await Promise.all(notifications.map(n => createNotification(siteId, n)));
  } catch (err: any) {
    console.error('Error in notifyOrderPurchased:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งการแจ้งเตือนคำสั่งซื้อ');
  }
}

// Helper function
function getPreferenceKey(type: NotificationType): string {
  const mapping: Record<NotificationType, string> = {
    order: 'orderUpdates',
    product: 'productAlerts',
    promotion: 'promotions',
    system: 'systemMessages',
    chat: 'chatMessages',
    affiliate: 'affiliateUpdates',
    topup: 'topupAlerts',
    membership: 'membershipUpdates',
    expiry: 'expiryWarnings',
    inventory: 'inventoryAlerts',
    payment: 'paymentNotifications',
    security: 'securityAlerts',
    marketing: 'marketingMessages'
  };

  return mapping[type] || 'systemMessages';
}