import { t } from 'elysia';

// Create Inventory Schema
export const createInventorySchema = t.Object({
  productId: t.String({ minLength: 1, error: 'productId ต้องไม่ว่าง' }),
  variantId: t.Optional(t.String({ error: 'variantId ต้องเป็นข้อความ' })),
  sku: t.String({ minLength: 1, error: 'sku ต้องไม่ว่าง' }),
  quantity: t.Number({ minimum: 0, error: 'quantity ต้องเป็นตัวเลขและไม่ติดลบ' }),
  costPrice: t.Number({ minimum: 0, error: 'costPrice ต้องเป็นตัวเลขและไม่ติดลบ' }),
  lowStockThreshold: t.Optional(t.Number({ minimum: 0, error: 'lowStockThreshold ต้องเป็นตัวเลขและไม่ติดลบ' })),
  reorderPoint: t.Optional(t.Number({ minimum: 0, error: 'reorderPoint ต้องเป็นตัวเลขและไม่ติดลบ' })),
  reorderQuantity: t.Optional(t.Number({ minimum: 1, error: 'reorderQuantity ต้องเป็นตัวเลขและมากกว่า 0' })),
  supplierId: t.Optional(t.String({ error: 'supplierId ต้องเป็นข้อความ' })),
  supplierName: t.Optional(t.String({ error: 'supplierName ต้องเป็นข้อความ' })),
  location: t.Optional(t.String({ error: 'location ต้องเป็นข้อความ' })),
  expiryDate: t.Optional(t.String({ format: 'date-time', error: 'expiryDate ต้องเป็นวันที่' })),
  batchNumber: t.Optional(t.String({ error: 'batchNumber ต้องเป็นข้อความ' }))
});

// Update Inventory Schema
export const updateInventorySchema = t.Object({
  quantity: t.Optional(t.Number({ minimum: 0, error: 'quantity ต้องเป็นตัวเลขและไม่ติดลบ' })),
  costPrice: t.Optional(t.Number({ minimum: 0, error: 'costPrice ต้องเป็นตัวเลขและไม่ติดลบ' })),
  lowStockThreshold: t.Optional(t.Number({ minimum: 0, error: 'lowStockThreshold ต้องเป็นตัวเลขและไม่ติดลบ' })),
  reorderPoint: t.Optional(t.Number({ minimum: 0, error: 'reorderPoint ต้องเป็นตัวเลขและไม่ติดลบ' })),
  reorderQuantity: t.Optional(t.Number({ minimum: 1, error: 'reorderQuantity ต้องเป็นตัวเลขและมากกว่า 0' })),
  supplierId: t.Optional(t.String({ error: 'supplierId ต้องเป็นข้อความ' })),
  supplierName: t.Optional(t.String({ error: 'supplierName ต้องเป็นข้อความ' })),
  location: t.Optional(t.String({ error: 'location ต้องเป็นข้อความ' })),
  expiryDate: t.Optional(t.String({ format: 'date-time', error: 'expiryDate ต้องเป็นวันที่' })),
  batchNumber: t.Optional(t.String({ error: 'batchNumber ต้องเป็นข้อความ' })),
  status: t.Optional(t.Union([
    t.Literal('active', { error: 'ต้องเป็น active เท่านั้น' }),
    t.Literal('inactive', { error: 'ต้องเป็น inactive เท่านั้น' }),
    t.Literal('discontinued', { error: 'ต้องเป็น discontinued เท่านั้น' })
  ], { error: 'status ไม่ถูกต้อง' }))
});

// Adjust Stock Schema
export const adjustStockSchema = t.Object({
  action: t.Union([
    t.Literal('in', { error: 'ต้องเป็น in เท่านั้น' }),
    t.Literal('out', { error: 'ต้องเป็น out เท่านั้น' }),
    t.Literal('adjust', { error: 'ต้องเป็น adjust เท่านั้น' }),
    t.Literal('transfer', { error: 'ต้องเป็น transfer เท่านั้น' }),
    t.Literal('damage', { error: 'ต้องเป็น damage เท่านั้น' }),
    t.Literal('return', { error: 'ต้องเป็น return เท่านั้น' })
  ], { error: 'action ไม่ถูกต้อง' }),
  quantity: t.Number({ minimum: 1, error: 'quantity ต้องเป็นตัวเลขและมากกว่า 0' }),
  referenceId: t.Optional(t.String({ error: 'referenceId ต้องเป็นข้อความ' })),
  referenceType: t.Optional(t.String({ error: 'referenceType ต้องเป็นข้อความ' })),
  notes: t.Optional(t.String({ error: 'notes ต้องเป็นข้อความ' }))
});

// Reserve Stock Schema
export const reserveStockSchema = t.Object({
  quantity: t.Number({ minimum: 1, error: 'quantity ต้องเป็นตัวเลขและมากกว่า 0' })
});

// Release Stock Schema
export const releaseStockSchema = t.Object({
  quantity: t.Number({ minimum: 1, error: 'quantity ต้องเป็นตัวเลขและมากกว่า 0' })
});

// Inventory Filter Schema
export const inventoryFilterSchema = t.Object({
  status: t.Optional(t.Union([
    t.Literal('active', { error: 'ต้องเป็น active เท่านั้น' }),
    t.Literal('inactive', { error: 'ต้องเป็น inactive เท่านั้น' }),
    t.Literal('discontinued', { error: 'ต้องเป็น discontinued เท่านั้น' })
  ], { error: 'status ไม่ถูกต้อง' })),
  lowStock: t.Optional(t.Boolean({ error: 'lowStock ต้องเป็น true หรือ false เท่านั้น' })),
  reorder: t.Optional(t.Boolean({ error: 'reorder ต้องเป็น true หรือ false เท่านั้น' })),
  expiring: t.Optional(t.Number({ minimum: 1, error: 'expiring ต้องเป็นตัวเลขและมากกว่า 0' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' }))
});

// Transaction Filter Schema
export const transactionFilterSchema = t.Object({
  action: t.Optional(t.Union([
    t.Literal('in', { error: 'ต้องเป็น in เท่านั้น' }),
    t.Literal('out', { error: 'ต้องเป็น out เท่านั้น' }),
    t.Literal('adjust', { error: 'ต้องเป็น adjust เท่านั้น' }),
    t.Literal('transfer', { error: 'ต้องเป็น transfer เท่านั้น' }),
    t.Literal('damage', { error: 'ต้องเป็น damage เท่านั้น' }),
    t.Literal('return', { error: 'ต้องเป็น return เท่านั้น' })
  ], { error: 'action ไม่ถูกต้อง' })),
  startDate: t.Optional(t.String({ format: 'date-time', error: 'startDate ต้องเป็นวันที่' })),
  endDate: t.Optional(t.String({ format: 'date-time', error: 'endDate ต้องเป็นวันที่' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' }))
});

// Inventory Response Schema
export const inventoryResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Optional(t.Any())
});

// Inventory List Response Schema
export const inventoryListResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Array(t.Any()),
  total: t.Number({ error: 'total ต้องเป็นตัวเลข' }),
  page: t.Number({ error: 'page ต้องเป็นตัวเลข' }),
  limit: t.Number({ error: 'limit ต้องเป็นตัวเลข' })
});

// Transaction List Response Schema
export const transactionListResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Array(t.Any()),
  total: t.Number({ error: 'total ต้องเป็นตัวเลข' }),
  page: t.Number({ error: 'page ต้องเป็นตัวเลข' }),
  limit: t.Number({ error: 'limit ต้องเป็นตัวเลข' })
});

// Inventory Stats Response Schema
export const inventoryStatsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Object({
    totalItems: t.Number({ error: 'totalItems ต้องเป็นตัวเลข' }),
    lowStockItems: t.Number({ error: 'lowStockItems ต้องเป็นตัวเลข' }),
    reorderItems: t.Number({ error: 'reorderItems ต้องเป็นตัวเลข' }),
    totalValue: t.Number({ error: 'totalValue ต้องเป็นตัวเลข' })
  })
});

// Stock Movement Response Schema
export const stockMovementResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Array(t.Any())
}); 