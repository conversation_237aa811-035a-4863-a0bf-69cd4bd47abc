import { Elysia } from 'elysia';
import { 
  createInventoryItem,
  getInventoryByProduct,
  getInventoryById,
  updateInventoryStock,
  adjustStock,
  reserveStock,
  releaseStock,
  getInventoryTransactions,
  getLowStockItems,
  getReorderItems,
  getExpiringItems,
  getInventoryStats,
  getStockMovement
} from './inventory.service';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import {
  createInventorySchema,
  updateInventorySchema,
  adjustStockSchema,
  reserveStockSchema,
  releaseStockSchema,
  inventoryFilterSchema,
  transactionFilterSchema,
  inventoryResponseSchema,
  inventoryListResponseSchema,
  transactionListResponseSchema,
  inventoryStatsResponseSchema,
  stockMovementResponseSchema
} from './inventory.schema';

export const inventoryRoutes = new Elysia({ prefix: '/inventory' })
  .use(userAuthPlugin)

  // สร้าง inventory item ใหม่
  .post('/create', async ({ body, user }: any) => {
    const { siteId, ...inventoryData } = body;
    
    const result = await createInventoryItem({
      siteId,
      ...inventoryData
    });
    
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: createInventorySchema,
    response: inventoryResponseSchema
  })

  // ดึง inventory ของสินค้า
  .get('/product/:productId', async ({ params, query }: any) => {
    const { productId } = params;
    const { siteId } = query;
    
    const inventory = await getInventoryByProduct(productId, siteId);
    
    return {
      success: true,
      message: 'ดึง inventory ของสินค้าสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: inventory
    };
  }, {
    response: inventoryResponseSchema
  })

  // ดึงข้อมูล inventory เฉพาะ
  .get('/:inventoryId', async ({ params }: any) => {
    const { inventoryId } = params;
    const inventory = await getInventoryById(inventoryId);
    
    return {
      success: true,
      message: 'ดึงข้อมูล inventory สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: inventory
    };
  }, {
    response: inventoryResponseSchema
  })

  // อัปเดต inventory
  .put('/:inventoryId', async ({ params, body }: any) => {
    const { inventoryId } = params;
    
    const result = await updateInventoryStock(inventoryId, body);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: updateInventorySchema,
    response: inventoryResponseSchema
  })

  // ปรับสต็อก
  .post('/:inventoryId/adjust', async ({ params, body, user }: any) => {
    const { inventoryId } = params;
    const { action, quantity, referenceId, referenceType, notes } = body;
    
    const result = await adjustStock(inventoryId, action, quantity, user._id, referenceId, referenceType, notes);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: adjustStockSchema,
    response: inventoryResponseSchema
  })

  // จองสต็อก
  .post('/:inventoryId/reserve', async ({ params, body }: any) => {
    const { inventoryId } = params;
    const { quantity } = body;
    
    const result = await reserveStock(inventoryId, quantity);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: reserveStockSchema,
    response: inventoryResponseSchema
  })

  // ปล่อยสต็อก
  .post('/:inventoryId/release', async ({ params, body }: any) => {
    const { inventoryId } = params;
    const { quantity } = body;
    
    const result = await releaseStock(inventoryId, quantity);
    return {
      ...result,
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString()
    };
  }, {
    body: releaseStockSchema,
    response: inventoryResponseSchema
  })

  // ดึงประวัติการเคลื่อนไหวสต็อก
  .get('/transactions', async ({ query }: any) => {
    const { siteId, action, startDate, endDate, page = 1, limit = 20 } = query;
    const filter: any = {};
    
    if (action) filter.action = action;
    if (startDate && endDate) {
      filter.performedAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }
    
    const transactions = await getInventoryTransactions(siteId, filter);
    const total = transactions.length;
    const skip = (page - 1) * limit;
    const paginatedTransactions = transactions.slice(skip, skip + limit);
    
    return {
      success: true,
      message: 'ดึงประวัติการเคลื่อนไหวสต็อกสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: paginatedTransactions,
      total,
      page,
      limit
    };
  }, {
    query: transactionFilterSchema,
    response: transactionListResponseSchema
  })

  // ดึงรายการสต็อกต่ำ
  .get('/low-stock', async ({ query }: any) => {
    const { siteId } = query;
    const lowStockItems = await getLowStockItems(siteId);
    
    return {
      success: true,
      message: 'ดึงรายการสต็อกต่ำสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: lowStockItems
    };
  }, {
    response: inventoryResponseSchema
  })

  // ดึงรายการที่ต้องสั่งซื้อใหม่
  .get('/reorder', async ({ query }: any) => {
    const { siteId } = query;
    const reorderItems = await getReorderItems(siteId);
    
    return {
      success: true,
      message: 'ดึงรายการที่ต้องสั่งซื้อใหม่สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: reorderItems
    };
  }, {
    response: inventoryResponseSchema
  })

  // ดึงรายการใกล้หมดอายุ
  .get('/expiring', async ({ query }: any) => {
    const { siteId, days = 30 } = query;
    const expiringItems = await getExpiringItems(siteId, parseInt(days));
    
    return {
      success: true,
      message: 'ดึงรายการใกล้หมดอายุสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: expiringItems
    };
  }, {
    response: inventoryResponseSchema
  })

  // สถิติ inventory
  .get('/stats/:siteId', async ({ params }: any) => {
    const { siteId } = params;
    const stats = await getInventoryStats(siteId);
    
    return {
      success: true,
      message: 'ดึงสถิติ inventory สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: stats
    };
  }, {
    response: inventoryStatsResponseSchema
  })

  // การเคลื่อนไหวสต็อก
  .get('/movement/:siteId', async ({ params, query }: any) => {
    const { siteId } = params;
    const { startDate, endDate } = query;
    
    if (!startDate || !endDate) {
      throw new HttpError(400, 'กรุณาระบุ startDate และ endDate');
    }
    
    const movements = await getStockMovement(siteId, new Date(startDate), new Date(endDate));
    
    return {
      success: true,
      message: 'ดึงการเคลื่อนไหวสต็อกสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: movements
    };
  }, {
    response: stockMovementResponseSchema
  }); 