import { Order, IOrder, type OrderStatus, type PaymentStatus, type PaymentMethod } from './order.model';
import { Product } from '../product/product.model';
import { HttpError } from '@/core/utils/error';

// Order Service
export async function createOrder(orderData: {
  siteId: string;
  customerId: string;
  customerEmail: string;
  customerName: string;
  items: Array<{
    productId: string;
    variantId?: string;
    quantity: number;
  }>;
  shippingAddress: any;
  billingAddress: any;
  paymentMethod: PaymentMethod;
  customerNotes?: string;
  adminNotes?: string;
}) {
  try {
    const { siteId, customerId, customerEmail, customerName, items, shippingAddress, billingAddress, paymentMethod, customerNotes, adminNotes } = orderData;

    // ตรวจสอบสินค้าและคำนวณราคา
    const orderItems = [];
    let subtotal = 0;

    for (const item of items) {
      const product = await Product.findById(item.productId);
      if (!product) {
        throw new HttpError(400, `ไม่พบสินค้า ${item.productId}`);
      }

      if (!product.isActive) {
        throw new HttpError(400, `สินค้า ${product.name} ไม่เปิดขาย`);
      }

      // ตรวจสอบสต็อก
      let availableStock = product.stock || 0;
      if (product.hasVariants && item.variantId) {
        const variant = product.variants.find(v => v._id === item.variantId);
        if (!variant) {
          throw new HttpError(400, `ไม่พบ variant ${item.variantId}`);
        }
        availableStock = variant.stock || 0;
      }

      if (availableStock < item.quantity && product.trackStock) {
        throw new HttpError(400, `สินค้า ${product.name} มีสต็อกไม่เพียงพอ`);
      }

      // คำนวณราคา
      let unitPrice = product.price;
      if (product.hasVariants && item.variantId) {
        const variant = product.variants.find(v => v._id === item.variantId);
        if (variant?.price) {
          unitPrice = variant.price;
        }
      }

      const totalPrice = unitPrice * item.quantity;
      subtotal += totalPrice;

      orderItems.push({
        productId: item.productId,
        variantId: item.variantId,
        name: product.name,
        sku: product.hasVariants && item.variantId 
          ? product.variants.find(v => v._id === item.variantId)?.sku 
          : undefined,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        discount: 0,
        finalPrice: totalPrice
      });
    }

    // สร้าง Order
    const order = await Order.create({
      siteId,
      customerId,
      customerEmail,
      customerName,
      items: orderItems,
      subtotal,
      tax: 0, // คำนวณตาม tax rate
      shipping: 0, // คำนวณตาม shipping method
      discount: 0,
      total: subtotal,
      paymentMethod,
      shippingAddress,
      billingAddress,
      customerNotes,
      adminNotes
    });

    return {
      success: true,
      message: 'สร้างออเดอร์สำเร็จ',
      data: order
    };
  } catch (err: any) {
    console.error('Error in createOrder:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างออเดอร์');
  }
}

export async function getOrderById(orderId: string) {
  try {
    const order = await Order.findById(orderId);
    if (!order) {
      throw new HttpError(404, 'ไม่พบออเดอร์');
    }
    return order;
  } catch (err: any) {
    console.error('Error in getOrderById:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลออเดอร์');
  }
}

export async function getOrdersBySite(siteId: string, filter: any = {}) {
  try {
    const query = { siteId, ...filter };
    const orders = await Order.find(query).sort({ createdAt: -1 });
    return orders;
  } catch (err: any) {
    console.error('Error in getOrdersBySite:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการออเดอร์');
  }
}

export async function getOrdersByCustomer(customerId: string, siteId: string) {
  try {
    const orders = await Order.find({ customerId, siteId }).sort({ createdAt: -1 });
    return orders;
  } catch (err: any) {
    console.error('Error in getOrdersByCustomer:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงออเดอร์ของลูกค้า');
  }
}

export async function updateOrderStatus(orderId: string, newStatus: OrderStatus) {
  try {
    const order = await Order.findById(orderId);
    if (!order) {
      throw new HttpError(404, 'ไม่พบออเดอร์');
    }

    await (order as any).updateStatus(newStatus);
    
    return {
      success: true,
      message: 'อัปเดตสถานะออเดอร์สำเร็จ',
      data: order
    };
  } catch (err: any) {
    console.error('Error in updateOrderStatus:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะออเดอร์');
  }
}

export async function updatePaymentStatus(orderId: string, newStatus: PaymentStatus, paymentId?: string) {
  try {
    const order = await Order.findById(orderId);
    if (!order) {
      throw new HttpError(404, 'ไม่พบออเดอร์');
    }

    if (paymentId) {
      order.paymentId = paymentId;
    }

    await (order as any).updatePaymentStatus(newStatus);
    
    return {
      success: true,
      message: 'อัปเดตสถานะการชำระเงินสำเร็จ',
      data: order
    };
  } catch (err: any) {
    console.error('Error in updatePaymentStatus:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะการชำระเงิน');
  }
}

export async function updateOrderShipping(orderId: string, shippingData: {
  trackingNumber?: string;
  estimatedDelivery?: Date;
  shippingMethod?: string;
}) {
  try {
    const order = await Order.findByIdAndUpdate(
      orderId,
      shippingData,
      { new: true }
    );

    if (!order) {
      throw new HttpError(404, 'ไม่พบออเดอร์');
    }

    return {
      success: true,
      message: 'อัปเดตข้อมูลการจัดส่งสำเร็จ',
      data: order
    };
  } catch (err: any) {
    console.error('Error in updateOrderShipping:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตข้อมูลการจัดส่ง');
  }
}

export async function cancelOrder(orderId: string, reason?: string) {
  try {
    const order = await Order.findById(orderId);
    if (!order) {
      throw new HttpError(404, 'ไม่พบออเดอร์');
    }

    if (order.status === 'cancelled') {
      throw new HttpError(400, 'ออเดอร์นี้ถูกยกเลิกแล้ว');
    }

    if (order.status === 'delivered') {
      throw new HttpError(400, 'ไม่สามารถยกเลิกออเดอร์ที่จัดส่งแล้ว');
    }

    order.status = 'cancelled';
    order.cancelledAt = new Date();
    if (reason) {
      order.adminNotes = reason;
    }

    await order.save();

    return {
      success: true,
      message: 'ยกเลิกออเดอร์สำเร็จ',
      data: order
    };
  } catch (err: any) {
    console.error('Error in cancelOrder:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะยกเลิกออเดอร์');
  }
}

// Dashboard Analytics
export async function getOrderStats(siteId: string, dateRange?: { start: Date; end: Date }) {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // ใช้ dateRange ถ้ามี หรือใช้ค่าเริ่มต้น
    const range = dateRange || {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: new Date()
    };

    const [totalOrders, todayOrders, monthlyOrders, pendingOrders, paidOrders, recentOrders] = await Promise.all([
      Order.countDocuments({ siteId }),
      Order.countDocuments({ siteId, createdAt: { $gte: startOfDay } }),
      Order.countDocuments({ siteId, createdAt: { $gte: startOfMonth } }),
      Order.countDocuments({ siteId, status: 'pending' }),
      Order.countDocuments({ siteId, paymentStatus: 'paid' }),
      Order.find({ siteId })
        .sort({ createdAt: -1 })
        .limit(10)
        .select('orderNumber customerName total status createdAt')
    ]);

    const revenue = await Order.aggregate([
      { $match: { siteId, paymentStatus: 'paid' } },
      { $group: { _id: null, total: { $sum: '$total' } } }
    ]);

    // คำนวณเปอร์เซ็นต์การเปลี่ยนแปลง (mock data สำหรับตอนนี้)
    const todayChange = '+5%';
    const monthChange = '+12%';

    return {
      total: totalOrders,
      today: todayOrders,
      month: monthlyOrders,
      totalOrders,
      pendingOrders,
      paidOrders,
      totalRevenue: revenue[0]?.total || 0,
      todayChange,
      monthChange,
      recentOrders: recentOrders.map(order => ({
        _id: order._id,
        orderNumber: order.orderNumber,
        customerName: order.customerName,
        total: order.total,
        status: order.status,
        createdAt: order.createdAt
      }))
    };
  } catch (err: any) {
    console.error('Error in getOrderStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติออเดอร์');
  }
} 