# ระบบโฆษณา (Ads Management System)

ระบบจัดการโฆษณาที่สมบูรณ์ รองรับแบนเนอร์, modal popup, banner slide, ข้อความเลื่อน และสามารถกำหนดหน้าที่แสดงได้

## คุณสมบัติ

### ประเภทโฆษณา (Ad Types)
- ✅ **Banner** - แบนเนอร์โฆษณาทั่วไป
- ✅ **Modal** - หน้าต่าง popup
- ✅ **Slide** - สไลด์โชว์หลายภาพ
- ✅ **Marquee** - ข้อความเลื่อน
- ✅ **Popup** - popup แบบเล็ก
- ✅ **Floating** - โฆษณาลอยตัว

### ตำแหน่งแสดงผล (Positions)
- ✅ **Header** - ส่วนหัวเว็บไซต์
- ✅ **Footer** - ส่วนท้ายเว็บไซต์
- ✅ **Sidebar** - แถบข้าง
- ✅ **Content-top** - ด้านบนเนื้อหา
- ✅ **Content-bottom** - ด้านล่างเนื้อหา
- ✅ **Custom** - ตำแหน่งกำหนดเอง

### การควบคุมการแสดงผล
- ✅ **กำหนดหน้าที่แสดง** - ทุกหน้า, หน้าเฉพาะ, ยกเว้นหน้า
- ✅ **ควบคุมตามอุปกรณ์** - Mobile, Tablet, Desktop
- ✅ **ควบคุมตามผู้ใช้** - Visitor, Customer, Admin
- ✅ **กำหนดวันเวลา** - วันเริ่ม-สิ้นสุด
- ✅ **จำกัดการแสดง** - จำนวนการดู/คลิกสูงสุด

### พฤติกรรมการแสดงผล
- ✅ **ความถี่การแสดง** - ทุกครั้ง, ครั้งเดียวต่อ session/วัน/สัปดาห์
- ✅ **Animation** - Fade, Slide, Bounce, Zoom
- ✅ **Auto Close** - ปิดอัตโนมัติ
- ✅ **Delay** - หน่วงเวลาก่อนแสดง

### การติดตามสถิติ
- ✅ **View Tracking** - นับจำนวนการดู
- ✅ **Click Tracking** - นับจำนวนการคลิก
- ✅ **Analytics** - สถิติแยกตามประเภท
- ✅ **Auto Disable** - ปิดอัตโนมัติเมื่อถึงขีดจำกัด

## API Endpoints

### จัดการโฆษณา (Admin Only)

#### สร้างโฆษณา
```
POST /v1/ads
```

**Body (Banner):**
```json
{
  "title": "แบนเนอร์โปรโมชั่น",
  "type": "banner",
  "position": "header",
  "content": {
    "imageUrl": "/images/banner.jpg",
    "linkUrl": "/promotion",
    "linkTarget": "_self"
  },
  "displaySettings": {
    "showOnPages": "all",
    "showOnMobile": true,
    "showOnTablet": true,
    "showOnDesktop": true,
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-12-31T23:59:59Z",
    "maxViews": 10000,
    "showToVisitors": true,
    "showToCustomers": true,
    "showToAdmins": false
  },
  "behaviorSettings": {
    "showDelay": 2,
    "showFrequency": "once-per-day",
    "animationType": "fade",
    "animationDuration": 500,
    "closeButton": true
  },
  "isActive": true,
  "priority": 1
}
```

**Body (Slide):**
```json
{
  "title": "สไลด์โชว์สินค้า",
  "type": "slide",
  "position": "content-top",
  "content": {
    "backgroundColor": "#ffffff"
  },
  "displaySettings": {
    "showOnPages": "specific",
    "specificPages": ["home", "products"],
    "showOnMobile": true,
    "showOnTablet": true,
    "showOnDesktop": true
  },
  "behaviorSettings": {
    "showFrequency": "always",
    "animationType": "slide"
  },
  "slideSettings": {
    "slides": [
      {
        "id": "slide1",
        "imageUrl": "/images/slide1.jpg",
        "title": "สินค้าใหม่",
        "description": "สินค้าใหม่ล่าสุด",
        "linkUrl": "/products/new",
        "linkTarget": "_self",
        "order": 1
      }
    ],
    "autoPlay": true,
    "autoPlayInterval": 5,
    "showDots": true,
    "showArrows": true,
    "infinite": true,
    "pauseOnHover": true
  },
  "isActive": true
}
```

**Body (Marquee):**
```json
{
  "title": "ข้อความเลื่อน",
  "type": "marquee",
  "position": "header",
  "content": {},
  "displaySettings": {
    "showOnPages": "all",
    "showOnMobile": true,
    "showOnTablet": true,
    "showOnDesktop": true
  },
  "behaviorSettings": {
    "showFrequency": "always"
  },
  "marqueeSettings": {
    "text": "ข่าวสารล่าสุด: ลดราคาพิเศษ 50% ทุกสินค้า!",
    "speed": 50,
    "direction": "left",
    "pauseOnHover": true,
    "backgroundColor": "#ff0000",
    "textColor": "#ffffff",
    "fontSize": "16px",
    "height": "40px"
  },
  "isActive": true
}
```

#### ดึงรายการโฆษณา
```
GET /v1/ads?type=banner&position=header&isActive=true
```

#### ดึงโฆษณาตาม ID
```
GET /v1/ads/:id
```

#### อัพเดทโฆษณา
```
PATCH /v1/ads/:id
```

#### ลบโฆษณา
```
DELETE /v1/ads/:id
```

#### คัดลอกโฆษณา
```
POST /v1/ads/:id/duplicate
```

**Body:**
```json
{
  "title": "ชื่อโฆษณาใหม่"
}
```

#### เปิด/ปิดโฆษณา
```
PATCH /v1/ads/:id/toggle
```

#### ดึงสถิติโฆษณา
```
GET /v1/ads/stats
```

### API สำหรับแสดงผล (Public)

#### ดึงโฆษณาสำหรับหน้าเว็บ
```
POST /v1/ads/page
```

**Body:**
```json
{
  "pageSlug": "home",
  "userType": "visitor",
  "deviceType": "desktop"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "banners": [...],
    "modals": [...],
    "slides": [...],
    "marquees": [...],
    "popups": [...],
    "floating": [...]
  }
}
```

#### ติดตามการดู/คลิก
```
POST /v1/ads/:id/track
```

**Body:**
```json
{
  "action": "view"  // หรือ "click"
}
```

## ตัวอย่างการใช้งาน

### 1. สร้างแบนเนอร์โปรโมชั่น

```bash
curl -X POST http://localhost:5000/v1/ads \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "โปรโมชั่นปีใหม่",
    "type": "banner",
    "position": "header",
    "content": {
      "imageUrl": "/images/newyear-banner.jpg",
      "linkUrl": "/promotion/newyear",
      "linkTarget": "_self"
    },
    "displaySettings": {
      "showOnPages": "all",
      "showOnMobile": true,
      "showOnTablet": true,
      "showOnDesktop": true,
      "startDate": "2024-12-25T00:00:00Z",
      "endDate": "2024-01-15T23:59:59Z",
      "maxViews": 50000
    },
    "behaviorSettings": {
      "showFrequency": "once-per-day",
      "animationType": "fade",
      "closeButton": true
    },
    "isActive": true,
    "priority": 1
  }'
```

### 2. สร้าง Modal Popup

```bash
curl -X POST http://localhost:5000/v1/ads \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "สมัครสมาชิก",
    "type": "modal",
    "position": "custom",
    "content": {
      "html": "<div class=\"modal-content\"><h2>สมัครสมาชิกวันนี้</h2><p>รับส่วนลด 20%</p><button>สมัครเลย</button></div>",
      "customCSS": ".modal-content { padding: 20px; text-align: center; }"
    },
    "displaySettings": {
      "showOnPages": "exclude",
      "excludePages": ["login", "register"],
      "showToVisitors": true,
      "showToCustomers": false
    },
    "behaviorSettings": {
      "showDelay": 5,
      "showFrequency": "once-per-session",
      "animationType": "zoom",
      "autoClose": 15,
      "overlayClose": true,
      "escapeClose": true
    },
    "isActive": true
  }'
```

### 3. สร้างข้อความเลื่อน

```bash
curl -X POST http://localhost:5000/v1/ads \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "ข่าวสารด่วน",
    "type": "marquee",
    "position": "header",
    "content": {},
    "displaySettings": {
      "showOnPages": "all",
      "showOnMobile": true,
      "showOnTablet": true,
      "showOnDesktop": true
    },
    "behaviorSettings": {
      "showFrequency": "always"
    },
    "marqueeSettings": {
      "text": "🔥 Flash Sale วันนี้เท่านั้น! ลดสูงสุด 70% ทุกสินค้า 🔥",
      "speed": 60,
      "direction": "left",
      "pauseOnHover": true,
      "backgroundColor": "#ff4444",
      "textColor": "#ffffff",
      "fontSize": "18px",
      "height": "50px"
    },
    "isActive": true,
    "priority": 10
  }'
```

### 4. ดึงโฆษณาสำหรับหน้าเว็บ

```bash
curl -X POST http://localhost:5000/v1/ads/page \
  -H "Content-Type: application/json" \
  -d '{
    "pageSlug": "home",
    "userType": "visitor",
    "deviceType": "desktop"
  }'
```

### 5. ติดตามการคลิก

```bash
curl -X POST http://localhost:5000/v1/ads/ad123/track \
  -H "Content-Type: application/json" \
  -d '{
    "action": "click"
  }'
```

## Frontend Integration

### JavaScript สำหรับแสดงโฆษณา

```javascript
// ดึงโฆษณาสำหรับหน้าปัจจุบัน
async function loadAds() {
  const response = await fetch('/v1/ads/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      pageSlug: getCurrentPageSlug(),
      userType: getUserType(),
      deviceType: getDeviceType()
    })
  });
  
  const { data } = await response.json();
  
  // แสดงแบนเนอร์
  data.banners.forEach(banner => {
    displayBanner(banner);
    trackView(banner._id);
  });
  
  // แสดง Modal
  data.modals.forEach(modal => {
    setTimeout(() => {
      displayModal(modal);
      trackView(modal._id);
    }, modal.behaviorSettings.showDelay * 1000);
  });
  
  // แสดงข้อความเลื่อน
  data.marquees.forEach(marquee => {
    displayMarquee(marquee);
    trackView(marquee._id);
  });
}

// ติดตามการดู
function trackView(adsId) {
  fetch(`/v1/ads/${adsId}/track`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      action: 'view'
    })
  });
}

// ติดตามการคลิก
function trackClick(adsId) {
  fetch(`/v1/ads/${adsId}/track`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      action: 'click'
    })
  });
}
```

## Response Format

### สำเร็จ
```json
{
  "success": true,
  "message": "ดำเนินการสำเร็จ",
  "data": { ... }
}
```

### ผิดพลาด
```json
{
  "success": false,
  "message": "ข้อความข้อผิดพลาด"
}
```

## การทดสอบ

รันการทดสอบด้วยคำสั่ง:
```bash
bun test src/modules/ads/ads.service.test.ts
```

## Database Schema

### Ads Collection
```typescript
{
  _id: string,
  siteId: string,
  title: string,
  type: 'banner' | 'modal' | 'slide' | 'marquee' | 'popup' | 'floating',
  position: 'header' | 'footer' | 'sidebar' | 'content-top' | 'content-bottom' | 'custom',
  content: {
    html?: string,
    imageUrl?: string,
    videoUrl?: string,
    text?: string,
    linkUrl?: string,
    linkTarget: '_self' | '_blank',
    backgroundColor?: string,
    textColor?: string,
    customCSS?: string,
    customJS?: string
  },
  displaySettings: {
    showOnPages: 'all' | 'specific' | 'exclude',
    specificPages?: string[],
    excludePages?: string[],
    showOnMobile: boolean,
    showOnTablet: boolean,
    showOnDesktop: boolean,
    startDate?: Date,
    endDate?: Date,
    maxViews?: number,
    maxClicks?: number,
    currentViews: number,
    currentClicks: number,
    showToVisitors: boolean,
    showToCustomers: boolean,
    showToAdmins: boolean
  },
  behaviorSettings: {
    autoClose?: number,
    showDelay?: number,
    showFrequency: 'always' | 'once-per-session' | 'once-per-day' | 'once-per-week',
    animationType?: 'fade' | 'slide' | 'bounce' | 'zoom' | 'none',
    animationDuration?: number,
    closeButton: boolean,
    overlayClose: boolean,
    escapeClose: boolean
  },
  slideSettings?: {
    slides: Array<{
      id: string,
      imageUrl: string,
      title?: string,
      description?: string,
      linkUrl?: string,
      linkTarget: '_self' | '_blank',
      order: number
    }>,
    autoPlay: boolean,
    autoPlayInterval: number,
    showDots: boolean,
    showArrows: boolean,
    infinite: boolean,
    pauseOnHover: boolean
  },
  marqueeSettings?: {
    text: string,
    speed: number,
    direction: 'left' | 'right' | 'up' | 'down',
    pauseOnHover: boolean,
    backgroundColor?: string,
    textColor?: string,
    fontSize?: string,
    height?: string
  },
  isActive: boolean,
  priority: number,
  createdBy: string,
  createdAt: Date,
  updatedAt: Date
}
```

## หมายเหตุ

- เฉพาะ Admin เท่านั้นที่สามารถจัดการโฆษณาได้
- ระบบจะปิดโฆษณาอัตโนมัติเมื่อถึงขีดจำกัดการดู/คลิก
- รองรับการแสดงผลแบบ responsive ตามอุปกรณ์
- มีระบบ priority สำหรับจัดลำดับการแสดงผล
- รองรับ Custom CSS/JS สำหรับการปรับแต่ง
- ระบบติดตามสถิติแบบ real-time