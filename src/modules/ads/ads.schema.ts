import { t } from 'elysia';

export const createAdsSchema = t.Object({
  title: t.String({ minLength: 1, maxLength: 200, error: 'title ต้องมีความยาว 1-200 ตัวอักษร' }),
  type: t.Union([
    t.Literal('banner', { error: 'ต้องเป็น banner เท่านั้น' }),
    t.Literal('modal', { error: 'ต้องเป็น modal เท่านั้น' }),
    t.Literal('slide', { error: 'ต้องเป็น slide เท่านั้น' }),
    t.<PERSON>teral('marquee', { error: 'ต้องเป็น marquee เท่านั้น' }),
    t.Literal('popup', { error: 'ต้องเป็น popup เท่านั้น' }),
    t.Literal('floating', { error: 'ต้องเป็น floating เท่านั้น' })
  ], { error: 'ประเภทโฆษณาไม่ถูกต้อง' }),
  position: t.<PERSON>([
    t.Literal('header', { error: 'ต้องเป็น header เท่านั้น' }),
    t.Literal('footer', { error: 'ต้องเป็น footer เท่านั้น' }),
    t.Literal('sidebar', { error: 'ต้องเป็น sidebar เท่านั้น' }),
    t.Literal('content-top', { error: 'ต้องเป็น content-top เท่านั้น' }),
    t.<PERSON>teral('content-bottom', { error: 'ต้องเป็น content-bottom เท่านั้น' }),
    t.Literal('custom', { error: 'ต้องเป็น custom เท่านั้น' })
  ], { error: 'ตำแหน่งโฆษณาไม่ถูกต้อง' }),
  content: t.Object({
    html: t.Optional(t.String({ error: 'html ต้องเป็นข้อความ' })),
    imageUrl: t.Optional(t.String({ error: 'imageUrl ต้องเป็นข้อความ' })),
    videoUrl: t.Optional(t.String({ error: 'videoUrl ต้องเป็นข้อความ' })),
    text: t.Optional(t.String({ error: 'text ต้องเป็นข้อความ' })),
    linkUrl: t.Optional(t.String({ error: 'linkUrl ต้องเป็นข้อความ' })),
    linkTarget: t.Optional(t.Union([
      t.Literal('_self', { error: 'ต้องเป็น _self เท่านั้น' }), 
      t.Literal('_blank', { error: 'ต้องเป็น _blank เท่านั้น' })
    ], { error: 'linkTarget ไม่ถูกต้อง' })),
    backgroundColor: t.Optional(t.String({ error: 'backgroundColor ต้องเป็นข้อความ' })),
    textColor: t.Optional(t.String({ error: 'textColor ต้องเป็นข้อความ' })),
    fontSize: t.Optional(t.String({ error: 'fontSize ต้องเป็นข้อความ' })),
    fontWeight: t.Optional(t.String({ error: 'fontWeight ต้องเป็นข้อความ' })),
    borderRadius: t.Optional(t.String({ error: 'borderRadius ต้องเป็นข้อความ' })),
    padding: t.Optional(t.String({ error: 'padding ต้องเป็นข้อความ' })),
    margin: t.Optional(t.String({ error: 'margin ต้องเป็นข้อความ' })),
    customCSS: t.Optional(t.String({ error: 'customCSS ต้องเป็นข้อความ' })),
    customJS: t.Optional(t.String({ error: 'customJS ต้องเป็นข้อความ' })),
  }),
  displaySettings: t.Object({
    showOnPages: t.Union([
      t.Literal('all', { error: 'ต้องเป็น all เท่านั้น' }),
      t.Literal('specific', { error: 'ต้องเป็น specific เท่านั้น' }),
      t.Literal('exclude', { error: 'ต้องเป็น exclude เท่านั้น' })
    ], { error: 'showOnPages ไม่ถูกต้อง' }),
    specificPages: t.Optional(t.Array(t.String({ error: 'specificPages ต้องเป็น array ของข้อความ' }), { error: 'specificPages ต้องเป็น array ของข้อความ' })),
    excludePages: t.Optional(t.Array(t.String({ error: 'excludePages ต้องเป็น array ของข้อความ' }), { error: 'excludePages ต้องเป็น array ของข้อความ' })),
    showOnMobile: t.Optional(t.Boolean({ error: 'showOnMobile ต้องเป็น true หรือ false เท่านั้น' })),
    showOnTablet: t.Optional(t.Boolean({ error: 'showOnTablet ต้องเป็น true หรือ false เท่านั้น' })),
    showOnDesktop: t.Optional(t.Boolean({ error: 'showOnDesktop ต้องเป็น true หรือ false เท่านั้น' })),
    startDate: t.Optional(t.Date({ error: 'startDate ต้องเป็นวันที่' })),
    endDate: t.Optional(t.Date({ error: 'endDate ต้องเป็นวันที่' })),
    maxViews: t.Optional(t.Number({ minimum: 1, error: 'maxViews ต้องเป็นตัวเลขและมากกว่า 0' })),
    maxClicks: t.Optional(t.Number({ minimum: 1, error: 'maxClicks ต้องเป็นตัวเลขและมากกว่า 0' })),
    showToVisitors: t.Optional(t.Boolean({ error: 'showToVisitors ต้องเป็น true หรือ false เท่านั้น' })),
    showToCustomers: t.Optional(t.Boolean({ error: 'showToCustomers ต้องเป็น true หรือ false เท่านั้น' })),
    showToAdmins: t.Optional(t.Boolean({ error: 'showToAdmins ต้องเป็น true หรือ false เท่านั้น' })),
  }),
  behaviorSettings: t.Object({
    autoClose: t.Optional(t.Number({ minimum: 1, error: 'autoClose ต้องเป็นตัวเลขและมากกว่า 0' })),
    showDelay: t.Optional(t.Number({ minimum: 0, error: 'showDelay ต้องเป็นตัวเลขและไม่ติดลบ' })),
    showFrequency: t.Optional(t.Union([
      t.Literal('always', { error: 'ต้องเป็น always เท่านั้น' }),
      t.Literal('once-per-session', { error: 'ต้องเป็น once-per-session เท่านั้น' }),
      t.Literal('once-per-day', { error: 'ต้องเป็น once-per-day เท่านั้น' }),
      t.Literal('once-per-week', { error: 'ต้องเป็น once-per-week เท่านั้น' })
    ], { error: 'showFrequency ไม่ถูกต้อง' })),
    animationType: t.Optional(t.Union([
      t.Literal('fade', { error: 'ต้องเป็น fade เท่านั้น' }),
      t.Literal('slide', { error: 'ต้องเป็น slide เท่านั้น' }),
      t.Literal('bounce', { error: 'ต้องเป็น bounce เท่านั้น' }),
      t.Literal('zoom', { error: 'ต้องเป็น zoom เท่านั้น' }),
      t.Literal('none', { error: 'ต้องเป็น none เท่านั้น' })
    ], { error: 'animationType ไม่ถูกต้อง' })),
    animationDuration: t.Optional(t.Number({ minimum: 100, error: 'animationDuration ต้องเป็นตัวเลขและมากกว่าหรือเท่ากับ 100' })),
    closeButton: t.Optional(t.Boolean({ error: 'closeButton ต้องเป็น true หรือ false เท่านั้น' })),
    overlayClose: t.Optional(t.Boolean({ error: 'overlayClose ต้องเป็น true หรือ false เท่านั้น' })),
    escapeClose: t.Optional(t.Boolean({ error: 'escapeClose ต้องเป็น true หรือ false เท่านั้น' })),
  }),
  slideSettings: t.Optional(t.Object({
    slides: t.Array(t.Object({
      id: t.String({ error: 'id ต้องเป็นข้อความ' }),
      imageUrl: t.String({ error: 'imageUrl ต้องเป็นข้อความ' }),
      title: t.Optional(t.String({ error: 'title ต้องเป็นข้อความ' })),
      description: t.Optional(t.String({ error: 'description ต้องเป็นข้อความ' })),
      linkUrl: t.Optional(t.String({ error: 'linkUrl ต้องเป็นข้อความ' })),
      linkTarget: t.Optional(t.Union([
        t.Literal('_self', { error: 'ต้องเป็น _self เท่านั้น' }), 
        t.Literal('_blank', { error: 'ต้องเป็น _blank เท่านั้น' })
      ], { error: 'linkTarget ไม่ถูกต้อง' })),
      order: t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขและไม่ติดลบ' }),
    }), { error: 'slides ต้องเป็น array ของ object' }),
    autoPlay: t.Optional(t.Boolean({ error: 'autoPlay ต้องเป็น true หรือ false เท่านั้น' })),
    autoPlayInterval: t.Optional(t.Number({ minimum: 1, error: 'autoPlayInterval ต้องเป็นตัวเลขและมากกว่า 0' })),
    showDots: t.Optional(t.Boolean({ error: 'showDots ต้องเป็น true หรือ false เท่านั้น' })),
    showArrows: t.Optional(t.Boolean({ error: 'showArrows ต้องเป็น true หรือ false เท่านั้น' })),
    infinite: t.Optional(t.Boolean({ error: 'infinite ต้องเป็น true หรือ false เท่านั้น' })),
    pauseOnHover: t.Optional(t.Boolean({ error: 'pauseOnHover ต้องเป็น true หรือ false เท่านั้น' })),
  })),
  marqueeSettings: t.Optional(t.Object({
    text: t.String({ minLength: 1, error: 'text ต้องมีความยาวอย่างน้อย 1 ตัวอักษร' }),
    speed: t.Optional(t.Number({ minimum: 1, error: 'speed ต้องเป็นตัวเลขและมากกว่า 0' })),
    direction: t.Optional(t.Union([
      t.Literal('left', { error: 'ต้องเป็น left เท่านั้น' }),
      t.Literal('right', { error: 'ต้องเป็น right เท่านั้น' }),
      t.Literal('up', { error: 'ต้องเป็น up เท่านั้น' }),
      t.Literal('down', { error: 'ต้องเป็น down เท่านั้น' })
    ], { error: 'direction ไม่ถูกต้อง' })),
    pauseOnHover: t.Optional(t.Boolean({ error: 'pauseOnHover ต้องเป็น true หรือ false เท่านั้น' })),
    backgroundColor: t.Optional(t.String({ error: 'backgroundColor ต้องเป็นข้อความ' })),
    textColor: t.Optional(t.String({ error: 'textColor ต้องเป็นข้อความ' })),
    fontSize: t.Optional(t.String({ error: 'fontSize ต้องเป็นข้อความ' })),
    height: t.Optional(t.String({ error: 'height ต้องเป็นข้อความ' })),
  })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น true หรือ false เท่านั้น' })),
  priority: t.Optional(t.Number({ minimum: 0, error: 'priority ต้องเป็นตัวเลขและไม่ติดลบ' })),
});

export const updateAdsSchema = t.Object({
  title: t.Optional(t.String({ minLength: 1, maxLength: 200, error: 'title ต้องมีความยาว 1-200 ตัวอักษร' })),
  type: t.Optional(t.Union([
    t.Literal('banner', { error: 'ต้องเป็น banner เท่านั้น' }),
    t.Literal('modal', { error: 'ต้องเป็น modal เท่านั้น' }),
    t.Literal('slide', { error: 'ต้องเป็น slide เท่านั้น' }),
    t.Literal('marquee', { error: 'ต้องเป็น marquee เท่านั้น' }),
    t.Literal('popup', { error: 'ต้องเป็น popup เท่านั้น' }),
    t.Literal('floating', { error: 'ต้องเป็น floating เท่านั้น' })
  ], { error: 'ประเภทโฆษณาไม่ถูกต้อง' })),
  position: t.Optional(t.Union([
    t.Literal('header', { error: 'ต้องเป็น header เท่านั้น' }),
    t.Literal('footer', { error: 'ต้องเป็น footer เท่านั้น' }),
    t.Literal('sidebar', { error: 'ต้องเป็น sidebar เท่านั้น' }),
    t.Literal('content-top', { error: 'ต้องเป็น content-top เท่านั้น' }),
    t.Literal('content-bottom', { error: 'ต้องเป็น content-bottom เท่านั้น' }),
    t.Literal('custom', { error: 'ต้องเป็น custom เท่านั้น' })
  ], { error: 'ตำแหน่งโฆษณาไม่ถูกต้อง' })),
  content: t.Optional(t.Object({
    html: t.Optional(t.String({ error: 'html ต้องเป็นข้อความ' })),
    imageUrl: t.Optional(t.String({ error: 'imageUrl ต้องเป็นข้อความ' })),
    videoUrl: t.Optional(t.String({ error: 'videoUrl ต้องเป็นข้อความ' })),
    text: t.Optional(t.String({ error: 'text ต้องเป็นข้อความ' })),
    linkUrl: t.Optional(t.String({ error: 'linkUrl ต้องเป็นข้อความ' })),
    linkTarget: t.Optional(t.Union([
      t.Literal('_self', { error: 'ต้องเป็น _self เท่านั้น' }), 
      t.Literal('_blank', { error: 'ต้องเป็น _blank เท่านั้น' })
    ], { error: 'linkTarget ไม่ถูกต้อง' })),
    backgroundColor: t.Optional(t.String({ error: 'backgroundColor ต้องเป็นข้อความ' })),
    textColor: t.Optional(t.String({ error: 'textColor ต้องเป็นข้อความ' })),
    fontSize: t.Optional(t.String({ error: 'fontSize ต้องเป็นข้อความ' })),
    fontWeight: t.Optional(t.String({ error: 'fontWeight ต้องเป็นข้อความ' })),
    borderRadius: t.Optional(t.String({ error: 'borderRadius ต้องเป็นข้อความ' })),
    padding: t.Optional(t.String({ error: 'padding ต้องเป็นข้อความ' })),
    margin: t.Optional(t.String({ error: 'margin ต้องเป็นข้อความ' })),
    customCSS: t.Optional(t.String({ error: 'customCSS ต้องเป็นข้อความ' })),
    customJS: t.Optional(t.String({ error: 'customJS ต้องเป็นข้อความ' })),
  })),
  displaySettings: t.Optional(t.Object({
    showOnPages: t.Optional(t.Union([
      t.Literal('all', { error: 'ต้องเป็น all เท่านั้น' }),
      t.Literal('specific', { error: 'ต้องเป็น specific เท่านั้น' }),
      t.Literal('exclude', { error: 'ต้องเป็น exclude เท่านั้น' })
    ], { error: 'showOnPages ไม่ถูกต้อง' })),
    specificPages: t.Optional(t.Array(t.String({ error: 'specificPages ต้องเป็น array ของข้อความ' }), { error: 'specificPages ต้องเป็น array ของข้อความ' })),
    excludePages: t.Optional(t.Array(t.String({ error: 'excludePages ต้องเป็น array ของข้อความ' }), { error: 'excludePages ต้องเป็น array ของข้อความ' })),
    showOnMobile: t.Optional(t.Boolean({ error: 'showOnMobile ต้องเป็น true หรือ false เท่านั้น' })),
    showOnTablet: t.Optional(t.Boolean({ error: 'showOnTablet ต้องเป็น true หรือ false เท่านั้น' })),
    showOnDesktop: t.Optional(t.Boolean({ error: 'showOnDesktop ต้องเป็น true หรือ false เท่านั้น' })),
    startDate: t.Optional(t.Date({ error: 'startDate ต้องเป็นวันที่' })),
    endDate: t.Optional(t.Date({ error: 'endDate ต้องเป็นวันที่' })),
    maxViews: t.Optional(t.Number({ minimum: 1, error: 'maxViews ต้องเป็นตัวเลขและมากกว่า 0' })),
    maxClicks: t.Optional(t.Number({ minimum: 1, error: 'maxClicks ต้องเป็นตัวเลขและมากกว่า 0' })),
    showToVisitors: t.Optional(t.Boolean({ error: 'showToVisitors ต้องเป็น true หรือ false เท่านั้น' })),
    showToCustomers: t.Optional(t.Boolean({ error: 'showToCustomers ต้องเป็น true หรือ false เท่านั้น' })),
    showToAdmins: t.Optional(t.Boolean({ error: 'showToAdmins ต้องเป็น true หรือ false เท่านั้น' })),
  })),
  behaviorSettings: t.Optional(t.Object({
    autoClose: t.Optional(t.Number({ minimum: 1, error: 'autoClose ต้องเป็นตัวเลขและมากกว่า 0' })),
    showDelay: t.Optional(t.Number({ minimum: 0, error: 'showDelay ต้องเป็นตัวเลขและไม่ติดลบ' })),
    showFrequency: t.Optional(t.Union([
      t.Literal('always', { error: 'ต้องเป็น always เท่านั้น' }),
      t.Literal('once-per-session', { error: 'ต้องเป็น once-per-session เท่านั้น' }),
      t.Literal('once-per-day', { error: 'ต้องเป็น once-per-day เท่านั้น' }),
      t.Literal('once-per-week', { error: 'ต้องเป็น once-per-week เท่านั้น' })
    ], { error: 'showFrequency ไม่ถูกต้อง' })),
    animationType: t.Optional(t.Union([
      t.Literal('fade', { error: 'ต้องเป็น fade เท่านั้น' }),
      t.Literal('slide', { error: 'ต้องเป็น slide เท่านั้น' }),
      t.Literal('bounce', { error: 'ต้องเป็น bounce เท่านั้น' }),
      t.Literal('zoom', { error: 'ต้องเป็น zoom เท่านั้น' }),
      t.Literal('none', { error: 'ต้องเป็น none เท่านั้น' })
    ], { error: 'animationType ไม่ถูกต้อง' })),
    animationDuration: t.Optional(t.Number({ minimum: 100, error: 'animationDuration ต้องเป็นตัวเลขและมากกว่าหรือเท่ากับ 100' })),
    closeButton: t.Optional(t.Boolean({ error: 'closeButton ต้องเป็น true หรือ false เท่านั้น' })),
    overlayClose: t.Optional(t.Boolean({ error: 'overlayClose ต้องเป็น true หรือ false เท่านั้น' })),
    escapeClose: t.Optional(t.Boolean({ error: 'escapeClose ต้องเป็น true หรือ false เท่านั้น' })),
  })),
  slideSettings: t.Optional(t.Object({
    slides: t.Optional(t.Array(t.Object({
      id: t.String({ error: 'id ต้องเป็นข้อความ' }),
      imageUrl: t.String({ error: 'imageUrl ต้องเป็นข้อความ' }),
      title: t.Optional(t.String({ error: 'title ต้องเป็นข้อความ' })),
      description: t.Optional(t.String({ error: 'description ต้องเป็นข้อความ' })),
      linkUrl: t.Optional(t.String({ error: 'linkUrl ต้องเป็นข้อความ' })),
      linkTarget: t.Optional(t.Union([
        t.Literal('_self', { error: 'ต้องเป็น _self เท่านั้น' }), 
        t.Literal('_blank', { error: 'ต้องเป็น _blank เท่านั้น' })
      ], { error: 'linkTarget ไม่ถูกต้อง' })),
      order: t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขและไม่ติดลบ' }),
    }), { error: 'slides ต้องเป็น array ของ object' })),
    autoPlay: t.Optional(t.Boolean({ error: 'autoPlay ต้องเป็น true หรือ false เท่านั้น' })),
    autoPlayInterval: t.Optional(t.Number({ minimum: 1, error: 'autoPlayInterval ต้องเป็นตัวเลขและมากกว่า 0' })),
    showDots: t.Optional(t.Boolean({ error: 'showDots ต้องเป็น true หรือ false เท่านั้น' })),
    showArrows: t.Optional(t.Boolean({ error: 'showArrows ต้องเป็น true หรือ false เท่านั้น' })),
    infinite: t.Optional(t.Boolean({ error: 'infinite ต้องเป็น true หรือ false เท่านั้น' })),
    pauseOnHover: t.Optional(t.Boolean({ error: 'pauseOnHover ต้องเป็น true หรือ false เท่านั้น' })),
  })),
  marqueeSettings: t.Optional(t.Object({
    text: t.Optional(t.String({ minLength: 1, error: 'text ต้องมีความยาวอย่างน้อย 1 ตัวอักษร' })),
    speed: t.Optional(t.Number({ minimum: 1, error: 'speed ต้องเป็นตัวเลขและมากกว่า 0' })),
    direction: t.Optional(t.Union([
      t.Literal('left', { error: 'ต้องเป็น left เท่านั้น' }),
      t.Literal('right', { error: 'ต้องเป็น right เท่านั้น' }),
      t.Literal('up', { error: 'ต้องเป็น up เท่านั้น' }),
      t.Literal('down', { error: 'ต้องเป็น down เท่านั้น' })
    ], { error: 'direction ไม่ถูกต้อง' })),
    pauseOnHover: t.Optional(t.Boolean({ error: 'pauseOnHover ต้องเป็น true หรือ false เท่านั้น' })),
    backgroundColor: t.Optional(t.String({ error: 'backgroundColor ต้องเป็นข้อความ' })),
    textColor: t.Optional(t.String({ error: 'textColor ต้องเป็นข้อความ' })),
    fontSize: t.Optional(t.String({ error: 'fontSize ต้องเป็นข้อความ' })),
    height: t.Optional(t.String({ error: 'height ต้องเป็นข้อความ' })),
  })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น true หรือ false เท่านั้น' })),
  priority: t.Optional(t.Number({ minimum: 0, error: 'priority ต้องเป็นตัวเลขและไม่ติดลบ' })),
});

export const adsQuerySchema = t.Object({
  type: t.Optional(t.Union([
    t.Literal('banner', { error: 'ต้องเป็น banner เท่านั้น' }),
    t.Literal('modal', { error: 'ต้องเป็น modal เท่านั้น' }),
    t.Literal('slide', { error: 'ต้องเป็น slide เท่านั้น' }),
    t.Literal('marquee', { error: 'ต้องเป็น marquee เท่านั้น' }),
    t.Literal('popup', { error: 'ต้องเป็น popup เท่านั้น' }),
    t.Literal('floating', { error: 'ต้องเป็น floating เท่านั้น' })
  ], { error: 'ประเภทโฆษณาไม่ถูกต้อง' })),
  position: t.Optional(t.Union([
    t.Literal('header', { error: 'ต้องเป็น header เท่านั้น' }),
    t.Literal('footer', { error: 'ต้องเป็น footer เท่านั้น' }),
    t.Literal('sidebar', { error: 'ต้องเป็น sidebar เท่านั้น' }),
    t.Literal('content-top', { error: 'ต้องเป็น content-top เท่านั้น' }),
    t.Literal('content-bottom', { error: 'ต้องเป็น content-bottom เท่านั้น' }),
    t.Literal('custom', { error: 'ต้องเป็น custom เท่านั้น' })
  ], { error: 'ตำแหน่งโฆษณาไม่ถูกต้อง' })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น true หรือ false เท่านั้น' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' })),
});

export const trackAdsSchema = t.Object({
  action: t.Union([
    t.Literal('view', { error: 'ต้องเป็น view เท่านั้น' }), 
    t.Literal('click', { error: 'ต้องเป็น click เท่านั้น' })
  ], { error: 'action ไม่ถูกต้อง' }),
  userAgent: t.Optional(t.String({ error: 'userAgent ต้องเป็นข้อความ' })),
  ipAddress: t.Optional(t.String({ error: 'ipAddress ต้องเป็นข้อความ' })),
});

export const getAdsForPageSchema = t.Object({
  pageSlug: t.String({ error: 'pageSlug ต้องเป็นข้อความ' }),
  userType: t.Optional(t.Union([
    t.Literal('visitor', { error: 'ต้องเป็น visitor เท่านั้น' }),
    t.Literal('customer', { error: 'ต้องเป็น customer เท่านั้น' }),
    t.Literal('admin', { error: 'ต้องเป็น admin เท่านั้น' })
  ], { error: 'userType ไม่ถูกต้อง' })),
  deviceType: t.Optional(t.Union([
    t.Literal('mobile', { error: 'ต้องเป็น mobile เท่านั้น' }),
    t.Literal('tablet', { error: 'ต้องเป็น tablet เท่านั้น' }),
    t.Literal('desktop', { error: 'ต้องเป็น desktop เท่านั้น' })
  ], { error: 'deviceType ไม่ถูกต้อง' })),
});