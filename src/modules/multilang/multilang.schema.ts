import { t } from 'elysia';

// Multi-language Schemas
export const LanguageSchema = t.Object({
  code: t.String({
    error: 'รหัสภาษาต้องเป็นสตริง'
  }),
  name: t.String({
    error: 'ชื่อภาษาต้องเป็นสตริง'
  }),
  nativeName: t.String({
    error: 'ชื่อภาษาตัวเองต้องเป็นสตริง'
  }),
  isActive: t.Optional(t.<PERSON>({
    error: 'สถานะการเปิดใช้งานต้องเป็นบูลีน'
  })),
  isDefault: t.<PERSON>tional(t.<PERSON>({
    error: 'สถานะค่าเริ่มต้นต้องเป็นบูลีน'
  })),
  direction: t.<PERSON>tional(t.Union([
    t.Literal('ltr', {
      error: 'ทิศทางต้องเป็น "ltr" หรือ "rtl"'
    }),
    t.Literal('rtl', {
      error: 'ทิศทางต้องเป็น "ltr" หรือ "rtl"'
    })
  ])),
  flag: t.Optional(t.String({
    error: 'รูปภาพต้องเป็นสตริง'
  })),
  settings: t.Optional(t.Object({
    dateFormat: t.String({
      error: 'รูปแบบวันที่ต้องเป็นสตริง'
    }),
    timeFormat: t.String({
      error: 'รูปแบบเวลาต้องเป็นสตริง'
    }),
    currency: t.String({
      error: 'สกุลเงินต้องเป็นสตริง'
    }),
    currencySymbol: t.String({
      error: 'สัญลักษณ์สกุลเงินต้องเป็นสตริง'
    }),
    currencyPosition: t.Union([
      t.Literal('before', {
        error: 'ตำแหน่งสกุลเงินต้องเป็น "before" หรือ "after"'
      }),
      t.Literal('after', {
        error: 'ตำแหน่งสกุลเงินต้องเป็น "before" หรือ "after"'
      })
    ]),
    decimalSeparator: t.String({
      error: 'ตัวคั่นทศนิยมต้องเป็นสตริง'
    }),
    thousandSeparator: t.String({
      error: 'ตัวคั่นหมื่นต้องเป็นสตริง'
    }),
    timezone: t.String({
      error: 'เขตเวลาต้องเป็นสตริง'
    })
  }))
});

export const LanguageResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    code: t.String(),
    name: t.String(),
    nativeName: t.String(),
    isActive: t.Boolean(),
    isDefault: t.Boolean(),
    direction: t.String(),
    flag: t.Optional(t.String()),
    settings: t.Object({
      dateFormat: t.String(),
      timeFormat: t.String(),
      currency: t.String(),
      currencySymbol: t.String(),
      currencyPosition: t.String(),
      decimalSeparator: t.String(),
      thousandSeparator: t.String(),
      timezone: t.String()
    }),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const TranslationSchema = t.Object({
  languageCode: t.String({
    error: 'รหัสภาษาต้องเป็นสตริง'
  }),
  namespace: t.String({
    error: 'ชื่อพื้นที่ต้องเป็นสตริง'
  }),
  key: t.String({
    error: 'คีย์ต้องเป็นสตริง'
  }),
  value: t.String({
    error: 'ค่าต้องเป็นสตริง'
  }),
  context: t.Optional(t.String({
    error: 'บริบทต้องเป็นสตริง'
  }))
});

export const TranslationResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    languageCode: t.String(),
    namespace: t.String(),
    key: t.String(),
    value: t.String(),
    context: t.Optional(t.String()),
    isActive: t.Boolean(),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const CurrencySchema = t.Object({
  code: t.String({
    error: 'สกุลเงินต้องเป็นสตริง'
  }),
  name: t.String({
    error: 'ชื่อสกุลเงินต้องเป็นสตริง'
  }),
  symbol: t.String({
    error: 'สัญลักษณ์สกุลเงินต้องเป็นสตริง'
  }),
  rate: t.Number({
    error: 'อัตราแลกเปลี่ยนต้องเป็นตัวเลข'
  }),
  isBase: t.Optional(t.Boolean({
    error: 'สถานะสกุลเงินฐานต้องเป็นบูลีน'
  })),
  isActive: t.Optional(t.Boolean({
    error: 'สถานะการเปิดใช้งานสกุลเงินต้องเป็นบูลีน'
  })),
  precision: t.Optional(t.Number({
    error: 'ความแม่นยำต้องเป็นตัวเลข'
  })),
  position: t.Optional(t.Union([
    t.Literal('before', {
      error: 'ตำแหน่งสกุลเงินต้องเป็น "before" หรือ "after"'
    }),
    t.Literal('after', {
      error: 'ตำแหน่งสกุลเงินต้องเป็น "before" หรือ "after"'
    })
  ]))
});

export const CurrencyResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    code: t.String(),
    name: t.String(),
    symbol: t.String(),
    rate: t.Number(),
    isBase: t.Boolean(),
    isActive: t.Boolean(),
    precision: t.Number(),
    position: t.String(),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const TimezoneSchema = t.Object({
  name: t.String({
    error: 'ชื่อเขตเวลาต้องเป็นสตริง'
  }),
  offset: t.String({
    error: 'ครึ่งนาทีต้องเป็นสตริง'
  }),
  abbreviation: t.String({
    error: 'ตัวย่อเขตเวลาต้องเป็นสตริง'
  }),
  isActive: t.Optional(t.Boolean({
    error: 'สถานะการเปิดใช้งานเขตเวลาต้องเป็นบูลีน'
  })),
  isDefault: t.Optional(t.Boolean({
    error: 'สถานะค่าเริ่มต้นเขตเวลาต้องเป็นบูลีน'
  }))
});

export const TimezoneResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    name: t.String(),
    offset: t.String(),
    abbreviation: t.String(),
    isActive: t.Boolean(),
    isDefault: t.Boolean(),
    createdAt: t.String(),
    updatedAt: t.String()
  })
});

export const LanguageListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(t.Object({
    _id: t.String(),
    siteId: t.String(),
    code: t.String(),
    name: t.String(),
    nativeName: t.String(),
    isActive: t.Boolean(),
    isDefault: t.Boolean(),
    direction: t.String(),
    flag: t.Optional(t.String()),
    settings: t.Object({
      dateFormat: t.String(),
      timeFormat: t.String(),
      currency: t.String(),
      currencySymbol: t.String(),
      currencyPosition: t.String(),
      decimalSeparator: t.String(),
      thousandSeparator: t.String(),
      timezone: t.String()
    }),
    createdAt: t.String(),
    updatedAt: t.String()
  }))
});

export const CurrencyListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(t.Object({
    _id: t.String(),
    siteId: t.String(),
    code: t.String(),
    name: t.String(),
    symbol: t.String(),
    rate: t.Number(),
    isBase: t.Boolean(),
    isActive: t.Boolean(),
    precision: t.Number(),
    position: t.String(),
    createdAt: t.String(),
    updatedAt: t.String()
  }))
});

export const TimezoneListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(t.Object({
    _id: t.String(),
    siteId: t.String(),
    name: t.String(),
    offset: t.String(),
    abbreviation: t.String(),
    isActive: t.Boolean(),
    isDefault: t.Boolean(),
    createdAt: t.String(),
    updatedAt: t.String()
  }))
});

export const TranslationListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(t.Object({
    _id: t.String(),
    siteId: t.String(),
    languageCode: t.String(),
    namespace: t.String(),
    key: t.String(),
    value: t.String(),
    context: t.Optional(t.String()),
    isActive: t.Boolean(),
    createdAt: t.String(),
    updatedAt: t.String()
  }))
});

export const CurrencyConversionSchema = t.Object({
  amount: t.Number({
    error: 'จำนวนเงินต้องเป็นตัวเลข'
  }),
  fromCurrency: t.String({
    error: 'สกุลเงินต้องเป็นสตริง'
  }),
  toCurrency: t.String({
    error: 'สกุลเงินต้องเป็นสตริง'
  })
});

export const CurrencyConversionResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    originalAmount: t.Number(),
    originalCurrency: t.String(),
    convertedAmount: t.Number(),
    convertedCurrency: t.String(),
    rate: t.Number()
  })
});

export const LocalizationSettingsResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    languages: t.Array(t.Object({
      _id: t.String(),
      siteId: t.String(),
      code: t.String(),
      name: t.String(),
      nativeName: t.String(),
      isActive: t.Boolean(),
      isDefault: t.Boolean(),
      direction: t.String(),
      flag: t.Optional(t.String()),
      settings: t.Object({
        dateFormat: t.String(),
        timeFormat: t.String(),
        currency: t.String(),
        currencySymbol: t.String(),
        currencyPosition: t.String(),
        decimalSeparator: t.String(),
        thousandSeparator: t.String(),
        timezone: t.String()
      }),
      createdAt: t.String(),
      updatedAt: t.String()
    })),
    currencies: t.Array(t.Object({
      _id: t.String(),
      siteId: t.String(),
      code: t.String(),
      name: t.String(),
      symbol: t.String(),
      rate: t.Number(),
      isBase: t.Boolean(),
      isActive: t.Boolean(),
      precision: t.Number(),
      position: t.String(),
      createdAt: t.String(),
      updatedAt: t.String()
    })),
    timezones: t.Array(t.Object({
      _id: t.String(),
      siteId: t.String(),
      name: t.String(),
      offset: t.String(),
      abbreviation: t.String(),
      isActive: t.Boolean(),
      isDefault: t.Boolean(),
      createdAt: t.String(),
      updatedAt: t.String()
    }))
  })
}); 