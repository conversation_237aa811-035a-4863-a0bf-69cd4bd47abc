import { t } from 'elysia';

// Loyalty Schemas
export const LoyaltyAccountResponseSchema = t.Object({
  success: t.<PERSON>({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
    userId: t.String({ error: 'userId ต้องเป็นข้อความ' }),
    userType: t.String({ error: 'userType ต้องเป็นข้อความ' }),
    tier: t.String({ error: 'tier ต้องเป็นข้อความ' }),
    points: t.Number({ error: 'points ต้องเป็นตัวเลข' }),
    lifetimePoints: t.Number({ error: 'lifetimePoints ต้องเป็นตัวเลข' }),
    tierPoints: t.Number({ error: 'tierPoints ต้องเป็นตัวเลข' }),
    nextTierPoints: t.Number({ error: 'nextTierPoints ต้องเป็นตัวเลข' }),
    settings: t.Object({
      autoUpgrade: t.<PERSON>({ error: 'autoUpgrade ต้องเป็น true หรือ false เท่านั้น' }),
      allowPointExpiry: t.<PERSON>({ error: 'allowPointExpiry ต้องเป็น true หรือ false เท่านั้น' }),
      pointExpiryDays: t.Number({ error: 'pointExpiryDays ต้องเป็นตัวเลข' })
    }),
    stats: t.Object({
      totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
      totalSpent: t.Number({ error: 'totalSpent ต้องเป็นตัวเลข' }),
      averageOrderValue: t.Number({ error: 'averageOrderValue ต้องเป็นตัวเลข' }),
      lastOrderDate: t.Optional(t.String({ error: 'lastOrderDate ต้องเป็นข้อความ' })),
      customerSince: t.String({ error: 'customerSince ต้องเป็นข้อความ' })
    }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
  })
});

export const PointsTransactionSchema = t.Object({
  type: t.Union([
    t.Literal('earn', { error: 'ต้องเป็น earn เท่านั้น' }),
    t.Literal('spend', { error: 'ต้องเป็น spend เท่านั้น' }),
    t.Literal('expire', { error: 'ต้องเป็น expire เท่านั้น' }),
    t.Literal('adjust', { error: 'ต้องเป็น adjust เท่านั้น' }),
    t.Literal('bonus', { error: 'ต้องเป็น bonus เท่านั้น' })
  ], { error: 'type ไม่ถูกต้อง' }),
  amount: t.Number({ error: 'amount ต้องเป็นตัวเลข' }),
  source: t.Union([
    t.Literal('purchase', { error: 'ต้องเป็น purchase เท่านั้น' }),
    t.Literal('referral', { error: 'ต้องเป็น referral เท่านั้น' }),
    t.Literal('birthday', { error: 'ต้องเป็น birthday เท่านั้น' }),
    t.Literal('review', { error: 'ต้องเป็น review เท่านั้น' }),
    t.Literal('social', { error: 'ต้องเป็น social เท่านั้น' }),
    t.Literal('manual', { error: 'ต้องเป็น manual เท่านั้น' }),
    t.Literal('expiry', { error: 'ต้องเป็น expiry เท่านั้น' })
  ], { error: 'source ไม่ถูกต้อง' }),
  description: t.String({ error: 'description ต้องเป็นข้อความ' }),
  orderId: t.Optional(t.String({ error: 'orderId ต้องเป็นข้อความ' })),
  productId: t.Optional(t.String({ error: 'productId ต้องเป็นข้อความ' })),
  expiresAt: t.Optional(t.String({ error: 'expiresAt ต้องเป็นข้อความ' }))
});

export const PointsTransactionResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
    userId: t.String({ error: 'userId ต้องเป็นข้อความ' }),
    type: t.String({ error: 'type ต้องเป็นข้อความ' }),
    amount: t.Number({ error: 'amount ต้องเป็นตัวเลข' }),
    balance: t.Number({ error: 'balance ต้องเป็นตัวเลข' }),
    source: t.String({ error: 'source ต้องเป็นข้อความ' }),
    orderId: t.Optional(t.String({ error: 'orderId ต้องเป็นข้อความ' })),
    productId: t.Optional(t.String({ error: 'productId ต้องเป็นข้อความ' })),
    description: t.String({ error: 'description ต้องเป็นข้อความ' }),
    expiresAt: t.Optional(t.String({ error: 'expiresAt ต้องเป็นข้อความ' })),
    isExpired: t.Boolean({ error: 'isExpired ต้องเป็น true หรือ false เท่านั้น' }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
  })
});

export const PointsHistoryResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    transactions: t.Array(t.Object({
      _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
      siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
      userId: t.String({ error: 'userId ต้องเป็นข้อความ' }),
      type: t.String({ error: 'type ต้องเป็นข้อความ' }),
      amount: t.Number({ error: 'amount ต้องเป็นตัวเลข' }),
      balance: t.Number({ error: 'balance ต้องเป็นตัวเลข' }),
      source: t.String({ error: 'source ต้องเป็นข้อความ' }),
      orderId: t.Optional(t.String({ error: 'orderId ต้องเป็นข้อความ' })),
      productId: t.Optional(t.String({ error: 'productId ต้องเป็นข้อความ' })),
      description: t.String({ error: 'description ต้องเป็นข้อความ' }),
      expiresAt: t.Optional(t.String({ error: 'expiresAt ต้องเป็นข้อความ' })),
      isExpired: t.Boolean({ error: 'isExpired ต้องเป็น true หรือ false เท่านั้น' }),
      createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
      updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
    }), { error: 'transactions ต้องเป็น array ของ object' }),
    pagination: t.Object({
      page: t.Number({ error: 'page ต้องเป็นตัวเลข' }),
      limit: t.Number({ error: 'limit ต้องเป็นตัวเลข' }),
      total: t.Number({ error: 'total ต้องเป็นตัวเลข' }),
      pages: t.Number({ error: 'pages ต้องเป็นตัวเลข' })
    })
  })
});

export const LoyaltyRewardSchema = t.Object({
  name: t.String({ error: 'name ต้องเป็นข้อความ' }),
  description: t.String({ error: 'description ต้องเป็นข้อความ' }),
  type: t.Union([
    t.Literal('discount', { error: 'ต้องเป็น discount เท่านั้น' }),
    t.Literal('free_shipping', { error: 'ต้องเป็น free_shipping เท่านั้น' }),
    t.Literal('free_product', { error: 'ต้องเป็น free_product เท่านั้น' }),
    t.Literal('cashback', { error: 'ต้องเป็น cashback เท่านั้น' }),
    t.Literal('voucher', { error: 'ต้องเป็น voucher เท่านั้น' })
  ], { error: 'type ไม่ถูกต้อง' }),
  pointsRequired: t.Number({ error: 'pointsRequired ต้องเป็นตัวเลข' }),
  value: t.Number({ error: 'value ต้องเป็นตัวเลข' }),
  maxUses: t.Number({ error: 'maxUses ต้องเป็นตัวเลข' }),
  minOrderAmount: t.Optional(t.Number({ error: 'minOrderAmount ต้องเป็นตัวเลข' })),
  applicableProducts: t.Optional(t.Array(t.String({ error: 'applicableProducts ต้องเป็นข้อความ' }), { error: 'applicableProducts ต้องเป็น array ของข้อความ' })),
  applicableCategories: t.Optional(t.Array(t.String({ error: 'applicableCategories ต้องเป็นข้อความ' }), { error: 'applicableCategories ต้องเป็น array ของข้อความ' })),
  startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
  endDate: t.Optional(t.String({ error: 'endDate ต้องเป็นข้อความ' }))
});

export const LoyaltyRewardResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
    name: t.String({ error: 'name ต้องเป็นข้อความ' }),
    description: t.String({ error: 'description ต้องเป็นข้อความ' }),
    type: t.String({ error: 'type ต้องเป็นข้อความ' }),
    pointsRequired: t.Number({ error: 'pointsRequired ต้องเป็นตัวเลข' }),
    value: t.Number({ error: 'value ต้องเป็นตัวเลข' }),
    maxUses: t.Number({ error: 'maxUses ต้องเป็นตัวเลข' }),
    currentUses: t.Number({ error: 'currentUses ต้องเป็นตัวเลข' }),
    minOrderAmount: t.Optional(t.Number({ error: 'minOrderAmount ต้องเป็นตัวเลข' })),
    applicableProducts: t.Optional(t.Array(t.String({ error: 'applicableProducts ต้องเป็นข้อความ' }), { error: 'applicableProducts ต้องเป็น array ของข้อความ' })),
    applicableCategories: t.Optional(t.Array(t.String({ error: 'applicableCategories ต้องเป็นข้อความ' }), { error: 'applicableCategories ต้องเป็น array ของข้อความ' })),
    startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
    endDate: t.Optional(t.String({ error: 'endDate ต้องเป็นข้อความ' })),
    isActive: t.Boolean({ error: 'isActive ต้องเป็น true หรือ false เท่านั้น' }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
  })
});

export const LoyaltyTierSchema = t.Object({
  name: t.String({ error: 'name ต้องเป็นข้อความ' }),
  tier: t.Union([
    t.Literal('bronze', { error: 'ต้องเป็น bronze เท่านั้น' }),
    t.Literal('silver', { error: 'ต้องเป็น silver เท่านั้น' }),
    t.Literal('gold', { error: 'ต้องเป็น gold เท่านั้น' }),
    t.Literal('platinum', { error: 'ต้องเป็น platinum เท่านั้น' }),
    t.Literal('diamond', { error: 'ต้องเป็น diamond เท่านั้น' })
  ], { error: 'tier ไม่ถูกต้อง' }),
  pointsRequired: t.Number({ error: 'pointsRequired ต้องเป็นตัวเลข' }),
  benefits: t.Object({
    pointMultiplier: t.Number({ error: 'pointMultiplier ต้องเป็นตัวเลข' }),
    discountPercentage: t.Number({ error: 'discountPercentage ต้องเป็นตัวเลข' }),
    freeShipping: t.Boolean({ error: 'freeShipping ต้องเป็น true หรือ false เท่านั้น' }),
    prioritySupport: t.Boolean({ error: 'prioritySupport ต้องเป็น true หรือ false เท่านั้น' }),
    exclusiveAccess: t.Boolean({ error: 'exclusiveAccess ต้องเป็น true หรือ false เท่านั้น' }),
    birthdayBonus: t.Number({ error: 'birthdayBonus ต้องเป็นตัวเลข' }),
    referralBonus: t.Number({ error: 'referralBonus ต้องเป็นตัวเลข' })
  })
});

export const LoyaltyTierResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
    name: t.String({ error: 'name ต้องเป็นข้อความ' }),
    tier: t.String({ error: 'tier ต้องเป็นข้อความ' }),
    pointsRequired: t.Number({ error: 'pointsRequired ต้องเป็นตัวเลข' }),
    benefits: t.Object({
      pointMultiplier: t.Number({ error: 'pointMultiplier ต้องเป็นตัวเลข' }),
      discountPercentage: t.Number({ error: 'discountPercentage ต้องเป็นตัวเลข' }),
      freeShipping: t.Boolean({ error: 'freeShipping ต้องเป็น true หรือ false เท่านั้น' }),
      prioritySupport: t.Boolean({ error: 'prioritySupport ต้องเป็น true หรือ false เท่านั้น' }),
      exclusiveAccess: t.Boolean({ error: 'exclusiveAccess ต้องเป็น true หรือ false เท่านั้น' }),
      birthdayBonus: t.Number({ error: 'birthdayBonus ต้องเป็นตัวเลข' }),
      referralBonus: t.Number({ error: 'referralBonus ต้องเป็นตัวเลข' })
    }),
    isActive: t.Boolean({ error: 'isActive ต้องเป็น true หรือ false เท่านั้น' }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
  })
});

export const LoyaltyStatsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Object({
    currentPoints: t.Number({ error: 'currentPoints ต้องเป็นตัวเลข' }),
    lifetimePoints: t.Number({ error: 'lifetimePoints ต้องเป็นตัวเลข' }),
    tier: t.String({ error: 'tier ต้องเป็นข้อความ' }),
    tierPoints: t.Number({ error: 'tierPoints ต้องเป็นตัวเลข' }),
    nextTierPoints: t.Number({ error: 'nextTierPoints ต้องเป็นตัวเลข' }),
    totalTransactions: t.Number({ error: 'totalTransactions ต้องเป็นตัวเลข' }),
    totalEarned: t.Number({ error: 'totalEarned ต้องเป็นตัวเลข' }),
    totalSpent: t.Number({ error: 'totalSpent ต้องเป็นตัวเลข' })
  })
});

export const TopCustomersResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  data: t.Array(t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความ' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
    userId: t.String({ error: 'userId ต้องเป็นข้อความ' }),
    userType: t.String({ error: 'userType ต้องเป็นข้อความ' }),
    tier: t.String({ error: 'tier ต้องเป็นข้อความ' }),
    points: t.Number({ error: 'points ต้องเป็นตัวเลข' }),
    lifetimePoints: t.Number({ error: 'lifetimePoints ต้องเป็นตัวเลข' }),
    tierPoints: t.Number({ error: 'tierPoints ต้องเป็นตัวเลข' }),
    nextTierPoints: t.Number({ error: 'nextTierPoints ต้องเป็นตัวเลข' }),
    stats: t.Object({
      totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
      totalSpent: t.Number({ error: 'totalSpent ต้องเป็นตัวเลข' }),
      averageOrderValue: t.Number({ error: 'averageOrderValue ต้องเป็นตัวเลข' }),
      lastOrderDate: t.Optional(t.String({ error: 'lastOrderDate ต้องเป็นข้อความ' })),
      customerSince: t.String({ error: 'customerSince ต้องเป็นข้อความ' })
    }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความ' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความ' })
  }), { error: 'data ต้องเป็น array ของ object' })
});

export const RedeemRewardSchema = t.Object({
  rewardId: t.String({ error: 'rewardId ต้องเป็นข้อความ' })
});

export const EarnPointsSchema = t.Object({
  amount: t.Number({ error: 'amount ต้องเป็นตัวเลข' }),
  source: t.Union([
    t.Literal('purchase', { error: 'ต้องเป็น purchase เท่านั้น' }),
    t.Literal('referral', { error: 'ต้องเป็น referral เท่านั้น' }),
    t.Literal('birthday', { error: 'ต้องเป็น birthday เท่านั้น' }),
    t.Literal('review', { error: 'ต้องเป็น review เท่านั้น' }),
    t.Literal('social', { error: 'ต้องเป็น social เท่านั้น' }),
    t.Literal('manual', { error: 'ต้องเป็น manual เท่านั้น' }),
    t.Literal('expiry', { error: 'ต้องเป็น expiry เท่านั้น' })
  ], { error: 'source ไม่ถูกต้อง' }),
  description: t.String({ error: 'description ต้องเป็นข้อความ' }),
  orderId: t.Optional(t.String({ error: 'orderId ต้องเป็นข้อความ' })),
  productId: t.Optional(t.String({ error: 'productId ต้องเป็นข้อความ' }))
});

export const SpendPointsSchema = t.Object({
  amount: t.Number({ error: 'amount ต้องเป็นตัวเลข' }),
  purpose: t.String({ error: 'purpose ต้องเป็นข้อความ' }),
  orderId: t.Optional(t.String({ error: 'orderId ต้องเป็นข้อความ' }))
}); 