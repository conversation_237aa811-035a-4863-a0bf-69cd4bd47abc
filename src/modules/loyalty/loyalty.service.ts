import { LoyaltyAccount, PointsTransaction, LoyaltyReward, LoyaltyTier, ILoyaltyAccount, IPointsTransaction, ILoyaltyReward, ILoyaltyTier } from './loyalty.model';
import { User } from '@/modules/user/user.model';
import { Customer } from '@/modules/customer/customer.model';
import { HttpError } from '@/core/utils/error';

// Loyalty Service
export async function createLoyaltyAccount(siteId: string, userId: string, userType: string = 'user') {
  try {
    // ตรวจสอบว่ามีบัญชีอยู่แล้วหรือไม่
    const existingAccount = await (LoyaltyAccount as any).findByUser(siteId, userId);

    if (existingAccount) {
      throw new HttpError(400, 'บัญชี loyalty มีอยู่แล้ว');
    }

    // ดึง tier เริ่มต้น
    const defaultTier = await (LoyaltyTier as any).findByTier(siteId, 'bronze');

    const account = await LoyaltyAccount.create({
      siteId,
      userId,
      userType,
      tier: defaultTier?.tier || 'bronze',
      points: 0,
      lifetimePoints: 0,
      tierPoints: 0,
      nextTierPoints: defaultTier?.pointsRequired || 100
    });

    return account;
  } catch (err: any) {
    console.error('Error in createLoyaltyAccount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างบัญชี loyalty');
  }
}

export async function getLoyaltyAccount(siteId: string, userId: string) {
  try {
    let account = await (LoyaltyAccount as any).findByUser(siteId, userId);

    if (!account) {
      // สร้างบัญชีใหม่
      account = await createLoyaltyAccount(siteId, userId);
    }

    return account;
  } catch (err: any) {
    console.error('Error in getLoyaltyAccount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงบัญชี loyalty');
  }
}

export async function earnPoints(siteId: string, userId: string, amount: number, source: string, metadata: any = {}) {
  try {
    const account = await getLoyaltyAccount(siteId, userId);

    // คำนวณ points ตาม tier
    const tier = await (LoyaltyTier as any).findByTier(siteId, account.tier);
    const multiplier = tier?.benefits?.pointMultiplier || 1;
    const earnedPoints = Math.floor(amount * multiplier);

    // อัปเดตบัญชี
    const updatedAccount = await LoyaltyAccount.findOneAndUpdate(
      { siteId, userId },
      {
        $inc: {
          points: earnedPoints,
          lifetimePoints: earnedPoints,
          tierPoints: earnedPoints
        }
      },
      { new: true }
    );

    // สร้าง transaction
    const transaction = await PointsTransaction.create({
      siteId,
      userId,
      type: 'earn',
      amount: earnedPoints,
      balance: updatedAccount.points,
      source,
      description: `ได้รับ ${earnedPoints} points จาก ${source}`,
      orderId: metadata.orderId,
      productId: metadata.productId
    });

    // ตรวจสอบการเลื่อน tier
    await checkTierUpgrade(siteId, userId);

    return {
      account: updatedAccount,
      transaction
    };
  } catch (err: any) {
    console.error('Error in earnPoints:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะรับ points');
  }
}

export async function spendPoints(siteId: string, userId: string, amount: number, purpose: string, metadata: any = {}) {
  try {
    const account = await getLoyaltyAccount(siteId, userId);

    if (account.points < amount) {
      throw new HttpError(400, 'points ไม่เพียงพอ');
    }

    // อัปเดตบัญชี
    const updatedAccount = await LoyaltyAccount.findOneAndUpdate(
      { siteId, userId },
      {
        $inc: { points: -amount }
      },
      { new: true }
    );

    // สร้าง transaction
    const transaction = await PointsTransaction.create({
      siteId,
      userId,
      type: 'spend',
      amount: -amount,
      balance: updatedAccount.points,
      source: 'redemption',
      description: `ใช้ ${amount} points สำหรับ ${purpose}`,
      orderId: metadata.orderId
    });

    return {
      account: updatedAccount,
      transaction
    };
  } catch (err: any) {
    console.error('Error in spendPoints:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะใช้ points');
  }
}

export async function getPointsHistory(siteId: string, userId: string, page: number = 1, limit: number = 20) {
  try {
    const skip = (page - 1) * limit;
    const transactions = await (PointsTransaction as any).findByUser(siteId, userId)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await PointsTransaction.countDocuments({ siteId, userId });

    return {
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (err: any) {
    console.error('Error in getPointsHistory:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงประวัติ points');
  }
}

export async function getAvailableRewards(siteId: string, userId: string) {
  try {
    const account = await getLoyaltyAccount(siteId, userId);
    const rewards = await (LoyaltyReward as any).findAvailable(siteId, account.points);

    return rewards;
  } catch (err: any) {
    console.error('Error in getAvailableRewards:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง rewards');
  }
}

export async function redeemReward(siteId: string, userId: string, rewardId: string) {
  try {
    const reward = await LoyaltyReward.findOne({ siteId, _id: rewardId, isActive: true });

    if (!reward) {
      throw new HttpError(404, 'ไม่พบ reward');
    }

    const account = await getLoyaltyAccount(siteId, userId);

    if (account.points < reward.pointsRequired) {
      throw new HttpError(400, 'points ไม่เพียงพอ');
    }

    // ใช้ points
    const result = await spendPoints(siteId, userId, reward.pointsRequired, `แลก ${reward.name}`);

    return {
      reward,
      account: result.account,
      transaction: result.transaction
    };
  } catch (err: any) {
    console.error('Error in redeemReward:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะแลก reward');
  }
}

export async function getLoyaltyTiers(siteId: string) {
  try {
    const tiers = await LoyaltyTier.find({ siteId, isActive: true }).sort({ 'benefits.pointMultiplier': 1 });
    return tiers;
  } catch (err: any) {
    console.error('Error in getLoyaltyTiers:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง tiers');
  }
}

export async function getTopCustomers(siteId: string, limit: number = 10) {
  try {
    const customers = await (LoyaltyAccount as any).getTopCustomers(siteId, limit);
    return customers;
  } catch (err: any) {
    console.error('Error in getTopCustomers:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสมาชิกยอดนิยม');
  }
}

async function checkTierUpgrade(siteId: string, userId: string) {
  try {
    const account = await getLoyaltyAccount(siteId, userId);
    const tiers = await getLoyaltyTiers(siteId);

    // หา tier ถัดไป
    const currentTierIndex = tiers.findIndex(tier => tier.tier === account.tier);
    const nextTier = tiers[currentTierIndex + 1];

    if (nextTier && account.tierPoints >= nextTier.pointsRequired) {
      // เลื่อน tier
      await LoyaltyAccount.findOneAndUpdate(
        { siteId, userId },
        {
          $set: {
            tier: nextTier.tier,
            tierPoints: account.tierPoints - nextTier.pointsRequired,
            nextTierPoints: tiers[currentTierIndex + 2]?.pointsRequired || 0
          }
        }
      );
    }
  } catch (err: any) {
    console.error('Error in checkTierUpgrade:', err);
  }
}

export async function createLoyaltyReward(siteId: string, rewardData: any) {
  try {
    const reward = await LoyaltyReward.create({
      siteId,
      ...rewardData
    });

    return reward;
  } catch (err: any) {
    console.error('Error in createLoyaltyReward:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง reward');
  }
}

export async function getLoyaltyRewards(siteId: string) {
  try {
    const rewards = await LoyaltyReward.find({ siteId, isActive: true });
    return rewards;
  } catch (err: any) {
    console.error('Error in getLoyaltyRewards:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง rewards');
  }
}

export async function createLoyaltyTier(siteId: string, tierData: any) {
  try {
    const tier = await LoyaltyTier.create({
      siteId,
      ...tierData
    });

    return tier;
  } catch (err: any) {
    console.error('Error in createLoyaltyTier:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง tier');
  }
}

export async function getLoyaltyStats(siteId: string, userId: string) {
  try {
    const account = await getLoyaltyAccount(siteId, userId);
    const transactions = await (PointsTransaction as any).findByUser(siteId, userId);

    const stats = {
      currentPoints: account.points,
      lifetimePoints: account.lifetimePoints,
      tier: account.tier,
      tierPoints: account.tierPoints,
      nextTierPoints: account.nextTierPoints,
      totalTransactions: transactions.length,
      totalEarned: transactions
        .filter(t => t.type === 'earn')
        .reduce((sum, t) => sum + t.amount, 0),
      totalSpent: transactions
        .filter(t => t.type === 'spend')
        .reduce((sum, t) => sum + Math.abs(t.amount), 0)
    };

    return stats;
  } catch (err: any) {
    console.error('Error in getLoyaltyStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ loyalty');
  }
} 