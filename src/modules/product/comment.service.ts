import { Comment, IComment, type CommentType, CommentStatus } from './comment.model';
import { Product } from '../product/product.model';
import { Order } from '../order/order.model';
import { HttpError } from '@/core/utils/error';

// Comment Service
export async function createComment(commentData: {
  siteId: string;
  type: CommentType;
  targetId: string;
  parentId?: string;
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;
  content: string;
  images?: string[];
}) {
  const { siteId, type, targetId, parentId, userId, userName, userEmail, userAvatar, content, images } = commentData;
  if (type === 'product') {
    const product = await Product.findById(targetId);
    if (!product) throw new HttpError(404, 'ไม่พบสินค้า');
  }
  if (parentId) {
    const parentComment = await Comment.findById(parentId);
    if (!parentComment) throw new HttpError(404, 'ไม่พบคอมเมนต์ต้นฉบับ');
  }
  let isVerified = false;
  if (type === 'product') {
    const hasPurchased = await Order.findOne({ siteId, customerId: userId, 'items.productId': targetId, status: { $in: ['delivered', 'shipped'] } });
    isVerified = !!hasPurchased;
  }
  const comment = await Comment.create({ siteId, type, targetId, parentId, userId, userName, userEmail, userAvatar, content, images, isVerified });
  return comment;
}

export async function getCommentsByTarget(siteId: string, type: CommentType, targetId: string, filter: any = {}) {
  const comments = await (Comment as any).getCommentsByTarget(siteId, type, targetId, filter);
  return comments;
}

export async function getCommentTree(siteId: string, type: CommentType, targetId: string) {
  const commentTree = await (Comment as any).getCommentTree(siteId, type, targetId);
  return commentTree;
}

export async function getCommentsByUser(userId: string, siteId: string) {
  const comments = await Comment.find({ userId, siteId }).sort({ createdAt: -1 });
  return comments;
}

export async function getCommentById(commentId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  return comment;
}

export async function updateComment(commentId: string, updateData: {
  content?: string;
  images?: string[];
}) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  Object.assign(comment, updateData);
  await comment.save();
  return comment;
}

export async function deleteComment(commentId: string, userId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  if (comment.userId !== userId) throw new HttpError(403, 'ไม่มีสิทธิ์ลบคอมเมนต์นี้');
  await Comment.findByIdAndDelete(commentId);
  return true;
}

export async function likeComment(commentId: string, userId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).like(userId);
  return true;
}

export async function unlikeComment(commentId: string, userId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).unlike(userId);
  return true;
}

export async function dislikeComment(commentId: string, userId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).dislike(userId);
  return true;
}

export async function undislikeComment(commentId: string, userId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).undislike(userId);
  return true;
}

export async function reportComment(commentId: string, userId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).report(userId);
  return true;
}

// Admin functions
export async function approveComment(commentId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).approve();
  return true;
}

export async function rejectComment(commentId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).reject();
  return true;
}

export async function markCommentAsSpam(commentId: string) {
  const comment = await Comment.findById(commentId);
  if (!comment) throw new HttpError(404, 'ไม่พบคอมเมนต์');
  await (comment as any).markAsSpam();
  return true;
}

// Analytics functions
export async function getCommentStats(siteId: string) {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const [totalComments, todayComments, monthlyComments, pendingComments, verifiedComments] = await Promise.all([
    Comment.countDocuments({ siteId }),
    Comment.countDocuments({ siteId, createdAt: { $gte: startOfDay } }),
    Comment.countDocuments({ siteId, createdAt: { $gte: startOfMonth } }),
    Comment.countDocuments({ siteId, status: 'pending' }),
    Comment.countDocuments({ siteId, isVerified: true })
  ]);
  const totalLikes = await Comment.aggregate([
    { $match: { siteId } },
    { $group: { _id: null, total: { $sum: '$likes' } } }
  ]);
  return {
    totalComments,
    todayComments,
    monthlyComments,
    pendingComments,
    verifiedComments,
    totalLikes: totalLikes[0]?.total || 0
  };
} 