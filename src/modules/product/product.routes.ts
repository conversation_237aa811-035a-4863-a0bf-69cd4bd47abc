import { Elysia, t } from "elysia";
import {
	listProducts,
	getProductById,
	getProductBySlug,
	createProduct,
	updateProduct,
	deleteProduct,
	addProductVariant,
	updateProductVariant,
	deleteProductVariant,
	getProductVariant,
	getProductVariantByAttributes,
	updateProductStock,
	checkProductStock,
	listCategories,
	getCategoryById,
	createCategory,
	updateCategory,
	deleteCategory,
	getCart,
	addToCart,
	removeFromCart,
	getProductStats,
} from "./product.service";
import { logger } from "@/core/utils/logger";
import {
	productSchema,
	categorySchema,
	cartItemSchema,
	productResponseSchema,
	productsListResponseSchema,
} from "./product.schema";
import { authPlugin } from "@/core/plugins/auth";
import { sitePlugin } from "@/core/middleware/checkSite";
import { HttpError } from "@/core/utils/error";

export const productRoutes = new Elysia({ prefix: "/product" }).group(
	"/dashboard/:siteId",
	(app) =>
		app
			.use(sitePlugin)
			.use(authPlugin)

			// Product CRUD
			.get("/list", async ({ query }) => {
				try {
					const data = await listProducts(query);
					console.log(data);

					return {
						success: true,
						message: "ดึงข้อมูลสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				} catch (error) {
					console.error("[check-domain] Unexpected error:", error);
					// ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
					if (error instanceof HttpError) throw error;
					throw new HttpError(500, "เกิดข้อผิดพลาดขณะติดต่อกับระบบโดเมน");
				}
			})

			.get("/stats", async ({ params }) => {
				try {
					const data = await getProductStats(params.siteId);

					return {
						success: true,
						message: "ดึงสถิติสินค้าสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				} catch (error) {
					console.error("[product-stats] Unexpected error:", error);
					if (error instanceof HttpError) throw error;
					throw new HttpError(500, "เกิดข้อผิดพลาดในการดึงสถิติสินค้า");
				}
			})

			.post(
				"/create",
				async ({ body }: any) => {
					try {
						const data = await createProduct(body);
						return {
							success: true,
							message: "เพิ่มสินค้าเรียบร้อย",
							statusMessage: "สำเร็จ!",
							timestamp: new Date().toISOString(),
							data,
						};
					} catch (error) {
						console.error("[create-product] Unexpected error:", error);
						// ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
						if (error instanceof HttpError) throw error;
						throw new HttpError(500, "เกิดข้อผิดพลาดขณะติดต่อกับระบบ");
					}
				},
				{
					body: productSchema,
					response: productResponseSchema,
				},
			)

			.get(
				"/detail/:id",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Product details requested", {
						ip,
						productId: params.id,
					});

					const data = await getProductById(params.id);

					logger.info("Product details retrieved", {
						ip,
						productId: params.id,
						found: !!data,
					});

					return { success: true, data };
				},
				{
					response: productResponseSchema,
				},
			)

			.put(
				"/update/:id",
				async ({
					params,
					body,
					request,
				}: {
					params: any;
					body: any;
					request: any;
				}) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Product update attempt", {
						ip,
						productId: params.id,
						productName: body.name,
					});

					const data = await updateProduct(params.id, body);

					logger.info("Product updated successfully", {
						ip,
						productId: params.id,
						productName: body.name,
					});

					return { success: true, data };
				},
				{
					body: productSchema,
					response: productResponseSchema,
				},
			)

			.delete(
				"/delete/:id",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Product deletion attempt", {
						ip,
						productId: params.id,
					});

					const data = await deleteProduct(params.id);

					logger.info("Product deleted successfully", {
						ip,
						productId: params.id,
					});

					return { success: true, data };
				},
				{
					response: productResponseSchema,
				},
			)

			// Category CRUD
			.get(
				"/category/list",
				async ({ request }: any) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Categories list requested", { ip });

					const data = await listCategories();

					logger.info("Categories list retrieved", {
						ip,
						count: Array.isArray(data) ? data.length : 0,
					});

					return {
						success: true,
						message: "ดึงข้อมูลสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			.post(
				"/category/create",
				async ({ body, request }: { body: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Category creation attempt", {
						ip,
						categoryName: body.name,
						siteId: body.siteId,
					});

					const data = await createCategory(body);

					logger.info("Category created successfully", {
						ip,
						categoryId: data._id || data.id,
						categoryName: body.name,
					});

					return { success: true, data };
				},
				{
					body: categorySchema,
					response: productResponseSchema,
				},
			)

			.get(
				"/category/detail/:id",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Category details requested", {
						ip,
						categoryId: params.id,
					});

					const data = await getCategoryById(params.id);

					return {
						success: true,
						message: "ดึงข้อมูลสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			.put(
				"/category/update/:id",
				async ({
					params,
					body,
					request,
				}: {
					params: any;
					body: any;
					request: any;
				}) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Category update attempt", {
						ip,
						categoryId: params.id,
						categoryName: body.name,
					});

					const data = await updateCategory(params.id, body);

					logger.info("Category updated successfully", {
						ip,
						categoryId: params.id,
					});

					return { success: true, data };
				},
				{
					body: categorySchema,
					response: productResponseSchema,
				},
			)

			.delete(
				"/category/delete/:id",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Category deletion attempt", {
						ip,
						categoryId: params.id,
					});

					const data = await deleteCategory(params.id);

					logger.info("Category deleted successfully", {
						ip,
						categoryId: params.id,
					});

					return {
						success: true,
						message: "ลบข้อมูลสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			// === Product Variants ===

			// เพิ่มตัวเลือกสินค้า
			.post(
				"/variant/:productId/add",
				async ({ params, body, request }: { params: any; body: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Add product variant attempt", {
						ip,
						productId: params.productId,
						variantName: body.name,
					});

					const data = await addProductVariant(params.productId, body);

					logger.info("Product variant added successfully", {
						ip,
						productId: params.productId,
						variantName: body.name,
					});

					return {
						success: true,
						message: "เพิ่มตัวเลือกสินค้าสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					body: t.Object({
						name: t.String({ minLength: 1, error: 'ชื่อตัวเลือกต้องไม่ว่าง' }),
						sku: t.Optional(t.String()),
						price: t.Optional(t.Number({ minimum: 0 })),
						stock: t.Optional(t.Number({ minimum: 0 })),
						attributes: t.Record(t.String(), t.String()),
						images: t.Optional(t.Array(t.String())),
						isActive: t.Optional(t.Boolean())
					}),
					response: productResponseSchema,
				},
			)

			// อัปเดตตัวเลือกสินค้า
			.put(
				"/variant/:productId/:variantId",
				async ({ params, body, request }: { params: any; body: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Update product variant attempt", {
						ip,
						productId: params.productId,
						variantId: params.variantId,
					});

					const data = await updateProductVariant(params.productId, params.variantId, body);

					logger.info("Product variant updated successfully", {
						ip,
						productId: params.productId,
						variantId: params.variantId,
					});

					return {
						success: true,
						message: "อัปเดตตัวเลือกสินค้าสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					body: t.Object({
						name: t.Optional(t.String({ minLength: 1 })),
						sku: t.Optional(t.String()),
						price: t.Optional(t.Number({ minimum: 0 })),
						stock: t.Optional(t.Number({ minimum: 0 })),
						attributes: t.Optional(t.Record(t.String(), t.String())),
						images: t.Optional(t.Array(t.String())),
						isActive: t.Optional(t.Boolean())
					}),
					response: productResponseSchema,
				},
			)

			// ลบตัวเลือกสินค้า
			.delete(
				"/variant/:productId/:variantId",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Delete product variant attempt", {
						ip,
						productId: params.productId,
						variantId: params.variantId,
					});

					const data = await deleteProductVariant(params.productId, params.variantId);

					logger.info("Product variant deleted successfully", {
						ip,
						productId: params.productId,
						variantId: params.variantId,
					});

					return {
						success: true,
						message: "ลบตัวเลือกสินค้าสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			// ดูตัวเลือกสินค้าเฉพาะ
			.get(
				"/variant/:productId/:variantId",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Get product variant requested", {
						ip,
						productId: params.productId,
						variantId: params.variantId,
					});

					const data = await getProductVariant(params.productId, params.variantId);

					return {
						success: true,
						message: "ดึงข้อมูลตัวเลือกสินค้าสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			// ค้นหาตัวเลือกสินค้าตาม attributes
			.post(
				"/variant/:productId/find",
				async ({ params, body, request }: { params: any; body: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Find product variant by attributes", {
						ip,
						productId: params.productId,
						attributes: body.attributes,
					});

					const data = await getProductVariantByAttributes(params.productId, body.attributes);

					return {
						success: true,
						message: "พบตัวเลือกสินค้า",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					body: t.Object({
						attributes: t.Record(t.String(), t.String())
					}),
					response: productResponseSchema,
				},
			)

			// === Stock Management ===

			// อัปเดตสต็อกสินค้า
			.put(
				"/stock/:productId",
				async ({ params, body, request }: { params: any; body: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Update product stock attempt", {
						ip,
						productId: params.productId,
						stock: body.stock,
						variantId: body.variantId,
					});

					const data = await updateProductStock(params.productId, body.stock, body.variantId);

					logger.info("Product stock updated successfully", {
						ip,
						productId: params.productId,
						stock: body.stock,
					});

					return {
						success: true,
						message: "อัปเดตสต็อกสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					body: t.Object({
						stock: t.Number({ minimum: 0, error: 'จำนวนสต็อกต้องไม่ติดลบ' }),
						variantId: t.Optional(t.String())
					}),
					response: productResponseSchema,
				},
			)

			// ตรวจสอบสต็อกสินค้า
			.post(
				"/stock/:productId/check",
				async ({ params, body, request }: { params: any; body: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Check product stock", {
						ip,
						productId: params.productId,
						quantity: body.quantity,
						variantId: body.variantId,
					});

					const data = await checkProductStock(params.productId, body.quantity, body.variantId);

					return {
						success: true,
						message: "ตรวจสอบสต็อกสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					body: t.Object({
						quantity: t.Number({ minimum: 1, error: 'จำนวนต้องมากกว่า 0' }),
						variantId: t.Optional(t.String())
					}),
					response: productResponseSchema,
				},
			)

			// ดูสินค้าตาม slug (สำหรับ frontend)
			.get(
				"/slug/:slug",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";
					const siteId = request.headers.get("x-site-id") || "";

					logger.info("Get product by slug", {
						ip,
						slug: params.slug,
						siteId,
					});

					const data = await getProductBySlug(params.slug, siteId);

					return {
						success: true,
						message: "ดึงข้อมูลสินค้าสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			// Cart
			.get(
				"/cart/list/:userId",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Cart requested", {
						ip,
						userId: params.userId,
					});

					const data = await getCart(params.userId);

					return {
						success: true,
						message: "ดึงข้อมูลสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			)

			.post(
				"/cart/add/:userId",
				async ({
					params,
					body,
					request,
				}: {
					params: any;
					body: any;
					request: any;
				}) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Add to cart attempt", {
						ip,
						userId: params.userId,
						productId: body.productId,
						qty: body.qty,
					});

					const data = await addToCart(params.userId, body.productId, body.qty);

					logger.info("Added to cart successfully", {
						ip,
						userId: params.userId,
						productId: body.productId,
						qty: body.qty,
					});

					return { success: true, data };
				},
				{
					body: cartItemSchema,
					response: productResponseSchema,
				},
			)

			.delete(
				"/cart/remove/:userId/:productId",
				async ({ params, request }: { params: any; request: any }) => {
					const ip = request.headers.get("x-forwarded-for") || "unknown";

					logger.info("Remove from cart attempt", {
						ip,
						userId: params.userId,
						productId: params.productId,
					});

					const data = await removeFromCart(params.userId, params.productId);

					logger.info("Removed from cart successfully", {
						ip,
						userId: params.userId,
						productId: params.productId,
					});

					return {
						success: true,
						message: "ลบข้อมูลสำเร็จ",
						statusMessage: "สำเร็จ!",
						timestamp: new Date().toISOString(),
						data,
					};
				},
				{
					response: productResponseSchema,
				},
			),
);
