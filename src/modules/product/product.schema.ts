import { t } from 'elysia';

// Product image schema
export const productImageSchema = t.Object({
  url: t.String({
    format: 'uri',
    description: 'Image URL',
    error: 'URL รูปภาพต้องเป็นข้อความและเป็นลิงก์ที่ถูกต้อง'
  }),
  alt: t.Optional(t.String({
    description: 'Alternative text for image',
    error: 'ข้อความอธิบายรูปภาพต้องเป็นข้อความ'
  })),
  isPrimary: t.<PERSON>({
    description: 'Whether this is the primary image',
    error: 'isPrimary ต้องเป็น true หรือ false เท่านั้น'
  }),
  variantId: t.Optional(t.String({
    description: 'Associated variant ID',
    error: 'variantId ต้องเป็นข้อความ'
  }))
});

// Product variant schema
export const productVariantSchema = t.Object({
  name: t.String({
    minLength: 1,
    description: 'Variant name',
    error: 'ชื่อตัวเลือกสินค้าต้องไม่ว่าง'
  }),
  sku: t.Optional(t.String({
    description: 'Stock Keeping Unit',
    error: 'SKU ต้องเป็นข้อความ'
  })),
  price: t.Optional(t.Number({
    minimum: 0,
    description: 'Variant price',
    error: 'ราคาต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  stock: t.Optional(t.Number({
    minimum: 0,
    description: 'Stock quantity',
    error: 'จำนวนสต็อกต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  attributes: t.Record(t.String({ error: 'ชื่อ attribute ต้องเป็นข้อความ' }), t.String({ error: 'ค่าของ attribute ต้องเป็นข้อความ' }), {
    description: 'Variant attributes (color, size, etc.)'
  }),
  images: t.Optional(t.Array(t.String({
    format: 'uri',
    error: 'URL รูปภาพต้องเป็นข้อความและเป็นลิงก์ที่ถูกต้อง'
  }), {
    description: 'Variant images'
  })),
  isActive: t.Boolean({
    description: 'Whether variant is active',
    error: 'isActive ต้องเป็น true หรือ false เท่านั้น'
  })
});

// Digital asset schema
export const digitalAssetSchema = t.Object({
  name: t.String({
    minLength: 1,
    description: 'Asset name',
    error: 'ชื่อไฟล์ดิจิทัลต้องไม่ว่าง'
  }),
  url: t.String({
    format: 'uri',
    description: 'Asset download URL',
    error: 'URL ไฟล์ต้องเป็นข้อความและเป็นลิงก์ที่ถูกต้อง'
  }),
  fileSize: t.Optional(t.Number({
    minimum: 0,
    description: 'File size in bytes',
    error: 'ขนาดไฟล์ต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  fileType: t.Optional(t.String({
    description: 'MIME type',
    error: 'ประเภทไฟล์ต้องเป็นข้อความ'
  })),
  downloadLimit: t.Optional(t.Number({
    minimum: 0,
    description: 'Maximum download count',
    error: 'จำนวนครั้งดาวน์โหลดต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  expiryDays: t.Optional(t.Number({
    minimum: 0,
    description: 'Days until download expires',
    error: 'จำนวนวันหมดอายุต้องเป็นตัวเลขและไม่ติดลบ'
  }))
});

// Shipping schema
export const shippingSchema = t.Object({
  weight: t.Optional(t.Number({
    minimum: 0,
    description: 'Weight in grams',
    error: 'น้ำหนักต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  dimensions: t.Optional(t.Object({
    length: t.Number({ minimum: 0, error: 'ความยาวต้องเป็นตัวเลขและไม่ติดลบ' }),
    width: t.Number({ minimum: 0, error: 'ความกว้างต้องเป็นตัวเลขและไม่ติดลบ' }),
    height: t.Number({ minimum: 0, error: 'ความสูงต้องเป็นตัวเลขและไม่ติดลบ' })
  }, {
    description: 'Dimensions in centimeters'
  })),
  shippingClass: t.Optional(t.String({
    description: 'Shipping class identifier',
    error: 'shippingClass ต้องเป็นข้อความ'
  }))
});

// Main product schema
export const productSchema = t.Object({
  name: t.String({
    minLength: 1,
    maxLength: 200,
    description: 'Product name',
    error: 'ชื่อสินค้าต้องไม่ว่างและไม่เกิน 200 ตัวอักษร'
  }),
  type: t.Union([
    t.Literal('physical', { error: 'ต้องเป็น physical เท่านั้น' }),
    t.Literal('digital', { error: 'ต้องเป็น digital เท่านั้น' }),
    t.Literal('service', { error: 'ต้องเป็น service เท่านั้น' }),
    t.Literal('subscription', { error: 'ต้องเป็น subscription เท่านั้น' })
  ], {
    description: 'Product type',
    error: 'ประเภทสินค้าไม่ถูกต้อง'
  }),
  saleChannel: t.Union([
    t.Literal('online', { error: 'ต้องเป็น online เท่านั้น' }),
    t.Literal('offline', { error: 'ต้องเป็น offline เท่านั้น' }),
    t.Literal('both', { error: 'ต้องเป็น both เท่านั้น' })
  ], {
    description: 'Sales channel',
    error: 'ช่องทางการขายไม่ถูกต้อง'
  }),
  price: t.Number({
    minimum: 0,
    description: 'Product price',
    error: 'ราคาสินค้าต้องเป็นตัวเลขและไม่ติดลบ'
  }),
  compareAtPrice: t.Optional(t.Number({
    minimum: 0,
    description: 'Compare at price (original price)',
    error: 'ราคาปกติต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  costPrice: t.Optional(t.Number({
    minimum: 0,
    description: 'Cost price',
    error: 'ราคาทุนต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  stock: t.Optional(t.Number({
    minimum: 0,
    description: 'Stock quantity',
    error: 'จำนวนสต็อกต้องเป็นตัวเลขและไม่ติดลบ'
  })),
  trackStock: t.Optional(t.Boolean({
    description: 'Whether to track stock',
    error: 'trackStock ต้องเป็น true หรือ false เท่านั้น'
  })),
  allowBackorder: t.Optional(t.Boolean({
    description: 'Allow backorders when out of stock',
    error: 'allowBackorder ต้องเป็น true หรือ false เท่านั้น'
  })),
  categoryId: t.Optional(t.String({
    description: 'Category identifier',
    error: 'categoryId ต้องเป็นข้อความ'
  })),
  description: t.Optional(t.String({
    maxLength: 5000,
    description: 'Product description',
    error: 'รายละเอียดสินค้าต้องไม่เกิน 5000 ตัวอักษร'
  })),
  shortDescription: t.Optional(t.String({
    maxLength: 500,
    description: 'Short product description',
    error: 'รายละเอียดสั้นต้องไม่เกิน 500 ตัวอักษร'
  })),
  tags: t.Optional(t.Array(t.String({ error: 'tag ต้องเป็นข้อความ' }), {
    description: 'Product tags'
  })),
  images: t.Optional(t.Array(productImageSchema, {
    description: 'Product images'
  })),
  hasVariants: t.Optional(t.Boolean({
    description: 'Whether product has variants',
    error: 'hasVariants ต้องเป็น true หรือ false เท่านั้น'
  })),
  variants: t.Optional(t.Array(productVariantSchema, {
    description: 'Product variants'
  })),
  variantAttributes: t.Optional(t.Array(t.String({ error: 'ชื่อ attribute ต้องเป็นข้อความ' }), {
    description: 'Variant attribute names'
  })),
  digitalAssets: t.Optional(t.Array(digitalAssetSchema, {
    description: 'Digital assets for digital products'
  })),
  shipping: t.Optional(shippingSchema),
  seoTitle: t.Optional(t.String({
    maxLength: 60,
    description: 'SEO title',
    error: 'SEO title ต้องไม่เกิน 60 ตัวอักษร'
  })),
  seoDescription: t.Optional(t.String({
    maxLength: 160,
    description: 'SEO description',
    error: 'SEO description ต้องไม่เกิน 160 ตัวอักษร'
  })),
  featured: t.Optional(t.Boolean({
    description: 'Whether product is featured',
    error: 'featured ต้องเป็น true หรือ false เท่านั้น'
  })),
  isActive: t.Optional(t.Boolean({
    description: 'Whether product is active',
    error: 'isActive ต้องเป็น true หรือ false เท่านั้น'
  })),
  customFields: t.Optional(t.Record(t.String({ error: 'ชื่อฟิลด์ต้องเป็นข้อความ' }), t.Any(), {
    description: 'Custom fields'
  }))
});

// Category schema
export const categorySchema = t.Object({
  siteId: t.String({
    minLength: 1,
    description: 'Site identifier',
    error: 'siteId ต้องไม่ว่าง'
  }),
  name: t.String({
    minLength: 1,
    maxLength: 100,
    description: 'Category name',
    error: 'ชื่อหมวดหมู่ต้องไม่ว่างและไม่เกิน 100 ตัวอักษร'
  }),
  parentId: t.Optional(t.String({
    description: 'Parent category ID',
    error: 'parentId ต้องเป็นข้อความ'
  }))
});

// Cart item schema
export const cartItemSchema = t.Object({
  productId: t.String({
    minLength: 1,
    description: 'Product identifier',
    error: 'productId ต้องไม่ว่าง'
  }),
  qty: t.Number({
    minimum: 1,
    description: 'Quantity',
    error: 'จำนวนต้องเป็นตัวเลขและมากกว่า 0'
  })
});

// Response schemas
export const productResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.Optional(t.String({ error: 'message ต้องเป็นข้อความ' })),
  data: t.Optional(t.Any())
});

export const productsListResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Object({
    products: t.Array(t.Any()),
    total: t.Number({ error: 'total ต้องเป็นตัวเลข' })
  })
});