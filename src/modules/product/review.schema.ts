import { t } from 'elysia';

// Create Review Schema
export const createReviewSchema = t.Object({
  productId: t.String({ minLength: 1 }),
  rating: t.Number({ minimum: 1, maximum: 5 }),
  title: t.String({ minLength: 1, maxLength: 200 }),
  comment: t.String({ minLength: 1, maxLength: 2000 }),
  images: t.Optional(t.Array(t.String()))
});

// Update Review Schema
export const updateReviewSchema = t.Object({
  rating: t.Optional(t.Number({ minimum: 1, maximum: 5 })),
  title: t.Optional(t.String({ minLength: 1, maxLength: 200 })),
  comment: t.Optional(t.String({ minLength: 1, maxLength: 2000 })),
  images: t.Optional(t.Array(t.String()))
});

// Review Filter Schema
export const reviewFilterSchema = t.Object({
  rating: t.Optional(t.Number({ minimum: 1, maximum: 5 })),
  isVerified: t.Optional(t.Boolean()),
  sortBy: t.Optional(t.Union([
    t.Literal('newest'),
    t.Literal('oldest'),
    t.Literal('rating'),
    t.Literal('helpful')
  ])),
  page: t.Optional(t.Number({ minimum: 1 })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 50 }))
});

// Review Response Schema
export const reviewResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Optional(t.Any())
});

// Review List Response Schema
export const reviewListResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Array(t.Any()),
  total: t.Number(),
  page: t.Number(),
  limit: t.Number()
});

// Rating Stats Response Schema
export const ratingStatsResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Object({
    averageRating: t.Number(),
    totalReviews: t.Number(),
    ratingDistribution: t.Record(t.String(), t.Number())
  })
});

// Review Stats Response Schema
export const reviewStatsResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Object({
    totalReviews: t.Number(),
    todayReviews: t.Number(),
    monthlyReviews: t.Number(),
    pendingReviews: t.Number(),
    verifiedReviews: t.Number(),
    averageRating: t.Number()
  })
}); 