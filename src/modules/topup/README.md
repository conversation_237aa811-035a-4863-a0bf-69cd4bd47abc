# ระบบเติมเงิน (Topup System)

ระบบเติมเงินที่รองรับการเติม moneyPoint และ goldPoint ให้กับ User และ Customer

## คุณสมบัติ

- ✅ เติมเงินให้ User (moneyPoint, goldPoint)
- ✅ เติมเงินให้ Customer (moneyPoint, goldPoint) 
- ✅ ระบบอนุมัติ/ปฏิเสธคำขอเติมเงิน
- ✅ ติดตามสถานะการเติมเงิน (pending, completed, failed, cancelled)
- ✅ ประวัติการเติมเงิน
- ✅ สถิติการเติมเงิน
- ✅ ระบบ Logging และ Error Handling

## API Endpoints

### 1. สร้างคำขอเติมเงิน
```
POST /v1/topup
```

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Body (User):**
```json
{
  "targetType": "user",
  "targetId": "user_id",
  "pointType": "moneyPoint",
  "amount": 500,
  "description": "เติมเงินให้ลูกค้า"
}
```

**Body (Customer):**
```json
{
  "targetType": "customer",
  "targetId": "customer_id",
  "pointType": "goldPoint",
  "amount": 300,
  "siteId": "site_id",
  "description": "เติมโบนัสพ้อยให้สมาชิก"
}
```

### 2. ดึงรายการคำขอเติมเงิน
```
GET /v1/topup?page=1&limit=20&status=pending&targetType=user
```

**Query Parameters:**
- `targetType`: "user" | "customer" (optional)
- `targetId`: string (optional)
- `pointType`: "moneyPoint" | "goldPoint" (optional)
- `status`: "pending" | "completed" | "failed" | "cancelled" (optional)
- `siteId`: string (optional, สำหรับ customer)
- `page`: number (optional, default: 1)
- `limit`: number (optional, default: 20, max: 100)

### 3. ดึงคำขอเติมเงินตาม ID
```
GET /v1/topup/:id
```

### 4. อนุมัติ/ปฏิเสธคำขอเติมเงิน
```
PATCH /v1/topup/:id/process
```

**Body:**
```json
{
  "action": "approve"  // หรือ "reject"
}
```

### 5. ดึงประวัติการเติมเงินของ User
```
GET /v1/topup/history/user/:userId
```

### 6. ดึงประวัติการเติมเงินของ Customer
```
GET /v1/topup/history/customer/:customerId?siteId=site_id
```

### 7. ดึงสถิติการเติมเงิน
```
GET /v1/topup/stats?siteId=site_id
```

## สิทธิ์การเข้าถึง

- **Admin เท่านั้น**: สามารถสร้าง, อนุมัติ, ปฏิเสธ และดูรายการคำขอเติมเงินทั้งหมด
- **User**: สามารถดูประวัติการเติมเงินของตัวเองได้เท่านั้น

## สถานะการเติมเงิน

- `pending`: รอการอนุมัติ
- `completed`: เติมเงินสำเร็จแล้ว
- `failed`: เติมเงินไม่สำเร็จ (เกิดข้อผิดพลาด)
- `cancelled`: ถูกปฏิเสธ/ยกเลิก

## ตัวอย่างการใช้งาน

### 1. เติม moneyPoint ให้ User
```bash
curl -X POST http://localhost:5000/v1/topup \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "targetType": "user",
    "targetId": "user123",
    "pointType": "moneyPoint", 
    "amount": 1000,
    "description": "เติมเงินโปรโมชั่น"
  }'
```

### 2. เติม goldPoint ให้ Customer
```bash
curl -X POST http://localhost:5000/v1/topup \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "targetType": "customer",
    "targetId": "customer456", 
    "pointType": "goldPoint",
    "amount": 500,
    "siteId": "site789",
    "description": "เติมโบนัสพ้อยสำหรับสมาชิก VIP"
  }'
```

### 3. อนุมัติคำขอเติมเงิน
```bash
curl -X PATCH http://localhost:5000/v1/topup/topup123/process \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "approve"
  }'
```

### 4. ดูสถิติการเติมเงิน
```bash
curl -X GET http://localhost:5000/v1/topup/stats \
  -H "Authorization: Bearer <admin_token>"
```

## Response Format

### สำเร็จ
```json
{
  "success": true,
  "message": "ข้อความสำเร็จ",
  "data": { ... }
}
```

### ผิดพลาด
```json
{
  "success": false,
  "message": "ข้อความข้อผิดพลาด"
}
```

## การทดสอบ

รันการทดสอบด้วยคำสั่ง:
```bash
bun test src/modules/topup/topup.service.test.ts
```

## Database Schema

### Topup Collection
```typescript
{
  _id: string,
  targetType: 'user' | 'customer',
  targetId: string,
  pointType: 'moneyPoint' | 'goldPoint',
  amount: number,
  description?: string,
  status: 'pending' | 'completed' | 'failed' | 'cancelled',
  processedBy?: string,
  processedAt?: Date,
  siteId?: string,
  createdAt: Date,
  updatedAt: Date
}
```

## หมายเหตุ

- การเติมเงินให้ Customer จำเป็นต้องระบุ `siteId`
- เฉพาะ Admin เท่านั้นที่สามารถสร้างและอนุมัติคำขอเติมเงินได้
- ระบบจะบันทึก Log ทุกการดำเนินการเพื่อการตรวจสอบ
- การเติมเงินจะดำเนินการจริงเมื่อได้รับการอนุมัติเท่านั้น