import { t } from 'elysia';

export const createTopupSchema = t.Object({
  targetType: t.Union([
    t.Literal('user', { error: 'ต้องเป็น user เท่านั้น' }),
    t.Literal('customer', { error: 'ต้องเป็น customer เท่านั้น' })
  ], { error: 'targetType ไม่ถูกต้อง' }),
  targetId: t.String({ minLength: 1, error: 'targetId ต้องไม่ว่าง' }),
  pointType: t.Union([
    t.Literal('moneyPoint', { error: 'ต้องเป็น moneyPoint เท่านั้น' }),
    t.Literal('goldPoint', { error: 'ต้องเป็น goldPoint เท่านั้น' })
  ], { error: 'pointType ไม่ถูกต้อง' }),
  amount: t.Number({ minimum: 1, error: 'amount ต้องเป็นตัวเลขและมากกว่า 0' }),
  description: t.Optional(t.String({ error: 'description ต้องเป็นข้อความ' })),
  siteId: t.Optional(t.String({ error: 'siteId ต้องเป็นข้อความ' })),
});

export const updateTopupStatusSchema = t.Object({
  status: t.Union([
    t.Literal('pending', { error: 'ต้องเป็น pending เท่านั้น' }),
    t.Literal('completed', { error: 'ต้องเป็น completed เท่านั้น' }),
    t.Literal('failed', { error: 'ต้องเป็น failed เท่านั้น' }),
    t.Literal('cancelled', { error: 'ต้องเป็น cancelled เท่านั้น' })
  ], { error: 'status ไม่ถูกต้อง' }),
});

export const topupQuerySchema = t.Object({
  targetType: t.Optional(t.Union([
    t.Literal('user', { error: 'ต้องเป็น user เท่านั้น' }),
    t.Literal('customer', { error: 'ต้องเป็น customer เท่านั้น' })
  ], { error: 'targetType ไม่ถูกต้อง' })),
  targetId: t.Optional(t.String({ error: 'targetId ต้องเป็นข้อความ' })),
  pointType: t.Optional(t.Union([
    t.Literal('moneyPoint', { error: 'ต้องเป็น moneyPoint เท่านั้น' }),
    t.Literal('goldPoint', { error: 'ต้องเป็น goldPoint เท่านั้น' })
  ], { error: 'pointType ไม่ถูกต้อง' })),
  status: t.Optional(t.Union([
    t.Literal('pending', { error: 'ต้องเป็น pending เท่านั้น' }),
    t.Literal('completed', { error: 'ต้องเป็น completed เท่านั้น' }),
    t.Literal('failed', { error: 'ต้องเป็น failed เท่านั้น' }),
    t.Literal('cancelled', { error: 'ต้องเป็น cancelled เท่านั้น' })
  ], { error: 'status ไม่ถูกต้อง' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' })),
  siteId: t.Optional(t.String({ error: 'siteId ต้องเป็นข้อความ' })),
});

export const processTopupSchema = t.Object({
  action: t.Union([
    t.Literal('approve', { error: 'ต้องเป็น approve เท่านั้น' }),
    t.Literal('reject', { error: 'ต้องเป็น reject เท่านั้น' })
  ], { error: 'action ไม่ถูกต้อง' }),
});