import { generateFileId } from '@/core/utils/idGenerator';
import { Schema, model } from 'mongoose';

export interface ITopupBase {
  _id: string;
  targetType: 'user' | 'customer';
  targetId: string;
  pointType: 'moneyPoint' | 'goldPoint';
  amount: number;
  description?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  processedBy?: string; // admin user id
  processedAt?: Date;
  siteId?: string; // for customer topups
}

export interface ITopup extends ITopupBase {
  createdAt: Date;
  updatedAt: Date;
}

const topupSchemaMongoose = new Schema<ITopup>(
  {
    _id: {
      type: String,
      required: true,
      trim: true,
      default: () => generateFileId(8),
    },
    targetType: {
      type: String,
      required: true,
      enum: ['user', 'customer'],
    },
    targetId: {
      type: String,
      required: true,
    },
    pointType: {
      type: String,
      required: true,
      enum: ['moneyPoint', 'goldPoint'],
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    description: {
      type: String,
      default: '',
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'completed', 'failed', 'cancelled'],
      default: 'pending',
    },
    processedBy: {
      type: String,
      ref: 'User',
    },
    processedAt: {
      type: Date,
    },
    siteId: {
      type: String,
      ref: 'Site',
    },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
  }
);

// Index for better query performance
topupSchemaMongoose.index({ targetType: 1, targetId: 1 });
topupSchemaMongoose.index({ status: 1 });
topupSchemaMongoose.index({ createdAt: -1 });

export const Topup = model<ITopup>('Topup', topupSchemaMongoose);