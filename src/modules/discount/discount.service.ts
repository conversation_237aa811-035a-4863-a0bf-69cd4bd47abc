import { Discount, IDiscount, type DiscountType, type DiscountStatus, type DiscountTarget } from './discount.model';
// import { Order } from '../order';
import { HttpError } from '@/core/utils/error';

// Validation function for discount rules
export async function validateDiscountRules(discountData: {
  type: DiscountType;
  value: number;
  startDate: Date;
  endDate: Date;
  conditions?: any;
}) {
  const { type, value, startDate, endDate, conditions } = discountData;

  // ตรวจสอบ startDate < endDate
  if (new Date(startDate) >= new Date(endDate)) {
    throw new HttpError(400, 'วันที่เริ่มต้นต้องน้อยกว่าวันที่สิ้นสุด');
  }

  // ตรวจสอบ percentage ไม่เกิน 100%
  if ((type === 'percentage' || type === 'cashback') && value > 100) {
    throw new HttpError(400, 'เปอร์เซ็นต์ส่วนลดไม่ควรเกิน 100%');
  }

  // ตรวจสอบ fixed amount ต้องมากกว่า 0
  if ((type === 'fixed' || type === 'bundle_discount') && value <= 0) {
    throw new HttpError(400, 'จำนวนเงินส่วนลดต้องมากกว่า 0');
  }

  // ตรวจสอบ tiered_discount ต้องมี tierRules
  if (type === 'tiered_discount') {
    if (!conditions?.tierRules || conditions.tierRules.length === 0) {
      throw new HttpError(400, 'ส่วนลดแบบขั้นบันไดต้องมีกฎการคำนวณ (tierRules)');
    }

    // ตรวจสอบ tierRules ต้องเรียงจากน้อยไปมาก
    const sortedTiers = [...conditions.tierRules].sort((a, b) => a.minAmount - b.minAmount);
    for (let i = 0; i < sortedTiers.length; i++) {
      if (sortedTiers[i].minAmount < 0 || sortedTiers[i].discountValue < 0) {
        throw new HttpError(400, 'ค่าในกฎการคำนวณต้องไม่ติดลบ');
      }
    }
  }

  // ตรวจสอบ bundle_discount ต้องมี bundleProducts
  if (type === 'bundle_discount') {
    if (!conditions?.bundleProducts || conditions.bundleProducts.length === 0) {
      throw new HttpError(400, 'ส่วนลดแบบแพ็คเกจต้องระบุสินค้าในแพ็คเกจ (bundleProducts)');
    }
  }

  // ตรวจสอบ timeRange format
  if (conditions?.timeRange) {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(conditions.timeRange.start) || !timeRegex.test(conditions.timeRange.end)) {
      throw new HttpError(400, 'รูปแบบเวลาต้องเป็น HH:MM (เช่น 09:00, 17:30)');
    }

    if (conditions.timeRange.start >= conditions.timeRange.end) {
      throw new HttpError(400, 'เวลาเริ่มต้องน้อยกว่าเวลาสิ้นสุด');
    }
  }

  // ตรวจสอบ dayOfWeek
  if (conditions?.dayOfWeek && conditions.dayOfWeek.length > 0) {
    const validDays = conditions.dayOfWeek.every((day: number) => day >= 0 && day <= 6);
    if (!validDays) {
      throw new HttpError(400, 'วันในสัปดาห์ต้องเป็นตัวเลข 0-6 (0=อาทิตย์, 6=เสาร์)');
    }
  }

  return true;
}

// Discount Service
export async function createDiscount(discountData: {
  siteId?: string;
  name: string;
  description?: string;
  code?: string;
  type: DiscountType;
  value: number;
  maxDiscountAmount?: number;
  target: DiscountTarget;
  conditions: any;
  startDate: Date;
  endDate: Date;
}) {
  try {
    const { siteId, name, description, code, type, value, maxDiscountAmount, target, conditions, startDate, endDate } = discountData;

    // ตรวจสอบกฎการสร้าง discount
    await validateDiscountRules({ type, value, startDate, endDate, conditions });

    // ตรวจสอบ code ซ้ำ
    if (code) {
      const existingDiscount = await Discount.findOne({ code, siteId });
      if (existingDiscount) {
        throw new HttpError(400, 'Discount code นี้ถูกใช้แล้ว');
      }
    }

    const discount = await Discount.create({
      siteId,
      name,
      description,
      code,
      type,
      value,
      maxDiscountAmount,
      target,
      conditions,
      startDate,
      endDate
    });

    return {
      success: true,
      message: 'สร้าง discount สำเร็จ',
      data: discount
    };
  } catch (err: any) {
    console.error('Error in createDiscount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง discount');
  }
}

export async function getDiscountById(discountId: string) {
  try {
    const discount = await Discount.findById(discountId);
    if (!discount) {
      throw new HttpError(404, 'ไม่พบ discount');
    }
    return discount;
  } catch (err: any) {
    console.error('Error in getDiscountById:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล discount');
  }
}

export async function getDiscountByCode(code: string, siteId: string) {
  try {
    const discount = await (Discount as any).getDiscountByCode(code, siteId);
    if (!discount) {
      throw new HttpError(404, 'ไม่พบ discount code');
    }
    return discount;
  } catch (err: any) {
    console.error('Error in getDiscountByCode:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล discount');
  }
}

export async function getActiveDiscounts(siteId: string) {
  try {
    const discounts = await (Discount as any).getActiveDiscounts(siteId);
    return discounts;
  } catch (err: any) {
    console.error('Error in getActiveDiscounts:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง active discounts');
  }
}


// ฟังก์ชันตรวจสอบสถานะลูกค้าใหม่ (ปลอดภัย)
export async function checkUserFirstTimeStatus(userId: string, siteId?: string): Promise<boolean> {
  try {
    // วิธีที่ 1: ตรวจสอบจาก order history (ถ้ามี order module)
    // const Order = require('../order/order.model'); // uncomment ถ้ามี
    // const orderCount = await Order.countDocuments({ 
    //   userId, 
    //   siteId,
    //   status: { $in: ['completed', 'delivered', 'paid'] }
    // });
    // return orderCount === 0;

    // วิธีที่ 2: ตรวจสอบจาก discount usage history
    const discountUsageCount = await Discount.countDocuments({
      siteId,
      'usageHistory.userId': userId
    });

    // วิธีที่ 3: ตรวจสอบจาก user registration date
    // const User = require('../user/user.model'); // uncomment ถ้าต้องการ
    // const user = await User.findById(userId);
    // const daysSinceRegistration = (Date.now() - user.createdAt) / (1000 * 60 * 60 * 24);
    // return daysSinceRegistration <= 30; // ลูกค้าใหม่ = สมัครมาไม่เกิน 30 วัน

    // ใช้วิธีที่ 2 ก่อน (ถ้าไม่เคยใช้ discount = ลูกค้าใหม่)
    return discountUsageCount === 0;

  } catch (error) {
    console.error('Error checking first time status:', error);
    // ถ้า error ให้ถือว่าเป็นลูกค้าเก่า (ปลอดภัยกว่า)
    return false;
  }
}

export async function updateDiscount(discountId: string, updateData: {
  name?: string;
  description?: string;
  code?: string;
  type?: DiscountType;
  value?: number;
  maxDiscountAmount?: number;
  target?: DiscountTarget;
  conditions?: any;
  startDate?: Date;
  endDate?: Date;
  status?: DiscountStatus;
}) {
  try {
    const discount = await Discount.findById(discountId);
    if (!discount) {
      throw new HttpError(404, 'ไม่พบ discount');
    }

    // ตรวจสอบ code ซ้ำถ้ามีการอัปเดต
    if (updateData.code && updateData.code !== discount.code) {
      const existingDiscount = await Discount.findOne({ code: updateData.code, siteId: discount.siteId });
      if (existingDiscount) {
        throw new HttpError(400, 'Discount code นี้ถูกใช้แล้ว');
      }
    }

    // อัปเดตข้อมูล
    Object.assign(discount, updateData);
    await discount.save();

    return {
      success: true,
      message: 'อัปเดต discount สำเร็จ',
      data: discount
    };
  } catch (err: any) {
    console.error('Error in updateDiscount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต discount');
  }
}

export async function deleteDiscount(discountId: string) {
  try {
    const discount = await Discount.findById(discountId);
    if (!discount) {
      throw new HttpError(404, 'ไม่พบ discount');
    }

    await Discount.findByIdAndDelete(discountId);

    return {
      success: true,
      message: 'ลบ discount สำเร็จ'
    };
  } catch (err: any) {
    console.error('Error in deleteDiscount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ discount');
  }
}

export async function validateDiscount(discountCode: string, options: {
  target?: string;
  siteId?: string;
  userId: string;
  orderAmount: number;
  items: Array<any>;
  isFirstTime: boolean;
}) {
  try {
    const { target, siteId, userId, orderAmount, items, isFirstTime } = options;
    console.log('Validating discount code:', discountCode, 'target:', target, 'siteId:', siteId);

    // สร้าง query filter - ค้นหาด้วย code และ target เป็นหลัก, siteId เป็น option
    const filter: any = { code: discountCode };

    // เพิ่ม target ถ้ามี
    if (target) {
      filter.target = target;
    }

    // เพิ่ม siteId ถ้ามี (เป็น option)
    if (siteId) {
      filter.siteId = siteId;
    }

    console.log('Search filter:', filter);
    const discount = await Discount.findOne(filter);
    console.log('Found discount:', discount ? discount._id : 'Not found');

    if (!discount) {
      throw new HttpError(404, 'ไม่พบคูปองส่วนลดนี้');
    }

    // สร้าง orderData object
    const orderData = {
      userId,
      orderAmount,
      items,
      isFirstTime
    };

    // ตรวจสอบว่า discount ยังใช้งานได้หรือไม่
    if (!(discount as any).isValid()) {
      throw new HttpError(400, 'คูปองนี้หมดอายุแล้ว หรือไม่สามารถใช้งานได้');
    }

    // ตรวจสอบเงื่อนไขการใช้งาน
    const canUseResult = (discount as any).canUseForOrder(orderData);
    if (!canUseResult.success) {
      throw new HttpError(400, canUseResult.reason, canUseResult.code);
    }

    // คำนวณส่วนลด
    const discountAmount = (discount as any).calculateDiscount(orderData.orderAmount, orderData.items);

    return {
      success: true,
      message: 'Discount ใช้งานได้',
      data: {
        discount,
        discountAmount,
        finalAmount: orderData.orderAmount - discountAmount
      }
    };
  } catch (err: any) {
    console.error('Error in validateDiscount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะตรวจสอบ discount');
  }
}

export async function applyDiscount(discountId: string, orderId: string, userId: string, discountAmount: number) {
  try {
    const discount = await Discount.findById(discountId);
    if (!discount) {
      throw new HttpError(404, 'ไม่พบ discount');
    }

    // บันทึกการใช้งาน
    await (discount as any).recordUsage({
      userId,
      orderId,
      discountAmount
    });

    return {
      success: true,
      message: 'ใช้ discount สำเร็จ',
      data: discount
    };
  } catch (err: any) {
    console.error('Error in applyDiscount:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะใช้ discount');
  }
}

export async function getDiscountsByStatus(siteId: string, status: DiscountStatus) {
  try {
    const discounts = await Discount.find({ siteId, status }).sort({ createdAt: -1 });
    return discounts;
  } catch (err: any) {
    console.error('Error in getDiscountsByStatus:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง discounts');
  }
}

export async function getDiscountsByType(siteId: string, type: DiscountType) {
  try {
    const discounts = await Discount.find({ siteId, type }).sort({ createdAt: -1 });
    return discounts;
  } catch (err: any) {
    console.error('Error in getDiscountsByType:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง discounts');
  }
}

// Analytics functions
export async function getDiscountStats(siteId: string) {
  try {
    const [totalDiscounts, activeDiscounts, totalUsage, totalDiscountAmount] = await Promise.all([
      Discount.countDocuments({ siteId }),
      Discount.countDocuments({ siteId, status: 'active' }),
      Discount.aggregate([
        { $match: { siteId } },
        { $group: { _id: null, total: { $sum: '$totalUsage' } } }
      ]),
      Discount.aggregate([
        { $match: { siteId } },
        { $group: { _id: null, total: { $sum: '$totalDiscountAmount' } } }
      ])
    ]);

    return {
      totalDiscounts,
      activeDiscounts,
      totalUsage: totalUsage[0]?.total || 0,
      totalDiscountAmount: totalDiscountAmount[0]?.total || 0
    };
  } catch (err: any) {
    console.error('Error in getDiscountStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ discount');
  }
}

export async function getDiscountUsageHistory(discountId: string) {
  try {
    const discount = await Discount.findById(discountId);
    if (!discount) {
      throw new HttpError(404, 'ไม่พบ discount');
    }

    return discount.usageHistory;
  } catch (err: any) {
    console.error('Error in getDiscountUsageHistory:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงประวัติการใช้งาน discount');
  }
}

// ฟังก์ชันเสริมสำหรับการจัดการ discount
export async function getValidDiscounts(siteId?: string) {
  try {
    const discounts = await (Discount as any).getValidDiscounts(siteId);
    return discounts;
  } catch (err: any) {
    console.error('Error in getValidDiscounts:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง valid discounts');
  }
}

export async function updateDiscountStatuses(siteId?: string) {
  try {
    const result = await (Discount as any).updateAllStatuses(siteId);
    return {
      success: true,
      message: 'อัปเดตสถานะ discount สำเร็จ',
      data: result
    };
  } catch (err: any) {
    console.error('Error in updateDiscountStatuses:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะ discount');
  }
}

export async function findApplicableDiscounts(siteId: string, orderData: {
  orderAmount: number;
  items: Array<any>;
  userId: string;
  isFirstTime: boolean;
}) {
  try {
    const discounts = await (Discount as any).findApplicableDiscounts(siteId, orderData);

    // กรองเฉพาะ discount ที่ใช้งานได้จริง
    const validDiscounts = discounts.filter((discount: any) => {
      if (!discount.isValid()) return false;
      const canUseResult = discount.canUseForOrder(orderData);
      return canUseResult.success;
    });

    return validDiscounts;
  } catch (err: any) {
    console.error('Error in findApplicableDiscounts:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะค้นหา applicable discounts');
  }
} 