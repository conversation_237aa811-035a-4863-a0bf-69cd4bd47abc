import { Elysia, t } from "elysia";
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import {
    createInvitation,
    acceptInvitation,
    rejectInvitation,
    getSentInvitations,
    getReceivedInvitations,
    cancelInvitation,
    createBulkInvitations,
    getInvitationById,
    resendInvitation
} from "./invitation.service";
import {
    createInvitationSchema,
    invitationActionSchema,
    bulkInvitationSchema,
    invitationQuerySchema
} from "./invitation.schema";
import { checkSitePermission } from "./role-permission.util";

export const invitationRoutes = new Elysia({ prefix: '/invitations' })
    .use(userAuthPlugin)

    // ดูคำเชิญที่ได้รับ
    .get('/received', async ({ query, user }:any) => {
        const userId = user._id;
        const result = await getReceivedInvitations(userId, query);
        return result;
    }, {
        query: invitationQuerySchema
    })

    // ดูคำเชิญเฉพาะ
    .get('/:invitationId', async ({ params, user }:any) => {
        const { invitationId } = params;
        const userId = user._id;

        const result = await getInvitationById(invitationId, userId);
        return result;
    }, {
        params: t.Object({ invitationId: t.String() })
    })

    // รับคำเชิญ
    .post('/:invitationId/accept', async ({ params, user }:any) => {
        const { invitationId } = params;
        const userId = user._id;

        const result = await acceptInvitation(invitationId, userId);
        return result;
    }, {
        params: t.Object({ invitationId: t.String() })
    })

    // ปฏิเสธคำเชิญ
    .post('/:invitationId/reject', async ({ params, user }:any) => {
        const { invitationId } = params;
        const userId = user._id;

        const result = await rejectInvitation(invitationId, userId);
        return result;
    }, {
        params: t.Object({ invitationId: t.String() })
    })

    // ยกเลิกคำเชิญ
    .delete('/:invitationId', async ({ params, user }:any) => {
        const { invitationId } = params;
        const fromUserId = user._id;

        const result = await cancelInvitation(invitationId, fromUserId);
        return result;
    }, {
        params: t.Object({ invitationId: t.String() })
    })

    // ส่งคำเชิญใหม่
    .post('/:invitationId/resend', async ({ params, user }:any) => {
        const { invitationId } = params;
        const fromUserId = user._id;

        const result = await resendInvitation(invitationId, fromUserId);
        return result;
    }, {
        params: t.Object({ invitationId: t.String() })
    });