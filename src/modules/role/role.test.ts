// ตัวอย่างการทดสอบระบบ Role และ Invitation
// สามารถใช้กับ testing framework ใดก็ได้

import {
    createInvitation,
    acceptInvitation,
    rejectInvitation,
    getSentInvitations,
    getReceivedInvitations
} from './invitation.service';
import { addCustomer, getUserRole } from './role.service';

// Mock data สำหรับทดสอบ
const mockData = {
    siteId: "site123",
    ownerId: "owner123",
    userId: "user456",
    adminId: "admin789"
};

// ตัวอย่างการทดสอบ
export async function testInvitationFlow() {
    try {
        console.log("🧪 เริ่มทดสอบระบบ Invitation...");

        // 1. เพิ่ม owner ให้เว็บไซต์
        console.log("1. เพิ่ม owner...");
        await addCustomer(mockData.siteId, mockData.ownerId, "owner");

        // 2. ส่งคำเชิญ
        console.log("2. ส่งคำเชิญ...");
        const invitation = await createInvitation(
            mockData.siteId,
            mockData.ownerId,
            mockData.userId,
            "editor",
            "เชิญมาช่วยแก้ไขเนื้อหา"
        );
        console.log("✅ ส่งคำเชิญสำเร็จ:", invitation.data._id);

        // 3. ดูคำเชิญที่ส่งออกไป
        console.log("3. ดูคำเชิญที่ส่ง...");
        const sentInvitations = await getSentInvitations(mockData.siteId, mockData.ownerId);
        console.log("✅ คำเชิญที่ส่ง:", sentInvitations.data.length, "รายการ");

        // 4. ดูคำเชิญที่ได้รับ
        console.log("4. ดูคำเชิญที่ได้รับ...");
        const receivedInvitations = await getReceivedInvitations(mockData.userId);
        console.log("✅ คำเชิญที่ได้รับ:", receivedInvitations.data.length, "รายการ");

        // 5. รับคำเชิญ
        console.log("5. รับคำเชิญ...");
        const acceptResult = await acceptInvitation(invitation.data._id, mockData.userId);
        console.log("✅ รับคำเชิญสำเร็จ");

        // 6. ตรวจสอบ role ที่ได้
        console.log("6. ตรวจสอบ role...");
        const userRole = await getUserRole(mockData.siteId, mockData.userId);
        console.log("✅ Role ของผู้ใช้:", userRole.data);

        console.log("🎉 ทดสอบเสร็จสิ้น!");

    } catch (error) {
        console.error("❌ เกิดข้อผิดพลาด:", error.message);
    }
}

// ตัวอย่างการทดสอบกรณี error
export async function testErrorCases() {
    try {
        console.log("🧪 ทดสอบกรณี error...");

        // ทดสอบส่งคำเชิญโดยไม่มีสิทธิ์
        try {
            await createInvitation(mockData.siteId, "noPermissionUser", mockData.userId, "editor");
        } catch (error) {
            console.log("✅ ตรวจจับ error ไม่มีสิทธิ์:", error.message);
        }

        // ทดสอบรับคำเชิญที่ไม่ใช่ของตัวเอง
        try {
            await acceptInvitation("fakeInvitationId", "wrongUser");
        } catch (error) {
            console.log("✅ ตรวจจับ error คำเชิญไม่ใช่ของตัวเอง:", error.message);
        }

        console.log("🎉 ทดสอบ error cases เสร็จสิ้น!");

    } catch (error) {
        console.error("❌ เกิดข้อผิดพลาดในการทดสอบ:", error.message);
    }
}

// เรียกใช้ทดสอบ (uncomment เพื่อใช้งาน)
// testInvitationFlow();
// testErrorCases();