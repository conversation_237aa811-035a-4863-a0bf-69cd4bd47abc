import { t } from "elysia";

// Role types
export const roleTypes = t.Union([
	t.Literal("owner", { error: 'ต้องเป็น owner เท่านั้น' }),
	t.Literal("admin", { error: 'ต้องเป็น admin เท่านั้น' }),
	t.Literal("editor", { error: 'ต้องเป็น editor เท่านั้น' }),
	t.Literal("viewer", { error: 'ต้องเป็น viewer เท่านั้น' }),
], { error: 'ประเภท role ไม่ถูกต้อง' });

// Role validation schemas
export const siteIdQuery = t.Object({ 
  siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
  page: t.Optional(t.Number({ default: 1, error: 'page ต้องเป็นตัวเลข' })),
  limit: t.Optional(t.Number({ default: 10, error: 'limit ต้องเป็นตัวเลข' })),
});
export const roleBody = t.Object({
	siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
	userId: t.String({ error: 'userId ต้องเป็นข้อความ' }),
	role: t.Union([
		t.Literal("owner", { error: 'ต้องเป็น owner เท่านั้น' }),
		t.Literal("admin", { error: 'ต้องเป็น admin เท่านั้น' }),
		t.Literal("editor", { error: 'ต้องเป็น editor เท่านั้น' }),
		t.Literal("viewer", { error: 'ต้องเป็น viewer เท่านั้น' }),
	], { error: 'ประเภท role ไม่ถูกต้อง' }),
});
export const updateRoleBody = t.Object({
	siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
	role: t.Union([
		t.Literal("owner", { error: 'ต้องเป็น owner เท่านั้น' }),
		t.Literal("admin", { error: 'ต้องเป็น admin เท่านั้น' }),
		t.Literal("editor", { error: 'ต้องเป็น editor เท่านั้น' }),
		t.Literal("viewer", { error: 'ต้องเป็น viewer เท่านั้น' }),
	], { error: 'ประเภท role ไม่ถูกต้อง' }),
});

  
// Transfer owner body schema
export const transferOwnerBodySchema = t.Object({
	siteId: t.String({
		minLength: 1,
		description: "Site identifier",
		error: 'siteId ต้องเป็นข้อความและไม่ว่าง'
	}),
	fromUserId: t.String({
		minLength: 1,
		description: "Current owner user ID",
		error: 'fromUserId ต้องเป็นข้อความและไม่ว่าง'
	}),
	toUserId: t.String({
		minLength: 1,
		description: "New owner user ID",
		error: 'toUserId ต้องเป็นข้อความและไม่ว่าง'
	}),
});

// Alternative transfer owner schema
export const transferOwnerAltSchema = t.Object({
	siteId: t.String({
		minLength: 1,
		description: "Site identifier",
		error: 'siteId ต้องเป็นข้อความและไม่ว่าง'
	}),
	newOwnerId: t.String({
		minLength: 1,
		description: "New owner user ID",
		error: 'newOwnerId ต้องเป็นข้อความและไม่ว่าง'
	}),
});

// Response schemas
export const roleResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.Optional(t.String({ error: 'message ต้องเป็นข้อความ' })),
	data: t.Optional(t.Any()),
});

export const rolesListResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	data: t.Array(t.Any()),
});

export const messageResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
});

export const paginationQuery = t.Object({
  page: t.Optional(t.String({ error: 'page ต้องเป็นข้อความ' })),
  limit: t.Optional(t.String({ error: 'limit ต้องเป็นข้อความ' })),
});
