import { config } from '@/core/config/environment';

export interface LogContext {
  userId?: string;
  requestId?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  duration?: number;
  error?: Error | string;
  [key: string]: any;
}

export class Logger {
  private static instance: Logger;
  private isDev: boolean;

  private constructor() {
    this.isDev = config.isDev;
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...context,
    };

    if (this.isDev) {
      // Pretty format for development
      return `[${timestamp}] ${level.toUpperCase()}: ${message}${
        context ? ` | ${JSON.stringify(context, null, 2)}` : ''
      }`;
    }

    // JSON format for production
    return JSON.stringify(logEntry);
  }

  public info(message: string, context?: LogContext): void {
    console.log(this.formatMessage('info', message, context));
  }

  public error(message: string, context?: LogContext): void {
    console.error(this.formatMessage('error', message, context));
  }

  public warn(message: string, context?: LogContext): void {
    console.warn(this.formatMessage('warn', message, context));
  }

  public debug(message: string, context?: LogContext): void {
    if (this.isDev) {
      console.debug(this.formatMessage('debug', message, context));
    }
  }

  public http(message: string, context?: LogContext): void {
    console.log(this.formatMessage('http', message, context));
  }
}

// Export singleton instance
export const logger = Logger.getInstance();