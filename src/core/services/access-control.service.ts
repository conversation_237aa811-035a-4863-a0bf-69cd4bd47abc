import { Site } from '@/modules/site/site.model';
import { Customer } from '@/modules/customer/customer.model';
import { logger } from '@/core/utils/logger';
import { Role } from '@/modules/role';

// Base interfaces
export interface BaseAccessResult {
    hasAccess: boolean;
    role?: string;
    permissions?: string[];
}

export interface UserAccessResult extends BaseAccessResult {
    isOwner?: boolean;
}

export interface CustomerAccessResult extends BaseAccessResult {
    customerId?: string;
    isCustomer?: boolean;
}

export interface AccessCheckOptions {
    requiredRole?: string;
    requiredPermissions?: string[];
    allowRoles?: string[];
    requireAccess?: boolean;
}

export class AccessControlService {
    /**
     * ตรวจสอบสิทธิ์ของ User ในไซต์
     */
    static async checkUserAccess(siteId: string, userId: string): Promise<UserAccessResult> {
        try {
            // ตรวจสอบว่าเป็น owner หรือไม่
            const role = await Role.findOne({ siteId, userId }).select('role');

            if (role) {
                return {
                    hasAccess: true,
                    role: role.role,
                    permissions: this.getPermissionsByRole(role.role),
                    isOwner: role.role === 'owner'
                };
            }

            // TODO: ตรวจสอบ role อื่นๆ เช่น admin, editor, viewer
            // const siteUser = await SiteUser.findOne({ siteId, userId });
            // if (siteUser) {
            //   return {
            //     hasAccess: true,
            //     role: siteUser.role,
            //     permissions: this.getPermissionsByRole(siteUser.role),
            //     isOwner: false
            //   };
            // }

            return {
                hasAccess: false,
                role: undefined,
                permissions: [],
                isOwner: false
            };
        } catch (error) {
            logger.warn('User access check failed', {
                siteId,
                userId,
                error: error instanceof Error ? error.message : String(error)
            });

            return {
                hasAccess: false,
                role: undefined,
                permissions: [],
                isOwner: false
            };
        }
    }

    /**
     * ตรวจสอบสิทธิ์ของ Customer ในไซต์
     */
    static async checkCustomerAccess(siteId: string, customerId: string): Promise<CustomerAccessResult> {
        try {
            // TODO: ตรวจสอบ customer จาก database
            // const siteCustomer = await SiteCustomer.findOne({ siteId, customerId });
            // if (siteCustomer) {
            //   return {
            //     hasAccess: true,
            //     customerId: siteCustomer._id,
            //     role: siteCustomer.role,
            //     permissions: this.getPermissionsByRole(siteCustomer.role),
            //     isCustomer: true
            //   };
            // }

            // สำหรับตอนนี้ให้ return false
            return {
                hasAccess: false,
                customerId: undefined,
                role: undefined,
                permissions: [],
                isCustomer: false
            };
        } catch (error) {
            logger.warn('Customer access check failed', {
                siteId,
                customerId,
                error: error instanceof Error ? error.message : String(error)
            });

            return {
                hasAccess: false,
                customerId: undefined,
                role: undefined,
                permissions: [],
                isCustomer: false
            };
        }
    }

    /**
     * ตรวจสอบ permission เฉพาะ
     */
    static async checkPermission(
        siteId: string,
        userId: string,
        requiredPermission: string,
        type: 'user' | 'customer' = 'user'
    ): Promise<boolean> {
        const accessResult = type === 'user'
            ? await AccessControlService.checkUserAccess(siteId, userId)
            : await AccessControlService.checkCustomerAccess(siteId, userId);

        if (!accessResult.hasAccess) {
            return false;
        }

        return accessResult.permissions?.includes(requiredPermission) || false;
    }

    /**
     * ตรวจสอบ role และ permissions ตาม options
     */
    static validateAccess(
        accessResult: BaseAccessResult,
        options: AccessCheckOptions = {}
    ): { isValid: boolean; error?: string } {
        if (!accessResult.hasAccess) {
            return { isValid: false, error: 'ไม่มีสิทธิ์เข้าถึง' };
        }

        // ตรวจสอบ required role
        if (options.requiredRole && accessResult.role !== options.requiredRole) {
            return { isValid: false, error: `ต้องมี role: ${options.requiredRole}` };
        }

        // ตรวจสอบ allow roles
        if (options.allowRoles && !options.allowRoles.includes(accessResult.role || '')) {
            return {
                isValid: false,
                error: `ต้องมี role อย่างน้อยหนึ่งใน: ${options.allowRoles.join(', ')}`
            };
        }

        // ตรวจสอบ required permissions
        if (options.requiredPermissions) {
            const hasAllPermissions = options.requiredPermissions.every(permission =>
                accessResult.permissions?.includes(permission)
            );

            if (!hasAllPermissions) {
                return {
                    isValid: false,
                    error: `ต้องมี permissions: ${options.requiredPermissions.join(', ')}`
                };
            }
        }

        return { isValid: true };
    }

    /**
     * Helper functions
     */
    static async isSiteOwner(siteId: string, userId: string): Promise<boolean> {
        const accessResult = await AccessControlService.checkUserAccess(siteId, userId);
        return (accessResult as UserAccessResult).isOwner || false;
    }

    static async isSiteAdmin(siteId: string, userId: string): Promise<boolean> {
        const accessResult = await AccessControlService.checkUserAccess(siteId, userId);
        return accessResult.role === 'admin' || (accessResult as UserAccessResult).isOwner || false;
    }

    static async isSiteCustomer(siteId: string, customerId: string): Promise<boolean> {
        const accessResult = await AccessControlService.checkCustomerAccess(siteId, customerId);
        return (accessResult as CustomerAccessResult).isCustomer || false;
    }

    /**
     * Get permissions by role (TODO: implement based on your role system)
     */
    private static getPermissionsByRole(role: string): string[] {
        const rolePermissions: Record<string, string[]> = {
            owner: ['read', 'write', 'delete', 'admin'],
            admin: ['read', 'write', 'delete'],
            editor: ['read', 'write'],
            viewer: ['read']
        };

        return rolePermissions[role] || [];
    }
}