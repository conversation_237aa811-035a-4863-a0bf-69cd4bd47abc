import { logger } from '@/core/utils/logger';

/**
 * ✅ Token Blacklist Service
 * จัดการ token ที่ถูก revoke หรือ logout
 */

interface BlacklistedToken {
    token: string;
    expiresAt: Date;
    reason: 'logout' | 'security' | 'expired' | 'refresh';
    userId?: string;
    createdAt: Date;
}

/**
 * In-memory token blacklist (ในการใช้งานจริงควรใช้ Redis หรือ Database)
 */
class TokenBlacklistService {
    private blacklist = new Map<string, BlacklistedToken>();
    private cleanupInterval: NodeJS.Timeout;

    constructor() {
        // ทำความสะอาด expired tokens ทุก 1 ชั่วโมง
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 60 * 60 * 1000);
    }

    /**
     * เพิ่ม token ลง blacklist
     */
    async blacklistToken(
        token: string, 
        expiresAt: Date, 
        reason: BlacklistedToken['reason'],
        userId?: string
    ): Promise<void> {
        const tokenHash = this.hashToken(token);
        
        this.blacklist.set(tokenHash, {
            token: tokenHash,
            expiresAt,
            reason,
            userId,
            createdAt: new Date()
        });

        logger.info('Token blacklisted', {
            tokenHash: tokenHash.substring(0, 10) + '...',
            reason,
            userId,
            expiresAt
        });
    }

    /**
     * ตรวจสอบว่า token อยู่ใน blacklist หรือไม่
     */
    async isBlacklisted(token: string): Promise<boolean> {
        const tokenHash = this.hashToken(token);
        const blacklistedToken = this.blacklist.get(tokenHash);
        
        if (!blacklistedToken) {
            return false;
        }

        // ตรวจสอบว่า token หมดอายุแล้วหรือไม่
        if (blacklistedToken.expiresAt < new Date()) {
            this.blacklist.delete(tokenHash);
            return false;
        }

        return true;
    }

    /**
     * Blacklist ทุก token ของ user (เมื่อ logout all devices)
     */
    async blacklistAllUserTokens(userId: string, reason: BlacklistedToken['reason'] = 'security'): Promise<void> {
        // ในการใช้งานจริง ควรเก็บ mapping ระหว่าง userId กับ active tokens
        // และ blacklist ทั้งหมด
        
        logger.info('All user tokens blacklisted', {
            userId,
            reason
        });
    }

    /**
     * ลบ token ออกจาก blacklist (ใช้เมื่อต้องการ whitelist token)
     */
    async removeFromBlacklist(token: string): Promise<void> {
        const tokenHash = this.hashToken(token);
        this.blacklist.delete(tokenHash);
        
        logger.info('Token removed from blacklist', {
            tokenHash: tokenHash.substring(0, 10) + '...'
        });
    }

    /**
     * ทำความสะอาด expired tokens
     */
    private cleanup(): void {
        const now = new Date();
        let cleanedCount = 0;

        for (const [tokenHash, blacklistedToken] of this.blacklist.entries()) {
            if (blacklistedToken.expiresAt < now) {
                this.blacklist.delete(tokenHash);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            logger.debug('Token blacklist cleanup completed', {
                cleanedCount,
                remainingCount: this.blacklist.size
            });
        }
    }

    /**
     * Hash token สำหรับความปลอดภัย
     */
    private hashToken(token: string): string {
        // ใช้ simple hash สำหรับ demo (ในการใช้งานจริงควรใช้ crypto hash)
        return Buffer.from(token).toString('base64').substring(0, 32);
    }

    /**
     * ดูสถิติ blacklist
     */
    getStats(): {
        totalBlacklisted: number;
        byReason: Record<string, number>;
    } {
        const stats = {
            totalBlacklisted: this.blacklist.size,
            byReason: {} as Record<string, number>
        };

        for (const blacklistedToken of this.blacklist.values()) {
            stats.byReason[blacklistedToken.reason] = (stats.byReason[blacklistedToken.reason] || 0) + 1;
        }

        return stats;
    }

    /**
     * ปิด service และ cleanup
     */
    destroy(): void {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.blacklist.clear();
    }
}

// Singleton instance
export const tokenBlacklistService = new TokenBlacklistService();

/**
 * Middleware สำหรับตรวจสอบ blacklisted tokens
 */
export async function checkTokenBlacklist(token: string): Promise<boolean> {
    return await tokenBlacklistService.isBlacklisted(token);
}

/**
 * Helper function สำหรับ blacklist token เมื่อ logout
 */
export async function blacklistTokenOnLogout(token: string, userId?: string): Promise<void> {
    // คำนวณ expiration time จาก token (ในการใช้งานจริงควร decode JWT)
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 ชั่วโมง
    
    await tokenBlacklistService.blacklistToken(token, expiresAt, 'logout', userId);
}

/**
 * Helper function สำหรับ blacklist token เมื่อมีปัญหาความปลอดภัย
 */
export async function blacklistTokenForSecurity(token: string, userId?: string): Promise<void> {
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 ชั่วโมง
    
    await tokenBlacklistService.blacklistToken(token, expiresAt, 'security', userId);
}

/**
 * Helper function สำหรับ blacklist ทุก token ของ user
 */
export async function blacklistAllUserTokens(userId: string): Promise<void> {
    await tokenBlacklistService.blacklistAllUserTokens(userId, 'security');
}
