// Export checkUser with explicit naming to avoid conflicts
export {
    checkUser<PERSON><PERSON>,
    checkSiteAccess,
    getResultUser,
    isSiteOwner,
    isSiteAdmin,
    checkRoleSite,
    checkUser,
    userPlugin,
    type RoleCheckResult,
    type RoleCheckOptions
} from './checkUser';

// Export checkPermission from checkUser explicitly (preferred implementation)
export { checkPermission } from './checkUser';

// Export checkSite
export * from './checkSite';

// Export checkMember with explicit naming to avoid conflicts
export {
    checkCustomer,
    checkSiteCustomer,
    isSiteCustomerWithRole,
    customerPlugin,
    type CustomerCheckResult,
    type CustomerCheckOptions
} from './checkMember';

// Export isSiteCustomer from checkMember explicitly (preferred implementation)
export { isSiteCustomer } from './checkMember';

// Export new unified auth system
export * from './unified-auth';

// Export services
export * from '../services/jwt.service';
export * from '../services/access-control.service';

// Export schemas
export * from '../schemas/response.schema';

// Export auth plugins (legacy)
export * from '../plugins/auth';