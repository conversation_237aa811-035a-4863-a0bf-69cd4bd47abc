import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { serverTiming } from '@elysiajs/server-timing';
import { rateLimit } from 'elysia-rate-limit';
import { staticPlugin } from '@elysiajs/static';
import { bearer } from '@elysiajs/bearer';
import { helmet } from 'elysia-helmet';
import { Logestic } from 'logestic';
import { config } from '@/core/config/environment';
import { getRateLimitConfig } from '@/core/config/rate-limit';
import { logger } from '@/core/utils/logger';
import { publicCSRFProtection } from '@/core/middleware/csrf';

/**
 * Security plugins (helmet, cors, rate limiting, CSRF protection)
 */
export function setupSecurityPlugins(app: Elysia) {
    return app
        .use(
            helmet({
                crossOriginResourcePolicy: {
                    policy: 'cross-origin'
                }
            })
        )
        .use(bearer())
        .use(publicCSRFProtection) // ✅ เพิ่ม CSRF protection
        .use(
            rateLimit({
                ...getRateLimitConfig(config.NODE_ENV),
                generator: (request) => {
                    // Use IP address as the default identifier
                    return request.headers.get('x-forwarded-for') ||
                        request.headers.get('cf-connecting-ip') ||
                        'unknown';
                }
            })
        )
        .use(
            cors({
                exposeHeaders: ['Content-Range', 'X-Pagination', 'X-Response-Time'],
                methods: ['GET', 'PUT', 'DELETE', 'POST', 'OPTIONS', 'PATCH'],
                maxAge: 3600,
                origin: true,
                credentials: true,
                allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token'], // ✅ เพิ่ม CSRF header
            })
        );
}

/**
 * Utility plugins (static files, server timing, logging)
 */
export function setupUtilityPlugins(app: Elysia) {
    return app
        .use(Logestic.preset('fancy'))
        .use(staticPlugin())
        .use(serverTiming());
}

/**
 * Request/Response logging middleware
 */
export function setupLoggingMiddleware(app: Elysia) {
    return app
        .onRequest(({ request }) => {
            const startTime = Date.now();
            const ip = request.headers.get('x-forwarded-for') ||
                request.headers.get('cf-connecting-ip') ||
                'unknown';
            const userAgent = request.headers.get('user-agent') || 'unknown';

            logger.http('Incoming request', {
                method: request.method,
                url: request.url,
                ip,
                userAgent,
                timestamp: new Date().toISOString()
            });

            // Store start time for response logging
            (request as any).startTime = startTime;
        })
        .onAfterResponse(({ request, set }) => {
            const duration = Date.now() - ((request as any).startTime || Date.now());
            const ip = request.headers.get('x-forwarded-for') ||
                request.headers.get('cf-connecting-ip') ||
                'unknown';

            logger.http('Request completed', {
                method: request.method,
                url: request.url,
                ip,
                statusCode: set.status || 200,
                duration,
                timestamp: new Date().toISOString()
            });
        });
}

/**
 * Setup all plugins in the correct order
 */
export function setupAllPlugins(app: Elysia) {
    return setupLoggingMiddleware(
        setupUtilityPlugins(
            setupSecurityPlugins(app)
        )
    );
}