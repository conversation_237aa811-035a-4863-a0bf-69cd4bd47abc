# คู่มือการใช้ Disabled Styles ใน Tailwind CSS

## ❌ **ไม่ถูกต้อง:**
```html
<!-- Syntax ผิด -->
<input class="disable:!ring-0" />

<!-- Typo -->
<button class="disable:opacity-50" />
```

## ✅ **ถูกต้อง:**

### 1. **Basic Disabled Styles:**
```html
<input class="disabled:ring-0 disabled:bg-gray-100" disabled />
<button class="disabled:opacity-50 disabled:cursor-not-allowed" disabled />
```

### 2. **Complete Disabled State:**
```html
<input class="
  ring-2 ring-blue-500 
  focus:ring-blue-600 
  disabled:ring-0 
  disabled:bg-gray-100 
  disabled:text-gray-400
  disabled:cursor-not-allowed
  disabled:opacity-60
" />
```

### 3. **Button Disabled State:**
```html
<button class="
  bg-blue-500 hover:bg-blue-600 
  text-white font-medium py-2 px-4 rounded
  disabled:bg-gray-300 
  disabled:text-gray-500
  disabled:cursor-not-allowed
  disabled:hover:bg-gray-300
" disabled>
  Submit
</button>
```

### 4. **ใช้ Important เมื่อจำเป็น:**
```html
<!-- เมื่อมี CSS อื่นที่ override -->
<input class="disabled:!ring-0 disabled:!bg-gray-100" disabled />
```

## 🎨 **ตัวอย่างใน Svelte Component:**

### **Input Component:**
```svelte
<script>
  export let disabled = false;
  export let value = '';
</script>

<input 
  bind:value
  {disabled}
  class="
    w-full px-3 py-2 border border-gray-300 rounded-md
    focus:outline-none focus:ring-2 focus:ring-blue-500
    disabled:bg-gray-100 
    disabled:text-gray-400
    disabled:cursor-not-allowed
    disabled:ring-0
    disabled:border-gray-200
  "
/>
```

### **Button Component:**
```svelte
<script>
  export let disabled = false;
  export let loading = false;
  export let variant = 'primary';
  
  $: isDisabled = disabled || loading;
</script>

<button 
  disabled={isDisabled}
  class="
    px-4 py-2 rounded-md font-medium transition-colors
    {variant === 'primary' ? 'bg-blue-500 hover:bg-blue-600 text-white' : ''}
    disabled:bg-gray-300 
    disabled:text-gray-500
    disabled:cursor-not-allowed
    disabled:hover:bg-gray-300
    disabled:transform-none
  "
>
  {#if loading}
    <span class="animate-spin">⏳</span>
  {/if}
  <slot />
</button>
```

## 🔧 **Best Practices:**

### 1. **ใช้ Semantic HTML:**
```html
<!-- ✅ ดี -->
<button disabled>Submit</button>
<input disabled />

<!-- ❌ หลีกเลี่ยง -->
<div class="disabled:opacity-50">Fake Button</div>
```

### 2. **Accessibility:**
```html
<button 
  disabled={isLoading}
  aria-disabled={isLoading}
  aria-label={isLoading ? 'Loading...' : 'Submit form'}
>
  {isLoading ? 'Loading...' : 'Submit'}
</button>
```

### 3. **Visual Feedback:**
```html
<button class="
  bg-blue-500 text-white
  disabled:bg-gray-300 
  disabled:text-gray-500
  disabled:cursor-not-allowed
  transition-all duration-200
">
  Submit
</button>
```

## 📊 **Tailwind Disabled Modifiers:**

| Class | Description |
|-------|-------------|
| `disabled:bg-gray-100` | Background สีเทาเมื่อ disabled |
| `disabled:text-gray-400` | Text สีเทาเมื่อ disabled |
| `disabled:cursor-not-allowed` | Cursor แสดง not-allowed |
| `disabled:opacity-50` | ความโปร่งใส 50% |
| `disabled:ring-0` | ลบ ring เมื่อ disabled |
| `disabled:border-gray-200` | Border สีเทาอ่อน |
| `disabled:hover:bg-gray-300` | Hover state เมื่อ disabled |

## 🚫 **สิ่งที่ควรหลีกเลี่ยง:**

1. **Typo ใน modifier:**
   ```html
   <!-- ❌ ผิด -->
   <input class="disable:ring-0" />
   
   <!-- ✅ ถูก -->
   <input class="disabled:ring-0" />
   ```

2. **ใช้ !important มากเกินไป:**
   ```html
   <!-- ❌ หลีกเลี่ยง -->
   <input class="disabled:!bg-gray-100 disabled:!text-gray-400 disabled:!ring-0" />
   
   <!-- ✅ ดีกว่า -->
   <input class="disabled:bg-gray-100 disabled:text-gray-400 disabled:ring-0" />
   ```

3. **ลืม cursor state:**
   ```html
   <!-- ❌ ไม่สมบูรณ์ -->
   <button class="disabled:opacity-50">Submit</button>
   
   <!-- ✅ สมบูรณ์ -->
   <button class="disabled:opacity-50 disabled:cursor-not-allowed">Submit</button>
   ```