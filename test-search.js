// Test script สำหรับทดสอบ Search API
const BASE_URL = 'http://localhost:5000/v1';

async function testSearchAPI() {
    console.log('🧪 Testing Search API...\n');

    try {
        // 1. Test basic search
        console.log('1. Testing basic search...');
        const searchResponse = await fetch(`${BASE_URL}/search?q=test&page=1&limit=5`);
        const searchData = await searchResponse.json();
        console.log('✅ Basic search:', searchData.success ? 'PASS' : 'FAIL');

        // 2. Test health check
        console.log('\n2. Testing health check...');
        const healthResponse = await fetch(`${BASE_URL}/search/health`);
        const healthData = await healthResponse.json();
        console.log('✅ Health check:', healthData.success ? 'PASS' : 'FAIL');
        console.log('   Features:', healthData.data?.features);

        // 3. Test suggestions
        console.log('\n3. Testing suggestions...');
        const suggestionsResponse = await fetch(`${BASE_URL}/search/suggestions?q=test&limit=5`);
        const suggestionsData = await suggestionsResponse.json();
        console.log('✅ Suggestions:', suggestionsData.success ? 'PASS' : 'FAIL');

        // 4. Test enhanced search
        console.log('\n4. Testing enhanced search...');
        const enhancedResponse = await fetch(`${BASE_URL}/search/enhanced?q=test&userId=test-user`);
        const enhancedData = await enhancedResponse.json();
        console.log('✅ Enhanced search:', enhancedData.success ? 'PASS' : 'FAIL');

        console.log('\n🎉 All tests completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('\n💡 Make sure the server is running: bun run dev');
    }
}

// Run tests
testSearchAPI();