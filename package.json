{"name": "backend-elysia", "version": "1.0.50", "scripts": {"test": "bun test", "dev": "bun run --watch src/index.ts", "format": "bunx biome format --write", "lint": "bunx biome lint --write", "check": "bunx biome check --write"}, "dependencies": {"@elysiajs/bearer": "^1.3.0", "@elysiajs/cors": "^1.3.3", "@elysiajs/jwt": "^1.3.2", "@elysiajs/server-timing": "^1.3.0", "@elysiajs/static": "^1.3.0", "@tqman/nice-logger": "^1.1.1", "cloudinary": "^2.7.0", "elysia": "^1.3.6", "elysia-fault": "^1.0.7", "elysia-helmet": "^3.0.0", "elysia-rate-limit": "^4.4.0", "fastest-levenshtein": "^1.0.16", "ioredis": "^5.6.1", "jose": "^6.0.12", "logestic": "^1.2.4", "logixlysia": "^5.1.0", "mongoose": "^8.16.5", "nanoid": "^5.1.5", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "sharp": "^0.34.3", "zod": "^4.0.13"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@types/nodemailer": "^6.4.17", "bun-types": "^1.2.19"}, "module": "src/index.js"}