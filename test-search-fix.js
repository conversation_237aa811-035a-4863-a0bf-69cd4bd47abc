// Test script สำหรับทดสอบการแก้ไข search function
const { search } = require('./src/modules/search/search.service.ts');

async function testSearch() {
    try {
        console.log('Testing search with fixed query...');

        // ทดสอบด้วย siteId ที่ถูกต้อง (ไม่ใช่ null)
        const testSiteId = 'test-site-123';
        const testQuery = 'test';

        const result = await search(testSiteId, testQuery, {}, {}, 1, 20);

        console.log('Search result:', {
            total: result.pagination.total,
            returned: result.results.length,
            query: result.query
        });

    } catch (error) {
        console.error('Search test error:', error.message);
    }
}

// เรียกใช้ test
testSearch();