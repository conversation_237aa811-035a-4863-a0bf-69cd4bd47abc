# Logger Configuration

## Environment Variables

### Log Levels
```bash
# Enable/disable different log levels
VITE_LOG_DEBUG=true      # Debug logs (default: true in dev)
VITE_LOG_INFO=true       # Info logs (default: true in dev)
VITE_LOG_WARN=true       # Warning logs (default: true)
VITE_LOG_ERROR=true      # Error logs (default: true)
VITE_LOG_CRITICAL=true   # Critical logs (default: true)
```

### Log Categories
```bash
# Enable/disable specific log categories
VITE_LOG_AUTH=true       # Authentication logs (default: true in dev)
VITE_LOG_API=true        # API logs (default: true in dev)
VITE_LOG_SECURITY=true   # Security logs (default: true)
VITE_LOG_PERFORMANCE=true # Performance logs (default: true in dev)
VITE_LOG_USER=true       # User action logs (default: true in dev)
VITE_LOG_SYSTEM=true     # System logs (default: true in dev)
```

### Console Output Settings
```bash
# Console output configuration
VITE_LOG_CONSOLE=true    # Enable console output (default: true in dev)
VITE_LOG_TIMESTAMP=true  # Show timestamps (default: true in dev)
VITE_LOG_CATEGORY=true   # Show categories (default: true in dev)
```

### File Output Settings
```bash
# File output configuration
VITE_LOG_FILE=false      # Enable file output (default: false)
VITE_LOG_MAX_SIZE=10     # Max file size in MB (default: 10)
VITE_LOG_MAX_FILES=5     # Max number of files (default: 5)
```

## Usage Examples

### ปิด info logs ทั้งหมด
```bash
VITE_LOG_INFO=false
```

### ปิดเฉพาะ auth logs
```bash
VITE_LOG_AUTH=false
```

### ปิด console output
```bash
VITE_LOG_CONSOLE=false
```

### เปิด file logging
```bash
VITE_LOG_FILE=true
VITE_LOG_MAX_SIZE=50
VITE_LOG_MAX_FILES=10
```

## Default Behavior

- **Development**: Most logs enabled
- **Production**: Only critical logs enabled by default
- **Security logs**: Always enabled
- **Error/Warn logs**: Always enabled

## Code Usage

```typescript
import { logger, LogCategory } from '$lib/utils/logger';
import { isLoggingEnabled } from '$lib/config';

// Check if logging is enabled
if (isLoggingEnabled('info', 'auth')) {
  logger.info(LogCategory.AUTH, 'user_login', 'User logged in');
}

// Direct logging (will be filtered automatically)
logger.info(LogCategory.AUTH, 'user_login', 'User logged in');
logger.debug(LogCategory.PERFORMANCE, 'api_call', 'API call completed');
logger.warn(LogCategory.SECURITY, 'invalid_token', 'Invalid token detected');
logger.error(LogCategory.SYSTEM, 'database_error', 'Database connection failed');
``` 