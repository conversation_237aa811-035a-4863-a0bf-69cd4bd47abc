import { BaseService } from './base';
import { apiUrl } from '$lib/config';
import type { ThemeSettings, ThemeResponse, ThemeListResponse } from '$lib/types';
import type { ApiResponse } from '$lib/types/common';

export class ThemeService extends BaseService {
  async getTheme(siteId: string, token: string): Promise<ApiResponse<ThemeSettings>> {
    // ใช้ mock endpoint สำหรับ testing
    return this.makeAuthenticatedRequest(`/site/mock-site/theme`, token);
  }

  async createTheme(themeData: ThemeSettings, token: string): Promise<ApiResponse<ThemeSettings>> {
    return this.makeAuthenticatedRequest(`/site/${themeData.siteId}/theme`, token, {
      method: 'PUT',
      body: JSON.stringify(themeData)
    });
  }

  async updateTheme(themeData: ThemeSettings, token: string): Promise<ApiResponse<ThemeSettings>> {
    return this.makeAuthenticatedRequest(`/site/${themeData.siteId}/theme`, token, {
      method: 'PUT',
      body: JSON.stringify(themeData)
    });
  }

  async saveTheme(themeData: ThemeSettings, token: string): Promise<ApiResponse<ThemeSettings>> {
    // ใช้ PUT endpoint สำหรับ upsert
    return this.makeAuthenticatedRequest(`/site/${themeData.siteId}/theme`, token, {
      method: 'PUT',
      body: JSON.stringify(themeData)
    });
  }

  async deleteTheme(themeId: string, token: string): Promise<ApiResponse<void>> {
    // สำหรับ theme settings ใน site model ไม่มี delete แยก
    // ให้ reset เป็นค่าเริ่มต้นแทน
    return this.makeAuthenticatedRequest(`/site/${themeId}/theme/reset`, token, {
      method: 'POST'
    });
  }

  async getDefaultTheme(): Promise<ThemeSettings> {
    return {
      siteId: '',
      layout: 'grid',
      headerStyle: 'centered',
      footerStyle: 'simple',
      showSearch: true,
      showLanguageSelector: true,
      showThemeToggle: true,
      mobileMenuStyle: 'slide',
      desktopMenuStyle: 'horizontal',
      primaryColor: '#3b82f6',
      secondaryColor: '#6b7280',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      accentColor: '#f59e0b',
      fontFamily: 'Inter',
      fontSize: '16px',
      lineHeight: '1.6',
      fontWeight: '400',
      containerPadding: '1rem',
      sectionSpacing: '2rem',
      elementSpacing: '1rem',
      borderRadius: '0.5rem',
      spacing: '1rem',
      customCSS: ''
    };
  }

  generateCSS(theme: ThemeSettings): string {
    return `
:root {
    --primary-color: ${theme.primaryColor || '#3b82f6'};
    --secondary-color: ${theme.secondaryColor || '#6b7280'};
    --background-color: ${theme.backgroundColor || '#ffffff'};
    --text-color: ${theme.textColor || '#1f2937'};
    --accent-color: ${theme.accentColor || '#f59e0b'};
    --font-family: ${theme.fontFamily || 'Inter'}, sans-serif;
    --font-size: ${theme.fontSize || '16px'};
    --line-height: ${theme.lineHeight || '1.6'};
    --font-weight: ${theme.fontWeight || '400'};
    --container-padding: ${theme.containerPadding || '1rem'};
    --section-spacing: ${theme.sectionSpacing || '2rem'};
    --element-spacing: ${theme.elementSpacing || '1rem'};
    --border-radius: ${theme.borderRadius || '0.5rem'};
    --spacing: ${theme.spacing || '1rem'};
}

/* Layout styles */
.layout-${theme.layout || 'grid'} {
    display: ${theme.layout === 'grid' ? 'grid' : 'block'};
}

/* Header styles */
.header-${theme.headerStyle || 'centered'} {
    text-align: ${theme.headerStyle === 'centered' ? 'center' : 'left'};
}

/* Footer styles */
.footer-${theme.footerStyle || 'simple'} {
    padding: ${theme.footerStyle === 'detailed' ? '2rem' : '1rem'};
}

/* Mobile menu styles */
.mobile-menu-${theme.mobileMenuStyle || 'slide'} {
    transition: ${theme.mobileMenuStyle === 'slide' ? 'transform 0.3s ease' : 'opacity 0.3s ease'};
}

/* Responsive design */
@media (max-width: 768px) {
    :root {
        --container-padding: calc(${theme.containerPadding || '1rem'} * 0.5);
        --section-spacing: calc(${theme.sectionSpacing || '2rem'} * 0.75);
    }
}

/* Custom CSS */
${theme.customCSS || ''}
        `.trim();
  }
}

export const themeService = new ThemeService(); 