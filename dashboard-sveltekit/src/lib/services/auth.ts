import { BaseService } from './base';
import { Response<PERSON>elper, type StandardResponse } from '$lib/types/response';
import {
    validateForgotPasswordData,
    validateResetPasswordData,
    sanitizeAuthData,
    type SigninData,
    type SignupData,
    type ForgotPasswordData,
    type ResetPasswordData,
    type VerifyEmailData,
    type AuthResponse,
    type RefreshTokenResponse
} from '$lib/schemas/auth.schema';
import type { User } from '$lib/types/user';
import type { ApiResponse } from '$lib/types/common';

// Type aliases เพื่อลดความซับซ้อน
type AuthData = {
    user: User;
    token: string;
    refreshToken: string;
    sessionId?: string;
};

type AuthApiResponse = {
    data: AuthData;
    message?: string;
};

/**
 * ✅ Auth Service - ใช้ BaseService และ ofetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
class AuthService extends BaseService {

    /**
     * ✅ Sign in user - ไม่ validate ซ้ำ เพราะ server action validate แล้ว
     */
    async signin(credentials: SigninData): Promise<AuthResponse> {
        // Sanitize เท่านั้น ไม่ validate ซ้ำ
        const sanitizedData = sanitizeAuthData(credentials);

        return this.handleRequest(async () => {
            const result = await this.makePublicRequest<AuthApiResponse>('/user/signin', {
                method: 'POST',
                body: sanitizedData
            });

            return result;
        }, 'เข้าสู่ระบบสำเร็จ');
    }

    /**
     * ✅ Sign up new user - ไม่ validate ซ้ำ เพราะ server action validate แล้ว
     */
    async signup(credentials: SignupData): Promise<AuthResponse> {
        // Sanitize เท่านั้น ไม่ validate ซ้ำ
        const sanitizedData = sanitizeAuthData(credentials);

        return this.handleRequest(async () => {
            const result = await this.makePublicRequest<AuthApiResponse>('/user/signup', {
                method: 'POST',
                body: sanitizedData
            });

            return result;
        }, 'ลงทะเบียนสำเร็จ');
    }

    /**
     * ✅ Sign out user
     */
    async signout(token?: string): Promise<ApiResponse<void>> {
        console.log('AuthService: Starting signout', {
            hasToken: !!token,
            tokenLength: token?.length
        });

        if (!token?.trim()) {
            console.log('AuthService: No token provided for signout');
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        return this.handleRequest(async () => {
            console.log('AuthService: Making authenticated request to /user/signout');
            const result = await this.makeAuthenticatedRequest<void>('/user/signout', token, {
                method: 'POST'
            });
            console.log('AuthService: Signout request completed');
            return result;
        });
    }

    /**
     * ✅ Forgot password
     */
    async forgotPassword(data: ForgotPasswordData): Promise<ApiResponse<void>> {
        // Validate input
        const sanitizedData = sanitizeAuthData(data);
        const validationError = validateForgotPasswordData(sanitizedData);

        if (validationError) {
            return {
                success: false,
                error: validationError
            };
        }

        return this.handleRequest(async () => {
            await this.makePublicRequest<void>('/user/forgot-password', {
                method: 'POST',
                body: sanitizedData
            });
        });
    }

    /**
     * ✅ Reset password
     */
    async resetPassword(data: ResetPasswordData): Promise<ApiResponse<void>> {
        // Validate input
        const sanitizedData = sanitizeAuthData(data);
        const validationError = validateResetPasswordData(sanitizedData);

        if (validationError) {
            return {
                success: false,
                error: validationError
            };
        }

        return this.handleRequest(async () => {
            await this.makePublicRequest<void>('/user/reset-password', {
                method: 'POST',
                body: sanitizedData
            });
        });
    }

    /**
     * ✅ Verify email
     */
    async verifyEmail(data: VerifyEmailData): Promise<ApiResponse<void>> {
        if (!data.token?.trim()) {
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        return this.handleRequest(async () => {
            await this.makePublicRequest<void>('/user/verify-email', {
                method: 'POST',
                body: { token: data.token.trim() }
            });
        });
    }

    /**
     * ✅ Resend verification email
     */
    async resendVerification(data: ForgotPasswordData): Promise<ApiResponse<void>> {
        // Validate input
        const sanitizedData = sanitizeAuthData(data);
        const validationError = validateForgotPasswordData(sanitizedData);

        if (validationError) {
            return {
                success: false,
                error: validationError
            };
        }

        return this.handleRequest(async () => {
            await this.makePublicRequest<void>('/user/resend-verification', {
                method: 'POST',
                body: sanitizedData
            });
        });
    }

    /**
     * ✅ Refresh token
     */
    async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
        if (!refreshToken?.trim()) {
            return {
                success: false,
                error: 'Refresh token ไม่ถูกต้อง'
            };
        }

        return this.handleRequestWithMessage(async () => {
            const result = await this.makePublicRequest<AuthApiResponse>('/user/refresh-token', {
                method: 'POST',
                body: { refreshToken: refreshToken.trim() }
            });

            return result;
        }, 'รีเฟรช token สำเร็จ');
    }

    /**
     * ✅ Get current user
     */
    async getCurrentUser(token: string): Promise<ApiResponse<User>> {
        if (!token?.trim()) {
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        return this.handleRequest(async () => {
            const result = await this.makeAuthenticatedRequest<{ data: User }>('/user/profile', token);
            return result.data;
        });
    }
}

export const authService = new AuthService(); 