import { BaseService } from './base';
import {
  validateCreateSiteData,
  validateDomainData,
  validateUpdateSiteData,
  validatePaginationParams,
  sanitizeSiteData,
  buildDomainUrl,
  type CreateSiteData,
  type CheckDomainData,
  type UpdateSiteData,
  type SitePaginationParams
} from '$lib/schemas/site.schema';
import type { Site } from '$lib/types/site';
import type { ApiResponse } from '$lib/types/common';

// Validation functions ที่ขาดหายไป
const validateCreateSiteForm = (data: CreateSiteData) => {
  const errors: Record<string, string> = {};
  
  if (!data.siteName?.trim()) {
    errors.siteName = 'กรุณากรอกชื่อเว็บไซต์';
  }
  
  if (!data.packageType?.trim()) {
    errors.packageType = 'กรุณาเลือกแพ็คเกจ';
  }
  
  if (!data.typeDomain) {
    errors.typeDomain = 'กรุณาเลือกประเภทโดเมน';
  }
  
  if (data.typeDomain === 'subdomain') {
    if (!data.subDomain?.trim()) {
      errors.subDomain = 'กรุณากรอก subdomain';
    }
    if (!data.mainDomain?.trim()) {
      errors.mainDomain = 'กรุณากรอก main domain';
    }
  } else if (data.typeDomain === 'custom') {
    if (!data.customDomain?.trim()) {
      errors.customDomain = 'กรุณากรอก custom domain';
    }
  }
  
  return {
    success: Object.keys(errors).length === 0,
    errors
  };
};

const validateCheckDomainForm = (data: CheckDomainData) => {
  const errors: Record<string, string> = {};
  
  if (!data.typeDomain) {
    errors.typeDomain = 'กรุณาเลือกประเภทโดเมน';
  }
  
  if (data.typeDomain === 'subdomain') {
    if (!data.subDomain?.trim()) {
      errors.subDomain = 'กรุณากรอก subdomain';
    }
    if (!data.mainDomain?.trim()) {
      errors.mainDomain = 'กรุณากรอก main domain';
    }
  } else if (data.typeDomain === 'custom') {
    if (!data.customDomain?.trim()) {
      errors.customDomain = 'กรุณากรอก custom domain';
    }
  }
  
  return {
    success: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * ✅ Site Service - ใช้ BaseService และ ofetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
class SiteService extends BaseService {

  /**
   * ✅ Get single site by ID
   */
  async getSite(siteId: string, token: string): Promise<ApiResponse<Site>> {
    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Site }>(`/site/${siteId}/content`, token);
      return result.data;
    });
  }

  /**
   * ✅ Check domain availability
   */
  async checkDomain(params: {
    typeDomain: 'subdomain' | 'custom';
    subDomain?: string;
    mainDomain?: string;
    customDomain?: string;
  }, token?: string): Promise<ApiResponse<{ available: boolean; message: string; fullDomain?: string }>> {
    try {
      // Validate domain check form
      const validation = validateCheckDomainForm(params);
      if (!validation.success) {
        return {
          success: false,
          error: validation.errors ? Object.values(validation.errors)[0] : 'ข้อมูลโดเมนไม่ถูกต้อง'
        };
      }

      return this.handleRequest(async () => {
        const queryParams = new URLSearchParams();
        if (params.typeDomain) queryParams.append('typeDomain', params.typeDomain);
        if (params.subDomain) queryParams.append('subDomain', params.subDomain);
        if (params.mainDomain) queryParams.append('mainDomain', params.mainDomain);
        if (params.customDomain) queryParams.append('customDomain', params.customDomain);

        const result = await this.makePublicRequest<{ 
          success: boolean;
          data: { available: boolean; fullDomain: string };
          message: string;
        }>(`/site/check-domain?${queryParams}`, {
          method: 'GET',
        });
        
        return {
          available: result.data.available,
          message: result.message,
          fullDomain: result.data.fullDomain
        };
      });
    } catch (error) {
      console.error('Error checking domain:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน'
      };
    }
  }

  /**
   * ✅ Create site
   */
  async createSite(siteData: CreateSiteData, token?: string): Promise<ApiResponse<Site>> {
    try {
      if (!token?.trim()) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง'
        };
      }

      // Validate form data
      const validation = validateCreateSiteForm(siteData);
      if (!validation.success) {
        return {
          success: false,
          error: validation.errors ? Object.values(validation.errors)[0] : 'ข้อมูลไม่ถูกต้อง'
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ 
          data: { site: Site; resRegisterDomain: any; resCheck: any } 
        }>('/site/create', token, {
          method: 'POST',
          body: JSON.stringify({
            name: siteData.siteName,
            typeDomain: siteData.typeDomain,
            subDomain: siteData.subDomain,
            mainDomain: siteData.mainDomain,
            customDomain: siteData.customDomain,
            plan: siteData.packageType,
          }),
        });
        return result.data.site;
      });
    } catch (error) {
      console.error('Error creating site:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์'
      };
    }
  }

  /**
   * ✅ Create site with validation
   */
  async createSiteWithValidation(siteData: {
    siteName: string;
    typeDomain: 'subdomain' | 'custom';
    subDomain?: string;
    mainDomain?: string;
    customDomain?: string;
    packageType: string;
  }, userMoneyPoint: number, token?: string): Promise<ApiResponse<Site>> {
    try {
      // ตรวจสอบเงินในบัญชี
      if (userMoneyPoint < 100) {
        return {
          success: false,
          error: 'เงินในบัญชีไม่เพียงพอ (ต้องมีอย่างน้อย 100 บาท)'
        };
      }

      // ตรวจสอบข้อมูลที่จำเป็น
      if (!siteData.siteName || !siteData.typeDomain || !siteData.packageType) {
        return {
          success: false,
          error: 'กรุณากรอกข้อมูลให้ครบถ้วน'
        };
      }

      // ตรวจสอบโดเมนตามประเภท
      if (siteData.typeDomain === 'subdomain') {
        if (!siteData.subDomain || !siteData.mainDomain) {
          return {
            success: false,
            error: 'กรุณากรอกข้อมูล subdomain และ main domain'
          };
        }
      } else if (siteData.typeDomain === 'custom') {
        if (!siteData.customDomain) {
          return {
            success: false,
            error: 'กรุณากรอก custom domain'
          };
        }
      }

      // ตรวจสอบโดเมนก่อนสร้าง
      const domainCheck = await this.checkDomain({
        typeDomain: siteData.typeDomain,
        subDomain: siteData.subDomain,
        mainDomain: siteData.mainDomain,
        customDomain: siteData.customDomain,
      }, token);

      if (!domainCheck.success) {
        return {
          success: false,
          error: domainCheck.error || 'ไม่สามารถตรวจสอบโดเมนได้'
        };
      }

      if (!domainCheck.data?.available) {
        return {
          success: false,
          error: domainCheck.data?.message || 'โดเมนนี้ไม่สามารถใช้งานได้'
        };
      }

      // สร้างเว็บไซต์
      const createResult = await this.createSite({
        siteName: siteData.siteName,
        typeDomain: siteData.typeDomain,
        subDomain: siteData.subDomain,
        mainDomain: siteData.mainDomain,
        customDomain: siteData.customDomain,
        packageType: siteData.packageType,
      }, token);

      return createResult;
    } catch (error) {
      console.error('Error creating site with validation:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์'
      };
    }
  }

  /**
   * ✅ Get user sites
   */
  async getUserSites(token?: string): Promise<ApiResponse<Site[]>> {
    try {
      if (!token?.trim()) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง'
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: { sites: Site[] } }>('/site/my-sites', token, {
          method: 'GET',
        });
        return result.data.sites;
      });
    } catch (error) {
      console.error('Error fetching user sites:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์ของผู้ใช้'
      };
    }
  }

  /**
   * ✅ Get user sites with pagination
   */
  async getUserSitesWithPagination(params: {
    page?: string;
    limit?: string;
    search?: string;
    status?: string;
  }, token?: string): Promise<ApiResponse<{
    sites: Site[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>> {
    try {
      if (!token?.trim()) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง'
        };
      }

      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page);
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.search) queryParams.append('search', params.search);
      if (params.status) queryParams.append('status', params.status);

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{
          data: {
            sites: Site[];
            pagination: {
              page: number;
              limit: number;
              total: number;
              totalPages: number;
            };
          }
        }>(`/site/my-sites?${queryParams}`, token, {
          method: 'GET',
        });
        return result.data;
      });
    } catch (error) {
      console.error('Error fetching user sites with pagination:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์ของผู้ใช้'
      };
    }
  }

  /**
   * ✅ Get site packages
   */
  async getSitePackages(): Promise<ApiResponse<any[]>> {
    try {
      return this.handleRequest(async () => {
        const result = await this.makePublicRequest<{ data: any[] }>('/site/packages', {
          method: 'GET',
        });
        return result.data;
      });
    } catch (error) {
      console.error('Error fetching site packages:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ'
      };
    }
  }

  /**
   * ✅ Get site theme settings
   */
  async getSiteTheme(siteId: string, token?: string): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          error: 'Site ID ไม่ถูกต้อง'
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง'
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any }>(`/site/${siteId}/theme`, token, {
          method: 'GET',
        });
        return result.data;
      });
    } catch (error) {
      console.error('Error fetching site theme:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการดึงข้อมูลธีม'
      };
    }
  }

  /**
   * ✅ Update site theme settings
   */
  async updateSiteTheme(siteId: string, themeData: any, token?: string): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          error: 'Site ID ไม่ถูกต้อง'
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง'
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any }>(`/site/${siteId}/theme`, token, {
          method: 'PUT',
          body: JSON.stringify(themeData),
        });
        return result.data;
      });
    } catch (error) {
      console.error('Error updating site theme:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการอัปเดตธีม'
      };
    }
  }

  /**
   * ✅ Reset site theme settings
   */
  async resetSiteTheme(siteId: string, token?: string): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          error: 'Site ID ไม่ถูกต้อง'
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง'
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any }>(`/site/${siteId}/theme/reset`, token, {
          method: 'POST',
        });
        return result.data;
      });
    } catch (error) {
      console.error('Error resetting site theme:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการรีเซ็ตธีม'
      };
    }
  }
}

export const siteService = new SiteService();
