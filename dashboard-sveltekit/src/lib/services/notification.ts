import { BaseService } from './base';
import { apiUrl } from '$lib/config';
import type { NotificationType, NotificationData, Notification, NotificationSettings, NotificationStats } from '$lib/types';
import type { ApiResponse } from '$lib/types/common';

export class NotificationService extends BaseService {
  // ดึงการแจ้งเตือนทั้งหมด
  async getNotifications(token: string, params: {
    page?: number;
    limit?: number;
    type?: NotificationType;
    status?: 'unread' | 'read' | 'archived';
  } = {}): Promise<ApiResponse<Notification[]>> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.type) searchParams.set('type', params.type);
    if (params.status) searchParams.set('status', params.status);

    const query = searchParams.toString();
    const endpoint = query ? `/notifications?${query}` : '/notifications';

    return this.makeAuthenticatedRequest(endpoint, token);
  }

  // ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่าน
  async getUnreadCount(token: string): Promise<ApiResponse<number>> {
    return this.makeAuthenticatedRequest('/notifications/unread-count', token);
  }

  // ทำเครื่องหมายว่าอ่านแล้ว
  async markAsRead(notificationIds: string[], token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/mark-read', token, {
      method: 'POST',
      body: JSON.stringify({ notificationIds }),
    });
  }

  // ลบการแจ้งเตือน
  async deleteNotification(notificationId: string, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest(`/notifications/${notificationId}`, token, {
      method: 'DELETE',
    });
  }

  // ดึงสถิติการแจ้งเตือน
  async getStats(token: string): Promise<ApiResponse<NotificationStats[]>> {
    return this.makeAuthenticatedRequest('/notifications/stats', token);
  }

  // ดึงการตั้งค่าการแจ้งเตือน
  async getSettings(token: string): Promise<ApiResponse<NotificationSettings>> {
    return this.makeAuthenticatedRequest('/notifications/settings', token);
  }

  // อัปเดตการตั้งค่าการแจ้งเตือน
  async updateSettings(settings: Partial<NotificationSettings>, token: string): Promise<ApiResponse<NotificationSettings>> {
    return this.makeAuthenticatedRequest('/notifications/settings', token, {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // ส่งการแจ้งเตือนเติมเงิน
  async notifyTopup(userId: string, amount: number, balance: number, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/topup', token, {
      method: 'POST',
      body: JSON.stringify({ userId, amount, balance }),
    });
  }

  // ส่งการแจ้งเตือนสมาชิกใหม่
  async notifyNewMember(adminUsers: string[], customerName: string, customerId: string, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/new-member', token, {
      method: 'POST',
      body: JSON.stringify({ adminUsers, customerName, customerId }),
    });
  }

  // ส่งการแจ้งเตือนวันหมดอายุ
  async notifyExpiry(userId: string, expiryDate: string, daysLeft: number, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/expiry', token, {
      method: 'POST',
      body: JSON.stringify({ userId, expiryDate, daysLeft }),
    });
  }

  // ส่งการแจ้งเตือนสต็อกต่ำ
  async notifyLowStock(adminUsers: string[], productName: string, productId: string, stockLevel: number, threshold: number, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/low-stock', token, {
      method: 'POST',
      body: JSON.stringify({ adminUsers, productName, productId, stockLevel, threshold }),
    });
  }

  // ส่งการแจ้งเตือนสินค้าใหม่
  async notifyNewProduct(customerIds: string[], productName: string, productId: string, productImage: string | undefined, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/new-product', token, {
      method: 'POST',
      body: JSON.stringify({ customerIds, productName, productId, productImage }),
    });
  }

  // ส่งการแจ้งเตือนคำสั่งซื้อ
  async notifyOrderPurchased(adminUsers: string[], customerName: string, orderId: string, amount: number, token: string): Promise<ApiResponse<void>> {
    return this.makeAuthenticatedRequest('/notifications/order-purchased', token, {
      method: 'POST',
      body: JSON.stringify({ adminUsers, customerName, orderId, amount }),
    });
  }
}

export const notificationService = new NotificationService();