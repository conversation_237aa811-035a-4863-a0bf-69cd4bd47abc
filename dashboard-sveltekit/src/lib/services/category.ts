import { BaseService } from './base';
import {
  validateCreateCategoryData,
  validateUpdateCategoryData,
  sanitizeCategoryData,
  type CreateCategoryData,
  type UpdateCategoryData
} from '$lib/schemas/category.schema';
import type { Category, CategoryListResponse, CategoryResponse } from '$lib/types/category';
import type { ApiResponse } from '$lib/types/common';

/**
 * ✅ Category Service - ใช้ BaseService และ ofetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class CategoryService extends BaseService {

  /**
   * ✅ Get categories with pagination
   */
  async getCategories(siteId: string, token: string): Promise<ApiResponse<CategoryListResponse>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: CategoryListResponse }>(
        `/product/dashboard/${siteId}/category/list`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Get single category by ID
   */
  async getCategory(categoryId: string, siteId: string, token: string): Promise<ApiResponse<Category>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!categoryId?.trim()) {
      return {
        success: false,
        error: 'Category ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Category }>(
        `/product/dashboard/${siteId}/category/detail/${categoryId}`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new category
   */
  async createCategory(data: CreateCategoryData, siteId: string, token: string): Promise<ApiResponse<Category>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeCategoryData(data);
    const validationError = validateCreateCategoryData(sanitizedData);

    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Category }>(
        `/product/dashboard/${siteId}/category/create`,
        token,
        {
          method: 'POST',
          body: sanitizedData
        }
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing category
   */
  async updateCategory(categoryId: string, data: UpdateCategoryData, siteId: string, token: string): Promise<ApiResponse<Category>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!categoryId?.trim()) {
      return {
        success: false,
        error: 'Category ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeCategoryData(data);
    const validationError = validateUpdateCategoryData(sanitizedData);

    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Category }>(
        `/product/dashboard/${siteId}/category/update/${categoryId}`,
        token,
        {
          method: 'PUT',
          body: sanitizedData
        }
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete category
   */
  async deleteCategory(categoryId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!categoryId?.trim()) {
      return {
        success: false,
        error: 'Category ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/product/dashboard/${siteId}/category/delete/${categoryId}`,
        token,
        {
          method: 'DELETE'
        }
      );
    });
  }

  /**
   * ✅ Build category tree from flat list
   */
  buildCategoryTree(categories: Category[]): Category[] {
    const categoryMap = new Map<string, Category>();
    const rootCategories: Category[] = [];

    // สร้าง map ของ categories
    categories.forEach(category => {
      categoryMap.set(category._id, { ...category, children: [] });
    });

    // จัดเรียง categories เป็น tree
    categories.forEach(category => {
      const mappedCategory = categoryMap.get(category._id)!;
      
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children!.push(mappedCategory);
        }
      } else {
        rootCategories.push(mappedCategory);
      }
    });

    return rootCategories;
  }
}

export const categoryService = new CategoryService(); 