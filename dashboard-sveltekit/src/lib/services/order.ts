import { BaseService } from './base';
import {
  validateCreateOrderData,
  validateUpdateOrderData,
  sanitizeOrderData,
  type CreateOrderData,
  type UpdateOrderData
} from '$lib/schemas/order.schema';
import type { Order, OrderListResponse } from '$lib/types/order';
import type { ApiResponse } from '$lib/types/common';

/**
 * ✅ Order Service - ใช้ BaseService และ ofetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class OrderService extends BaseService {

  /**
   * ✅ Get orders with pagination and filters
   */
  async getOrders(siteId: string, token: string, params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ApiResponse<OrderListResponse>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{ data: OrderListResponse }>(
        `/order/dashboard/${siteId}/list?${queryParams}`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Get single order by ID
   */
  async getOrder(orderId: string, siteId: string, token: string): Promise<ApiResponse<Order>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!orderId?.trim()) {
      return {
        success: false,
        error: 'Order ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Order }>(
        `/order/dashboard/${siteId}/detail/${orderId}`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new order
   */
  async createOrder(data: CreateOrderData, siteId: string, token: string): Promise<ApiResponse<Order>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeOrderData(data);
    const validationError = validateCreateOrderData(sanitizedData);

    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Order }>(
        `/order/dashboard/${siteId}/create`,
        token,
        {
          method: 'POST',
          body: sanitizedData
        }
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing order
   */
  async updateOrder(orderId: string, data: UpdateOrderData, siteId: string, token: string): Promise<ApiResponse<Order>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!orderId?.trim()) {
      return {
        success: false,
        error: 'Order ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeOrderData(data);
    const validationError = validateUpdateOrderData(sanitizedData);

    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Order }>(
        `/order/dashboard/${siteId}/update/${orderId}`,
        token,
        {
          method: 'PUT',
          body: sanitizedData
        }
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete order
   */
  async deleteOrder(orderId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!orderId?.trim()) {
      return {
        success: false,
        error: 'Order ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/order/dashboard/${siteId}/delete/${orderId}`,
        token,
        {
          method: 'DELETE'
        }
      );
    });
  }

  /**
   * ✅ Get order statistics
   */
  async getOrderStats(siteId: string, token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: any }>(
        `/order/dashboard/${siteId}/stats`,
        token
      );
      return result.data;
    });
  }
}

export const orderService = new OrderService(); 