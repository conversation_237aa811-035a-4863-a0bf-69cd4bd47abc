{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset"}, "auth": {"login": "<PERSON><PERSON>", "signout": "Signout", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "registerSuccess": "Registration successful", "registerError": "Registration failed"}, "user": {"profile": "Profile", "users": "Users", "userManagement": "User Management", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "role": "Role", "status": "Status", "active": "Active", "inactive": "Inactive", "admin": "Administrator", "user": "User", "moderator": "Moderator", "createdAt": "Created At", "updatedAt": "Updated At", "lastLogin": "Last Login"}, "navigation": {"dashboard": "Dashboard", "users": "Users", "settings": "Settings", "reports": "Reports", "analytics": "Analytics", "website": "Website", "content": "Content", "pages": "Pages", "menu": "<PERSON><PERSON>", "themes": "Themes", "plugins": "Plugins"}, "website": {"websiteManagement": "Website Management", "createWebsite": "Create Website", "editWebsite": "Edit Website", "deleteWebsite": "Delete Website", "websiteName": "Website Name", "websiteUrl": "Website URL", "description": "Description", "template": "Template", "domain": "Domain", "ssl": "SSL", "backup": "Backup", "restore": "Rest<PERSON>"}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto", "currentTheme": "Current Theme", "toggleTheme": "Toggle Theme", "switchToLight": "Switch to Light Mode", "switchToDark": "Switch to Dark Mode", "switchToAuto": "Switch to Auto Mode", "systemPreference": "System Preference", "themeChanged": "Theme Changed", "followSystem": "Follow System Settings"}}