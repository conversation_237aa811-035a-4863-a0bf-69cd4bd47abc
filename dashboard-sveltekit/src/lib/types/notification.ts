// Types สำหรับ Notification
export type NotificationType =
    | 'order'
    | 'product'
    | 'promotion'
    | 'system'
    | 'chat'
    | 'affiliate'
    | 'topup'
    | 'membership'
    | 'expiry'
    | 'inventory'
    | 'payment'
    | 'security'
    | 'marketing';

export interface NotificationData {
    orderId?: string;
    productId?: string;
    chatRoomId?: string;
    affiliateId?: string;
    customerId?: string;
    userId?: string;
    amount?: number;
    balance?: number;
    expiryDate?: Date;
    daysLeft?: number;
    stockLevel?: number;
    threshold?: number;
    url?: string;
    image?: string;
    metadata?: Record<string, any>;
}

export interface Notification {
    _id: string;
    siteId: string;
    recipientId: string;
    recipientType: 'user' | 'customer' | 'admin';
    type: NotificationType;
    title: string;
    message: string;
    data?: NotificationData;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    status: 'unread' | 'read' | 'archived';
    channels: {
        inApp: boolean;
        email: boolean;
        push: boolean;
        sms: boolean;
    };
    deliveryStatus: {
        inApp: { sent: boolean; delivered: boolean; read: boolean };
        email: { sent: boolean; delivered: boolean; opened: boolean };
        push: { sent: boolean; delivered: boolean; clicked: boolean };
        sms: { sent: boolean; delivered: boolean };
    };
    scheduledAt?: Date;
    sentAt?: Date;
    readAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}

export interface NotificationSettings {
    _id: string;
    siteId: string;
    userId: string;
    userType: 'user' | 'customer' | 'admin';
    preferences: {
        orderUpdates: boolean;
        productAlerts: boolean;
        promotions: boolean;
        systemMessages: boolean;
        chatMessages: boolean;
        affiliateUpdates: boolean;
        topupAlerts: boolean;
        membershipUpdates: boolean;
        expiryWarnings: boolean;
        inventoryAlerts: boolean;
        paymentNotifications: boolean;
        securityAlerts: boolean;
        marketingMessages: boolean;
    };
    channels: {
        inApp: boolean;
        email: boolean;
        push: boolean;
        sms: boolean;
    };
    quietHours: {
        enabled: boolean;
        startTime: string;
        endTime: string;
        timezone: string;
    };
    createdAt: Date;
    updatedAt: Date;
}

export interface NotificationStats {
    _id: string;
    count: number;
    unread: number;
} 