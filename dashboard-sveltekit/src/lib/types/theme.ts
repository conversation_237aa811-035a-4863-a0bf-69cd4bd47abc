export interface ThemeSettings {
    id?: string;
    siteId: string;
    layout?: string;
    headerStyle?: string;
    footerStyle?: string;
    showSearch?: boolean;
    showLanguageSelector?: boolean;
    showThemeToggle?: boolean;
    mobileMenuStyle?: string;
    desktopMenuStyle?: string;
    primaryColor?: string;
    secondaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    accentColor?: string;
    fontFamily?: string;
    fontSize?: string;
    lineHeight?: string;
    fontWeight?: string;
    containerPadding?: string;
    sectionSpacing?: string;
    elementSpacing?: string;
    borderRadius?: string;
    spacing?: string;
    customCSS?: string;
    isActive?: boolean;
    createdAt?: string;
    updatedAt?: string;
}

export interface ThemeResponse {
    success: boolean;
    data?: ThemeSettings | null;
    error?: string;
}

export interface ThemeListResponse {
    success: boolean;
    data?: ThemeSettings[];
    error?: string;
} 