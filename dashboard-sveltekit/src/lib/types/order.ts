// ✅ Order Types
export interface Order {
  id: string;
  customerId: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    variantId?: string;
  }>;
  shippingAddress: {
    street: string;
    city: string;
    state?: string;
    zipCode: string;
    country: string;
  };
  paymentMethod: string;
  status: string;
  total: number;
  note?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
  variantId?: string;
}

export interface OrderStatus {
  id: string;
  name: string;
  color: string;
} 