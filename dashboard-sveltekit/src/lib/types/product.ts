export interface ProductImage {
    url: string;
    alt?: string;
    isPrimary: boolean;
    variantId?: string;
}

export interface ProductVariant {
    name: string;
    sku?: string;
    price?: number;
    stock?: number;
    attributes: Record<string, string>;
    images?: string[];
    isActive: boolean;
}

export interface Product {
    _id: string;
    siteId: string;
    name: string;
    description?: string;
    price: number;
    comparePrice?: number;
    cost?: number;
    sku?: string;
    barcode?: string;
    stock: number;
    lowStockThreshold?: number;
    categoryId?: string;
    brandId?: string;
    tags?: string[];
    images: ProductImage[];
    variants?: ProductVariant[];
    isActive: boolean;
    isDigital: boolean;
    weight?: number;
    dimensions?: {
        length: number;
        width: number;
        height: number;
    };
    seoTitle?: string;
    seoDescription?: string;
    seoKeywords?: string[];
    createdAt: string;
    updatedAt: string;
}

export interface ProductListResponse {
    success: boolean;
    data?: {
        products: Product[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
    error?: string;
}

export interface ProductResponse {
    success: boolean;
    data?: Product | null;
    error?: string;
}

export interface ProductStats {
    totalProducts: number;
    activeProducts: number;
    outOfStockProducts: number;
    lowStockProducts: number;
    totalSales: number;
    averageRating: number;
}

export interface ProductStatsResponse {
    success: boolean;
    data?: ProductStats;
    error?: string;
} 