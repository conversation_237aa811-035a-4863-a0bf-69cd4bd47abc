<script lang="ts">
    import { onMount } from "svelte";
    import { t } from "svelte-i18n";
    import { themeStore, type Theme } from "$lib/stores/theme.svelte";
    import { logger, LogCategory } from "$lib/utils/logger";
    import Icon from "@iconify/svelte";

    let currentTheme = $derived(themeStore.theme);
    let effectiveTheme = $derived(themeStore.getEffectiveThemeValue());
    let systemPrefersDark = $derived(themeStore.systemPrefersDark);
    let testResults = $state<Record<string, any>>({});
    let isTestRunning = $state(false);

    const themes: Theme[] = ["light", "dark", "auto"];

    async function runThemeTest() {
        isTestRunning = true;
        testResults = {};

        logger.info(
            LogCategory.SYSTEM,
            "theme_test_started",
            "Theme system test started",
            {
                currentTheme,
                effectiveTheme,
                systemPrefersDark,
            },
        );

        for (const theme of themes) {
            const results = {
                theme,
                success: true,
                errors: [],
                tests: {
                    setTheme: false,
                    domUpdate: false,
                    localStorage: false,
                    crossTabSync: false,
                    effectiveTheme: false,
                },
            };

            try {
                // Test 1: Set theme
                const originalTheme = themeStore.theme;
                themeStore.setTheme(theme);

                if (themeStore.theme === theme) {
                    results.tests.setTheme = true;
                } else {
                    results.errors.push(`Failed to set theme to ${theme}`);
                    results.success = false;
                }

                // Test 2: DOM update
                await new Promise((resolve) => setTimeout(resolve, 100));
                const domTheme =
                    document.documentElement.getAttribute("data-theme");
                const expectedDomTheme =
                    theme === "auto"
                        ? systemPrefersDark
                            ? "dark"
                            : "light"
                        : theme;

                if (domTheme === expectedDomTheme) {
                    results.tests.domUpdate = true;
                } else {
                    results.errors.push(
                        `DOM theme mismatch. Expected: ${expectedDomTheme}, Got: ${domTheme}`,
                    );
                    results.success = false;
                }

                // Test 3: localStorage
                const storedTheme = localStorage.getItem("theme");
                if (storedTheme === theme) {
                    results.tests.localStorage = true;
                } else {
                    results.errors.push(
                        `localStorage theme mismatch. Expected: ${theme}, Got: ${storedTheme}`,
                    );
                    results.success = false;
                }

                // Test 4: Effective theme calculation
                const calculatedEffective = themeStore.getEffectiveThemeValue();
                if (theme === "auto") {
                    const expectedEffective = systemPrefersDark
                        ? "dark"
                        : "light";
                    if (calculatedEffective === expectedEffective) {
                        results.tests.effectiveTheme = true;
                    } else {
                        results.errors.push(
                            `Effective theme calculation failed for auto mode. Expected: ${expectedEffective}, Got: ${calculatedEffective}`,
                        );
                        results.success = false;
                    }
                } else {
                    if (calculatedEffective === theme) {
                        results.tests.effectiveTheme = true;
                    } else {
                        results.errors.push(
                            `Effective theme calculation failed. Expected: ${theme}, Got: ${calculatedEffective}`,
                        );
                        results.success = false;
                    }
                }

                // Test 5: Cross-tab sync (simulate)
                try {
                    // This is a basic test - in real scenario, you'd open another tab
                    const event = new StorageEvent("storage", {
                        key: "theme-event",
                        newValue: JSON.stringify({
                            type: "theme-change",
                            payload: { theme },
                            timestamp: Date.now(),
                            tabId: "test-tab",
                        }),
                    });

                    window.dispatchEvent(event);
                    results.tests.crossTabSync = true;
                } catch (error) {
                    results.errors.push(`Cross-tab sync test failed: ${error}`);
                    results.success = false;
                }

                // Restore original theme
                themeStore.setTheme(originalTheme);
            } catch (error) {
                results.errors.push(`General error: ${error}`);
                results.success = false;
            }

            testResults[theme] = results;
        }

        logger.info(
            LogCategory.SYSTEM,
            "theme_test_completed",
            "Theme system test completed",
            {
                results: Object.keys(testResults).map((key) => ({
                    theme: key,
                    success: testResults[key].success,
                    errorCount: testResults[key].errors.length,
                })),
            },
        );

        isTestRunning = false;
    }

    function testThemeToggle() {
        logger.info(
            LogCategory.SYSTEM,
            "theme_toggle_test",
            "Testing theme toggle functionality",
        );

        const originalTheme = themeStore.theme;

        // Test toggle sequence: current -> next -> next -> back to original
        themeStore.toggleTheme();
        setTimeout(() => {
            themeStore.toggleTheme();
            setTimeout(() => {
                themeStore.toggleTheme();
                setTimeout(() => {
                    // Should be back to original or close to it
                    logger.info(
                        LogCategory.SYSTEM,
                        "theme_toggle_test_completed",
                        "Theme toggle test completed",
                        {
                            originalTheme,
                            finalTheme: themeStore.theme,
                        },
                    );
                }, 500);
            }, 500);
        }, 500);
    }

    function testSystemThemeDetection() {
        logger.info(
            LogCategory.SYSTEM,
            "system_theme_detection_test",
            "Testing system theme detection",
        );

        // Set to auto mode to test system detection
        themeStore.setTheme("auto");

        // Log current system preference
        logger.info(
            LogCategory.SYSTEM,
            "system_theme_info",
            "System theme information",
            {
                systemPrefersDark,
                effectiveTheme: themeStore.getEffectiveThemeValue(),
                mediaQuerySupported: !!window.matchMedia,
            },
        );
    }

    function getThemeIcon(theme: Theme) {
        switch (theme) {
            case "light":
                return "mdi:white-balance-sunny";
            case "dark":
                return "mdi:moon-waning-crescent";
            case "auto":
                return "mdi:theme-light-dark";
            default:
                return "mdi:palette";
        }
    }

    function getTestStatusIcon(success: boolean) {
        return success ? "mdi:check-circle" : "mdi:close-circle";
    }

    function getTestStatusColor(success: boolean) {
        return success ? "text-green-500" : "text-red-500";
    }

    onMount(() => {
        logger.info(
            LogCategory.SYSTEM,
            "theme_test_component_mounted",
            "Theme test component mounted",
            {
                currentTheme,
                effectiveTheme,
                systemPrefersDark,
            },
        );
    });
</script>

<div class="space-y-6">
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">Theme System Test</h2>

            <div class="flex gap-4 mb-4">
                <button
                    class="btn btn-primary"
                    onclick={runThemeTest}
                    disabled={isTestRunning}
                >
                    {#if isTestRunning}
                        <span class="loading loading-spinner loading-sm"></span>
                        Running Tests...
                    {:else}
                        <Icon icon="mdi:test-tube" class="w-4 h-4" />
                        Run Theme Tests
                    {/if}
                </button>

                <button class="btn btn-secondary" onclick={testThemeToggle}>
                    <Icon icon="mdi:toggle-switch" class="w-4 h-4" />
                    Test Toggle
                </button>

                <button
                    class="btn btn-accent"
                    onclick={testSystemThemeDetection}
                >
                    <Icon icon="mdi:monitor" class="w-4 h-4" />
                    Test System Detection
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Current Theme Info -->
                <div class="alert alert-info">
                    <Icon icon="mdi:information" class="w-5 h-5" />
                    <div>
                        <p><strong>Current Theme:</strong> {currentTheme}</p>
                        <p>
                            <strong>Effective Theme:</strong>
                            {effectiveTheme}
                        </p>
                        <p>
                            <strong>System Prefers Dark:</strong>
                            {systemPrefersDark ? "Yes" : "No"}
                        </p>
                    </div>
                </div>

                <!-- Theme Controls -->
                <div class="card bg-base-200">
                    <div class="card-body">
                        <h3 class="card-title text-sm">Quick Theme Switch</h3>
                        <div class="flex gap-2">
                            {#each themes as theme}
                                <button
                                    class="btn btn-sm {currentTheme === theme
                                        ? 'btn-primary'
                                        : 'btn-outline'}"
                                    onclick={() => themeStore.setTheme(theme)}
                                >
                                    <Icon
                                        icon={getThemeIcon(theme)}
                                        class="w-4 h-4"
                                    />
                                    {$t(`theme.${theme}`)}
                                </button>
                            {/each}
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="card bg-base-200">
                    <div class="card-body">
                        <h3 class="card-title text-sm">System Information</h3>
                        <div class="space-y-1 text-sm">
                            <p>
                                Media Query Support: {window.matchMedia
                                    ? "Yes"
                                    : "No"}
                            </p>
                            <p>
                                BroadcastChannel Support: {"BroadcastChannel" in
                                window
                                    ? "Yes"
                                    : "No"}
                            </p>
                            <p>
                                localStorage Available: {typeof Storage !==
                                "undefined"
                                    ? "Yes"
                                    : "No"}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {#if Object.keys(testResults).length > 0}
        <div class="space-y-4">
            {#each Object.entries(testResults) as [themeName, result]}
                <div class="card bg-base-100 shadow-sm">
                    <div class="card-body">
                        <div class="flex items-center gap-2 mb-4">
                            <Icon
                                icon={getThemeIcon(themeName)}
                                class="w-6 h-6"
                            />
                            <h3 class="text-lg font-semibold">
                                {$t(`theme.${themeName}`)} ({themeName})
                            </h3>
                            {#if result.success}
                                <div class="badge badge-success">✓ Pass</div>
                            {:else}
                                <div class="badge badge-error">✗ Fail</div>
                            {/if}
                        </div>

                        {#if result.errors.length > 0}
                            <div class="alert alert-error mb-4">
                                <Icon icon="mdi:alert-circle" class="w-5 h-5" />
                                <div>
                                    <p>
                                        <strong
                                            >Errors ({result.errors
                                                .length}):</strong
                                        >
                                    </p>
                                    <ul class="list-disc list-inside">
                                        {#each result.errors as error}
                                            <li class="text-sm">{error}</li>
                                        {/each}
                                    </ul>
                                </div>
                            </div>
                        {/if}

                        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                            {#each Object.entries(result.tests) as [testName, success]}
                                <div class="flex items-center gap-2">
                                    <Icon
                                        icon={getTestStatusIcon(success)}
                                        class="w-4 h-4 {getTestStatusColor(
                                            success,
                                        )}"
                                    />
                                    <span class="text-sm capitalize"
                                        >{testName.replace(
                                            /([A-Z])/g,
                                            " $1",
                                        )}</span
                                    >
                                </div>
                            {/each}
                        </div>
                    </div>
                </div>
            {/each}
        </div>
    {/if}

    <!-- Live Theme Demo -->
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <h3 class="card-title">Live Theme Demo</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Color Swatches -->
                <div>
                    <h4 class="font-semibold mb-2">Color Swatches</h4>
                    <div class="grid grid-cols-4 gap-2">
                        <div
                            class="w-12 h-12 bg-primary rounded flex items-center justify-center text-primary-content text-xs"
                        >
                            Primary
                        </div>
                        <div
                            class="w-12 h-12 bg-secondary rounded flex items-center justify-center text-secondary-content text-xs"
                        >
                            Secondary
                        </div>
                        <div
                            class="w-12 h-12 bg-accent rounded flex items-center justify-center text-accent-content text-xs"
                        >
                            Accent
                        </div>
                        <div
                            class="w-12 h-12 bg-neutral rounded flex items-center justify-center text-neutral-content text-xs"
                        >
                            Neutral
                        </div>
                        <div
                            class="w-12 h-12 bg-base-100 border rounded flex items-center justify-center text-base-content text-xs"
                        >
                            Base 100
                        </div>
                        <div
                            class="w-12 h-12 bg-base-200 rounded flex items-center justify-center text-base-content text-xs"
                        >
                            Base 200
                        </div>
                        <div
                            class="w-12 h-12 bg-base-300 rounded flex items-center justify-center text-base-content text-xs"
                        >
                            Base 300
                        </div>
                        <div
                            class="w-12 h-12 bg-info rounded flex items-center justify-center text-info-content text-xs"
                        >
                            Info
                        </div>
                    </div>
                </div>

                <!-- Component Examples -->
                <div>
                    <h4 class="font-semibold mb-2">Component Examples</h4>
                    <div class="space-y-2">
                        <button class="btn btn-primary btn-sm"
                            >Primary Button</button
                        >
                        <button class="btn btn-secondary btn-sm"
                            >Secondary Button</button
                        >
                        <div class="alert alert-info">
                            <Icon icon="mdi:information" class="w-4 h-4" />
                            <span>This is an info alert</span>
                        </div>
                        <div class="form-control">
                            <input
                                type="text"
                                placeholder="Sample input"
                                class="input input-bordered input-sm"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
