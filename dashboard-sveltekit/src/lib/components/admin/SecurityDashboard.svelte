<script lang="ts">
    import { onMount } from "svelte";
    import {
        logger,
        LogLevel,
        LogCategory,
        type LogEntry,
    } from "$lib/utils/logger";
    import { cacheUtils } from "$lib/utils/cache";
    import Icon from "@iconify/svelte";

    let logs = $state<LogEntry[]>([]);
    let performanceMetrics = $state<Map<string, any>>(new Map());
    let cacheStats = $state<Record<string, any>>({});
    let selectedCategory = $state<string | LogCategory>("all");
    let selectedLevel = $state(LogLevel.INFO);
    let autoRefresh = $state(true);
    let refreshInterval: NodeJS.Timeout | undefined;

    const categories = [
        { value: "all", label: "ทั้งหมด" },
        { value: LogCategory.AUTH, label: "Authentication" },
        { value: LogCategory.SECURITY, label: "Security" },
        { value: LogCategory.PERFORMANCE, label: "Performance" },
        { value: LogCategory.API, label: "API" },
        { value: LogCategory.SYSTEM, label: "System" },
    ];

    const levels = [
        { value: LogLevel.DEBUG, label: "Debug", color: "text-gray-500" },
        { value: LogLevel.INFO, label: "Info", color: "text-blue-500" },
        { value: LogLevel.WARN, label: "Warning", color: "text-yellow-500" },
        { value: LogLevel.ERROR, label: "Error", color: "text-red-500" },
        { value: LogLevel.CRITICAL, label: "Critical", color: "text-red-700" },
    ];

    function refreshData() {
        const filter = {
            level: selectedLevel,
            category:
                selectedCategory === "all"
                    ? undefined
                    : (selectedCategory as LogCategory),
            limit: 100,
        };

        logs = logger.getLogs(filter);
        performanceMetrics = logger.getPerformanceMetrics();
        cacheStats = cacheUtils.getAllStats();
    }

    function clearLogs() {
        logger.clearLogs();
        refreshData();
    }

    function exportLogs() {
        const data = logger.exportLogs();
        const blob = new Blob([data], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `security-logs-${new Date().toISOString().split("T")[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    function formatTimestamp(timestamp: string) {
        return new Date(timestamp).toLocaleString("th-TH");
    }

    function getLevelIcon(level: LogLevel) {
        switch (level) {
            case LogLevel.DEBUG:
                return "mdi:bug";
            case LogLevel.INFO:
                return "mdi:information";
            case LogLevel.WARN:
                return "mdi:alert";
            case LogLevel.ERROR:
                return "mdi:alert-circle";
            case LogLevel.CRITICAL:
                return "mdi:alert-octagon";
            default:
                return "mdi:circle";
        }
    }

    function getCategoryIcon(category: LogCategory) {
        switch (category) {
            case LogCategory.AUTH:
                return "mdi:account-key";
            case LogCategory.SECURITY:
                return "mdi:shield-check";
            case LogCategory.PERFORMANCE:
                return "mdi:speedometer";
            case LogCategory.API:
                return "mdi:api";
            case LogCategory.SYSTEM:
                return "mdi:cog";
            default:
                return "mdi:circle";
        }
    }

    onMount(() => {
        refreshData();

        if (autoRefresh) {
            refreshInterval = setInterval(refreshData, 5000);
        }

        return () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        };
    });

    $effect(() => {
        if (autoRefresh && !refreshInterval) {
            refreshInterval = setInterval(refreshData, 5000);
        } else if (!autoRefresh && refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = undefined;
        }
    });

    $effect(() => {
        refreshData();
    });
</script>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold">Security Dashboard</h2>
        <div class="flex gap-2">
            <label class="label cursor-pointer">
                <input
                    type="checkbox"
                    bind:checked={autoRefresh}
                    class="checkbox checkbox-primary checkbox-sm"
                />
                <span class="label-text ml-2">Auto Refresh</span>
            </label>
            <button class="btn btn-sm btn-outline" onclick={refreshData}>
                <Icon icon="mdi:refresh" class="w-4 h-4" />
                Refresh
            </button>
            <button class="btn btn-sm btn-outline" onclick={exportLogs}>
                <Icon icon="mdi:download" class="w-4 h-4" />
                Export
            </button>
            <button class="btn btn-sm btn-error" onclick={clearLogs}>
                <Icon icon="mdi:delete" class="w-4 h-4" />
                Clear
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Cache Stats -->
        <div class="card bg-base-100 shadow-sm">
            <div class="card-body">
                <h3 class="card-title text-sm">Cache Performance</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>Token Cache:</span>
                        <span class="font-mono"
                            >{cacheStats.tokenCache?.size || 0} items</span
                        >
                    </div>
                    <div class="flex justify-between">
                        <span>API Cache:</span>
                        <span class="font-mono"
                            >{cacheStats.apiCache?.size || 0} items</span
                        >
                    </div>
                    <div class="flex justify-between">
                        <span>Hit Rate:</span>
                        <span class="font-mono"
                            >{cacheStats.tokenCache?.hitRate || 0}%</span
                        >
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="card bg-base-100 shadow-sm">
            <div class="card-body">
                <h3 class="card-title text-sm">Performance Metrics</h3>
                <div class="space-y-2">
                    {#each Array.from(performanceMetrics.entries()).slice(0, 3) as [operation, metrics]}
                        <div class="flex justify-between">
                            <span class="text-xs">{operation}:</span>
                            <span class="font-mono text-xs"
                                >{Math.round(metrics.avgTime)}ms</span
                            >
                        </div>
                    {/each}
                </div>
            </div>
        </div>

        <!-- Log Summary -->
        <div class="card bg-base-100 shadow-sm">
            <div class="card-body">
                <h3 class="card-title text-sm">Log Summary</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>Total Logs:</span>
                        <span class="font-mono">{logs.length}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Errors:</span>
                        <span class="font-mono text-red-500">
                            {logs.filter((log) => log.level >= LogLevel.ERROR)
                                .length}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>Warnings:</span>
                        <span class="font-mono text-yellow-500">
                            {logs.filter((log) => log.level === LogLevel.WARN)
                                .length}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <div class="flex flex-wrap gap-4">
                <div class="form-control">
                    <label for="selectedCategory" class="label">
                        <span class="label-text">Category</span>
                    </label>
                    <select
                        bind:value={selectedCategory}
                        class="select select-bordered select-sm"
                    >
                        {#each categories as category}
                            <option value={category.value}
                                >{category.label}</option
                            >
                        {/each}
                    </select>
                </div>

                <div class="form-control">
                    <label for="selectedLevel" class="label">
                        <span class="label-text">Level</span>
                    </label>
                    <select
                        bind:value={selectedLevel}
                        class="select select-bordered select-sm"
                    >
                        {#each levels as level}
                            <option value={level.value}>{level.label}</option>
                        {/each}
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <h3 class="card-title">Security Logs</h3>
            <div class="overflow-x-auto">
                <table class="table table-xs">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Level</th>
                            <th>Category</th>
                            <th>Event</th>
                            <th>Message</th>
                            <th>IP</th>
                            <th>User</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each logs as log}
                            <tr class="hover">
                                <td class="font-mono text-xs">
                                    {formatTimestamp(log.timestamp)}
                                </td>
                                <td>
                                    <div class="flex items-center gap-1">
                                        <Icon
                                            icon={getLevelIcon(log.level)}
                                            class="w-3 h-3 {levels.find(
                                                (l) => l.value === log.level,
                                            )?.color || ''}"
                                        />
                                        <span class="text-xs"
                                            >{levels.find(
                                                (l) => l.value === log.level,
                                            )?.label}</span
                                        >
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center gap-1">
                                        <Icon
                                            icon={getCategoryIcon(log.category)}
                                            class="w-3 h-3"
                                        />
                                        <span class="text-xs"
                                            >{log.category}</span
                                        >
                                    </div>
                                </td>
                                <td class="font-mono text-xs">{log.event}</td>
                                <td
                                    class="text-xs max-w-xs truncate"
                                    title={log.message}
                                >
                                    {log.message}
                                </td>
                                <td class="font-mono text-xs"
                                    >{log.ip || "-"}</td
                                >
                                <td class="font-mono text-xs"
                                    >{log.userId || "-"}</td
                                >
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
