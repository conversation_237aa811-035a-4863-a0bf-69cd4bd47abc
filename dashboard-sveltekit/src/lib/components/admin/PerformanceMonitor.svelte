<script lang="ts">
    import { onMount } from "svelte";
    import { logger } from "$lib/utils/logger";
    import { cacheUtils } from "$lib/utils/cache";
    import Icon from "@iconify/svelte";

    let performanceData = $state({
        metrics: new Map(),
        cacheStats: {},
        systemInfo: {
            memory: 0,
            timing: {},
        },
    });

    let selectedMetric = $state("all");
    let timeRange = $state("1h");
    let autoRefresh = $state(true);
    let refreshInterval: NodeJS.Timeout;

    function refreshData() {
        performanceData.metrics = logger.getPerformanceMetrics();
        performanceData.cacheStats = cacheUtils.getAllStats();

        // Get browser performance info
        if (typeof performance !== "undefined") {
            performanceData.systemInfo.timing = performance.timing;

            if ("memory" in performance) {
                performanceData.systemInfo.memory = (performance as any).memory;
            }
        }
    }

    function formatDuration(ms: number): string {
        if (ms < 1) return `${(ms * 1000).toFixed(0)}μs`;
        if (ms < 1000) return `${ms.toFixed(1)}ms`;
        return `${(ms / 1000).toFixed(2)}s`;
    }

    function formatBytes(bytes: number): string {
        if (bytes === 0) return "0 B";
        const k = 1024;
        const sizes = ["B", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`;
    }

    function getPerformanceColor(avgTime: number): string {
        if (avgTime < 100) return "text-green-500";
        if (avgTime < 500) return "text-yellow-500";
        return "text-red-500";
    }

    function clearMetrics() {
        // Clear performance metrics (would need to implement in logger)
        refreshData();
    }

    onMount(() => {
        refreshData();

        if (autoRefresh) {
            refreshInterval = setInterval(refreshData, 2000);
        }

        return () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        };
    });

    $effect(() => {
        if (autoRefresh && !refreshInterval) {
            refreshInterval = setInterval(refreshData, 2000);
        } else if (!autoRefresh && refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = undefined;
        }
    });
</script>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold">Performance Monitor</h2>
        <div class="flex gap-2">
            <label class="label cursor-pointer">
                <input
                    type="checkbox"
                    bind:checked={autoRefresh}
                    class="checkbox checkbox-primary checkbox-sm"
                />
                <span class="label-text ml-2">Auto Refresh</span>
            </label>
            <button class="btn btn-sm btn-outline" onclick={refreshData}>
                <Icon icon="mdi:refresh" class="w-4 h-4" />
                Refresh
            </button>
            <button class="btn btn-sm btn-outline" onclick={clearMetrics}>
                <Icon icon="mdi:delete" class="w-4 h-4" />
                Clear
            </button>
        </div>
    </div>

    <!-- System Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Memory Usage -->
        {#if performanceData.systemInfo.memory}
            <div class="card bg-base-100 shadow-sm">
                <div class="card-body">
                    <h3 class="card-title text-sm flex items-center gap-2">
                        <Icon icon="mdi:memory" class="w-4 h-4" />
                        Memory Usage
                    </h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-xs">Used:</span>
                            <span class="font-mono text-xs">
                                {formatBytes(
                                    performanceData.systemInfo.memory
                                        .usedJSHeapSize,
                                )}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-xs">Total:</span>
                            <span class="font-mono text-xs">
                                {formatBytes(
                                    performanceData.systemInfo.memory
                                        .totalJSHeapSize,
                                )}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-xs">Limit:</span>
                            <span class="font-mono text-xs">
                                {formatBytes(
                                    performanceData.systemInfo.memory
                                        .jsHeapSizeLimit,
                                )}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        {/if}

        <!-- Cache Performance -->
        <div class="card bg-base-100 shadow-sm">
            <div class="card-body">
                <h3 class="card-title text-sm flex items-center gap-2">
                    <Icon icon="mdi:database" class="w-4 h-4" />
                    Cache Stats
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-xs">Token Cache:</span>
                        <span class="font-mono text-xs">
                            {performanceData.cacheStats.tokenCache?.size || 0} items
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-xs">API Cache:</span>
                        <span class="font-mono text-xs">
                            {performanceData.cacheStats.apiCache?.size || 0} items
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-xs">Hit Rate:</span>
                        <span class="font-mono text-xs text-green-500">
                            {performanceData.cacheStats.tokenCache?.hitRate ||
                                0}%
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Operation Count -->
        <div class="card bg-base-100 shadow-sm">
            <div class="card-body">
                <h3 class="card-title text-sm flex items-center gap-2">
                    <Icon icon="mdi:counter" class="w-4 h-4" />
                    Operations
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-xs">Total:</span>
                        <span class="font-mono text-xs">
                            {Array.from(
                                performanceData.metrics.values(),
                            ).reduce((sum, m) => sum + m.count, 0)}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-xs">Auth Ops:</span>
                        <span class="font-mono text-xs">
                            {Array.from(performanceData.metrics.entries())
                                .filter(([key]) => key.includes("auth"))
                                .reduce((sum, [, m]) => sum + m.count, 0)}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-xs">API Calls:</span>
                        <span class="font-mono text-xs">
                            {Array.from(performanceData.metrics.entries())
                                .filter(([key]) => key.includes("backend"))
                                .reduce((sum, [, m]) => sum + m.count, 0)}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Response Time -->
        <div class="card bg-base-100 shadow-sm">
            <div class="card-body">
                <h3 class="card-title text-sm flex items-center gap-2">
                    <Icon icon="mdi:speedometer" class="w-4 h-4" />
                    Avg Response
                </h3>
                <div class="space-y-2">
                    {#each Array.from(performanceData.metrics.entries()).slice(0, 3) as [operation, metrics]}
                        <div class="flex justify-between">
                            <span class="text-xs truncate" title={operation}>
                                {operation.split("_").pop()}:
                            </span>
                            <span
                                class="font-mono text-xs {getPerformanceColor(
                                    metrics.avgTime,
                                )}"
                            >
                                {formatDuration(metrics.avgTime)}
                            </span>
                        </div>
                    {/each}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Metrics Table -->
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <h3 class="card-title">Performance Metrics</h3>
            <div class="overflow-x-auto">
                <table class="table table-xs">
                    <thead>
                        <tr>
                            <th>Operation</th>
                            <th>Count</th>
                            <th>Avg Time</th>
                            <th>Min Time</th>
                            <th>Max Time</th>
                            <th>Total Time</th>
                            <th>Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each Array.from(performanceData.metrics.entries()) as [operation, metrics]}
                            <tr class="hover">
                                <td class="font-mono text-xs">{operation}</td>
                                <td class="font-mono text-xs"
                                    >{metrics.count}</td
                                >
                                <td
                                    class="font-mono text-xs {getPerformanceColor(
                                        metrics.avgTime,
                                    )}"
                                >
                                    {formatDuration(metrics.avgTime)}
                                </td>
                                <td class="font-mono text-xs"
                                    >{formatDuration(metrics.minTime)}</td
                                >
                                <td class="font-mono text-xs"
                                    >{formatDuration(metrics.maxTime)}</td
                                >
                                <td class="font-mono text-xs"
                                    >{formatDuration(metrics.totalTime)}</td
                                >
                                <td>
                                    <div class="flex items-center gap-1">
                                        {#if metrics.avgTime < 100}
                                            <Icon
                                                icon="mdi:check-circle"
                                                class="w-3 h-3 text-green-500"
                                            />
                                            <span class="text-xs text-green-500"
                                                >Good</span
                                            >
                                        {:else if metrics.avgTime < 500}
                                            <Icon
                                                icon="mdi:alert-circle"
                                                class="w-3 h-3 text-yellow-500"
                                            />
                                            <span
                                                class="text-xs text-yellow-500"
                                                >Fair</span
                                            >
                                        {:else}
                                            <Icon
                                                icon="mdi:close-circle"
                                                class="w-3 h-3 text-red-500"
                                            />
                                            <span class="text-xs text-red-500"
                                                >Poor</span
                                            >
                                        {/if}
                                    </div>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Cache Details -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        {#each Object.entries(performanceData.cacheStats) as [cacheName, stats]}
            <div class="card bg-base-100 shadow-sm">
                <div class="card-body">
                    <h3 class="card-title text-sm">{cacheName}</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-xs">Size:</span>
                            <span class="font-mono text-xs"
                                >{stats.size}/{stats.maxSize}</span
                            >
                        </div>
                        <div class="flex justify-between">
                            <span class="text-xs">Hit Rate:</span>
                            <span class="font-mono text-xs text-green-500"
                                >{stats.hitRate}%</span
                            >
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div
                                class="bg-primary h-2 rounded-full"
                                style="width: {(stats.size / stats.maxSize) *
                                    100}%"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>
        {/each}
    </div>
</div>
