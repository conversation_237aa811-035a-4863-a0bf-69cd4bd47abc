<script lang="ts">
	import Icon from '@iconify/svelte';
	import { createEventDispatcher } from 'svelte';

	interface Column {
		key: string;
		label: string;
		sortable?: boolean;
		width?: string;
		align?: 'left' | 'center' | 'right';
		render?: (value: any, row: any) => string;
	}

	interface Props {
		columns: Column[];
		data: any[];
		sortable?: boolean;
		selectable?: boolean;
		pagination?: boolean;
		pageSize?: number;
		loading?: boolean;
		emptyMessage?: string;
		className?: string;
		onSort?: (key: string, direction: 'asc' | 'desc') => void;
		onSelect?: (selectedRows: any[]) => void;
		onRowClick?: (row: any) => void;
	}

	let {
		columns = [],
		data = [],
		sortable = false,
		selectable = false,
		pagination = false,
		pageSize = 10,
		loading = false,
		emptyMessage = 'ไม่พบข้อมูล',
		className = '',
		onSort,
		onSelect,
		onRowClick
	}: Props = $props();

	const dispatch = createEventDispatcher();

	// State
	let currentPage = $state(1);
	let sortKey = $state('');
	let sortDirection = $state<'asc' | 'desc'>('asc');
	let selectedRows = $state<Set<any>>(new Set());
	let selectAll = $state(false);

	// Computed
	const totalPages = $derived(Math.ceil(data.length / pageSize));
	const startIndex = $derived((currentPage - 1) * pageSize);
	const endIndex = $derived(startIndex + pageSize);
	const paginatedData = $derived(data.slice(startIndex, endIndex));

	// Functions
	function handleSort(key: string) {
		if (!sortable) return;
		
		if (sortKey === key) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortKey = key;
			sortDirection = 'asc';
		}
		
		onSort?.(key, sortDirection);
		dispatch('sort', { key, direction: sortDirection });
	}

	function handleSelectAll() {
		if (selectAll) {
			selectedRows.clear();
		} else {
			paginatedData.forEach(row => selectedRows.add(row));
		}
		selectAll = !selectAll;
		updateSelection();
	}

	function handleSelectRow(row: any) {
		if (selectedRows.has(row)) {
			selectedRows.delete(row);
		} else {
			selectedRows.add(row);
		}
		updateSelection();
	}

	function updateSelection() {
		const selected = Array.from(selectedRows);
		onSelect?.(selected);
		dispatch('select', { selected });
	}

	function handleRowClick(row: any) {
		onRowClick?.(row);
		dispatch('rowClick', { row });
	}

	function goToPage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}

	function renderCell(column: Column, row: any) {
		const value = row[column.key];
		return column.render ? column.render(value, row) : value;
	}
</script>

<div class="card bg-base-100 shadow-sm">
	<div class="card-body p-0">
		{#if loading}
			<div class="flex items-center justify-center p-8">
				<div class="loading loading-spinner loading-lg"></div>
				<span class="ml-2">กำลังโหลด...</span>
			</div>
		{:else if data.length === 0}
			<div class="flex flex-col items-center justify-center p-8 text-center">
				<Icon icon="mdi:database-off" class="w-12 h-12 text-base-content/30 mb-4" />
				<p class="text-base-content/70">{emptyMessage}</p>
			</div>
		{:else}
			<div class="overflow-x-auto">
				<table class="table table-zebra w-full">
					<thead>
						<tr>
							{#if selectable}
								<th class="w-12">
									<input
										type="checkbox"
										class="checkbox checkbox-sm"
										checked={selectAll}
										onchange={handleSelectAll}
									/>
								</th>
							{/if}
							{#each columns as column}
								<th
									class="cursor-pointer hover:bg-base-200 transition-colors"
									class:text-center={column.align === 'center'}
									class:text-right={column.align === 'right'}
									style={column.width ? `width: ${column.width}` : ''}
									onclick={() => column.sortable && handleSort(column.key)}
								>
									<div class="flex items-center gap-2">
										<span>{column.label}</span>
										{#if column.sortable && sortKey === column.key}
											<Icon
												icon={sortDirection === 'asc' ? 'mdi:chevron-up' : 'mdi:chevron-down'}
												class="w-4 h-4"
											/>
										{/if}
									</div>
								</th>
							{/each}
						</tr>
					</thead>
					<tbody>
						{#each paginatedData as row, index}
							<tr
								class="hover:bg-base-200 transition-colors cursor-pointer"
								onclick={() => handleRowClick(row)}
							>
								{#if selectable}
									<td>
										<input
											type="checkbox"
											class="checkbox checkbox-sm"
											checked={selectedRows.has(row)}
											onchange={() => handleSelectRow(row)}
											onclick={(e) => e.stopPropagation()}
										/>
									</td>
								{/if}
								{#each columns as column}
									<td
										class:text-center={column.align === 'center'}
										class:text-right={column.align === 'right'}
									>
										{renderCell(column, row)}
									</td>
								{/each}
							</tr>
						{/each}
					</tbody>
				</table>
			</div>

			{#if pagination && totalPages > 1}
				<div class="flex items-center justify-between p-4 border-t border-base-300">
					<div class="text-sm text-base-content/70">
						แสดง {startIndex + 1}-{Math.min(endIndex, data.length)} จาก {data.length} รายการ
					</div>
					<div class="join">
						<button
							class="join-item btn btn-sm"
							disabled={currentPage === 1}
							onclick={() => goToPage(currentPage - 1)}
						>
							<Icon icon="mdi:chevron-left" class="w-4 h-4" />
						</button>
						
						{#each Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
							const page = i + 1;
							return page;
						}) as page}
							<button
								class="join-item btn btn-sm {page === currentPage ? 'btn-active' : ''}"
								onclick={() => goToPage(page)}
							>
								{page}
							</button>
						{/each}
						
						<button
							class="join-item btn btn-sm"
							disabled={currentPage === totalPages}
							onclick={() => goToPage(currentPage + 1)}
						>
							<Icon icon="mdi:chevron-right" class="w-4 h-4" />
						</button>
					</div>
				</div>
			{/if}
		{/if}
	</div>
</div> 