<script lang="ts">
	import Icon from "@iconify/svelte";

	interface TabItem {
		id: string;
		label: string;
		icon?: string;
		disabled?: boolean;
		badge?: string | number;
	}

	interface Props {
		tabs: TabItem[];
		activeTab: string;
		size?: "xs" | "sm" | "md" | "lg";
		variant?: "bordered" | "lifted" | "boxed";
		className?: string;
		onTabChange?: (tabId: string) => void;
		children?: any;
	}

	let {
		tabs,
		activeTab = $bindable(),
		size = "md",
		variant = "bordered",
		className = "",
		onTabChange,
		children,
	}: Props = $props();

	function handleTabClick(tabId: string, disabled?: boolean) {
		if (disabled) return;
		activeTab = tabId;
		onTabChange?.(tabId);
	}

	const sizeClass = `tabs-${size}`;
	const variantClass = `tabs-${variant}`;
</script>

<div class="tabs {sizeClass} {variantClass} {className}" role="tablist">
	{#each tabs as tab}
		<button
			class="tab {activeTab === tab.id ? 'tab-active' : ''} {tab.disabled ? 'tab-disabled' : ''}"
			class:tab-active={activeTab === tab.id}
			disabled={tab.disabled}
			onclick={() => handleTabClick(tab.id, tab.disabled)}
			role="tab"
			aria-selected={activeTab === tab.id}
		>
			{#if tab.icon}
				<Icon icon={tab.icon} class="w-4 h-4" />
			{/if}
			<span>{tab.label}</span>
			{#if tab.badge}
				<span class="badge badge-sm badge-primary ml-1">{tab.badge}</span>
			{/if}
		</button>
	{/each}
</div>

{#if children}
	<div class="tab-content mt-4">
		{@render children()}
	</div>
{/if}
