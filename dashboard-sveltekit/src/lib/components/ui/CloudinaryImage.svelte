<script lang="ts">
    import { onMount } from 'svelte';
    import { 
        getCloudinaryUrl, 
        getThumbnailUrl, 
        getAvatarUrl, 
        getHeroUrl,
        getSrcSet,
        getPlaceholderUrl,
        isCloudinaryUrl,
        extractPublicId
    } from '$lib/utils/cloudinary';

    let { 
        src = '',
        alt = '',
        width = 300,
        height = 300,
        crop = 'fill',
        quality = 'auto',
        format = 'auto',
        lazy = true,
        placeholder = true,
        responsive = false,
        sizes = [],
        className = '',
        config = {},
        fallback = '/images/placeholder.jpg'
    } = $props();

    let imgElement: HTMLImageElement;
    let isLoaded = false;
    let hasError = false;
    let currentSrc = '';
    let placeholderSrc = '';

    function createImageUrl() {
        if (!src) return fallback;
        
        if (isCloudinaryUrl(src)) {
            const publicId = extractPublicId(src);
            if (publicId) {
                const transformation = `c_${crop},w_${width},h_${height},f_${format},q_${quality}`;
                return getCloudinaryUrl(publicId, transformation, config);
            }
        }
        
        if (src && !src.startsWith('http')) {
            const transformation = `c_${crop},w_${width},h_${height},f_${format},q_${quality}`;
            return getCloudinaryUrl(src, transformation, config);
        }
        
        return src;
    }

    function createPlaceholderUrl() {
        if (!src) return fallback;
        
        if (isCloudinaryUrl(src)) {
            const publicId = extractPublicId(src);
            if (publicId) {
                return getPlaceholderUrl(publicId, config);
            }
        }
        
        if (src && !src.startsWith('http')) {
            return getPlaceholderUrl(src, config);
        }
        
        return fallback;
    }

    function createSrcSet() {
        if (!responsive || sizes.length === 0) return '';
        
        if (isCloudinaryUrl(src)) {
            const publicId = extractPublicId(src);
            if (publicId) {
                return getSrcSet(publicId, sizes, config);
            }
        }
        
        if (src && !src.startsWith('http')) {
            return getSrcSet(src, sizes, config);
        }
        
        return '';
    }

    function handleLoad() {
        isLoaded = true;
        hasError = false;
    }

    function handleError() {
        hasError = true;
        isLoaded = false;
        if (currentSrc !== fallback) {
            currentSrc = fallback;
        }
    }

    $effect(() => {
        currentSrc = createImageUrl();
        placeholderSrc = createPlaceholderUrl();
        isLoaded = false;
        hasError = false;
    });

    onMount(() => {
        currentSrc = createImageUrl();
        placeholderSrc = createPlaceholderUrl();
    });
</script>

<div class="cloudinary-image-container {className}" style="width: {width}px; height: {height}px;">
    {#if placeholder && !isLoaded && !hasError}
        <img 
            src={placeholderSrc}
            alt=""
            class="cloudinary-placeholder absolute inset-0 w-full h-full object-cover blur-sm scale-110"
            style="filter: blur(10px);"
        />
    {/if}
    
    <img 
        bind:this={imgElement}
        src={currentSrc}
        alt={alt}
        width={width}
        height={height}
        loading={lazy ? 'lazy' : 'eager'}
        class="cloudinary-image w-full h-full object-cover transition-opacity duration-300 {isLoaded ? 'opacity-100' : 'opacity-0'}"
        on:load={handleLoad}
        on:error={handleError}
        {#if responsive && sizes.length > 0}
            srcset={createSrcSet()}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        {/if}
    />
    
    {#if !isLoaded && !hasError}
        <div class="cloudinary-loading absolute inset-0 flex items-center justify-center bg-base-200">
            <div class="loading loading-spinner loading-md"></div>
        </div>
    {/if}
    
    {#if hasError}
        <div class="cloudinary-error absolute inset-0 flex items-center justify-center bg-base-200">
            <div class="text-center">
                <svg class="w-8 h-8 text-base-content/50 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <p class="text-xs text-base-content/70">ไม่สามารถโหลดรูปภาพได้</p>
            </div>
        </div>
    {/if}
</div>

<style>
    .cloudinary-image-container {
        position: relative;
        overflow: hidden;
        border-radius: inherit;
    }
    
    .cloudinary-image {
        position: relative;
        z-index: 2;
    }
    
    .cloudinary-placeholder {
        position: absolute;
        z-index: 1;
    }
    
    .cloudinary-loading {
        z-index: 3;
    }
    
    .cloudinary-error {
        z-index: 4;
    }
</style> 