<script lang="ts">
	import Icon from "@iconify/svelte";

	interface StepItem {
		id: string;
		title: string;
		description?: string;
		icon?: string;
		completed?: boolean;
		error?: boolean;
	}

	interface Props {
		steps: StepItem[];
		currentStep: string;
		vertical?: boolean;
		className?: string;
		onStepClick?: (stepId: string) => void;
	}

	let {
		steps,
		currentStep,
		vertical = false,
		className = "",
		onStepClick,
	}: Props = $props();

	function handleStepClick(stepId: string) {
		onStepClick?.(stepId);
	}

	function getStepStatus(step: StepItem) {
		if (step.error) return "error";
		if (step.completed) return "success";
		if (step.id === currentStep) return "primary";
		return "neutral";
	}

	function getStepIcon(step: StepItem) {
		if (step.error) return "mdi:close";
		if (step.completed) return "mdi:check";
		return step.icon || "mdi:circle";
	}
</script>

<ul class="steps {vertical ? 'steps-vertical' : 'steps-horizontal'} {className}">
	{#each steps as step, index}
		<li 
			class="step step-{getStepStatus(step)} {onStepClick ? 'cursor-pointer' : ''}"
			onclick={() => onStepClick && handleStepClick(step.id)}
		>
			<div class="step-circle">
				<Icon icon={getStepIcon(step)} class="w-4 h-4" />
			</div>
			<div class="step-content">
				<div class="step-title">{step.title}</div>
				{#if step.description}
					<div class="step-description text-sm text-base-content/60">
						{step.description}
					</div>
				{/if}
			</div>
		</li>
	{/each}
</ul>
