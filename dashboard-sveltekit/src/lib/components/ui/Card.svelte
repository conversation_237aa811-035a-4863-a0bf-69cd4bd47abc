<script lang="ts">
    import type { Snippet } from "svelte";
    import Icon from "@iconify/svelte";

    interface Props {
        // Layout & Styling
        variant?: "default" | "bordered" | "compact" | "side" | "glass";
        size?: "sm" | "md" | "lg" | "xl" | "full";
        shadow?: "none" | "sm" | "md" | "lg" | "xl";
        centered?: boolean;

        // Content
        title?: string;
        subtitle?: string;
        titleIcon?: string;


        // States
        loading?: boolean;
        disabled?: boolean;
        clickable?: boolean;

        // Custom classes
        class?: string;
        bodyClass?: string;
        titleClass?: string;

        // Actions
        onclick?: () => void;

        // Snippets
        children?: Snippet;
        image?: Snippet;
        header?: Snippet;
        badge?: Snippet;
        actions?: Snippet;
    }

    let {
        variant = "default",
        size = "md",
        shadow = "md",
        centered = false,
        title,
        subtitle,
        titleIcon,
        loading = false,
        disabled = false,
        clickable = false,
        class: className = "",
        bodyClass = "",
        titleClass = "",
        onclick,
        children,
        image,
        header,
        badge,
        actions,
    }: Props = $props();

    // Dynamic classes
    const cardClasses = $derived.by(() => {
        let classes = ["card", "bg-base-100", "h-fit"];

        // Variant styles
        switch (variant) {
            case "bordered":
                classes.push("card-bordered");
                break;
            case "compact":
                classes.push("card-compact");
                break;
            case "side":
                classes.push("card-side");
                break;
            case "glass":
                classes.push("glass");
                break;
        }

        // Size classes
        switch (size) {
            case "sm":
                classes.push("w-full max-w-sm text-sm");
                break;
            case "md":
                classes.push("w-full max-w-md");
                break;
            case "lg":
                classes.push("w-full max-w-lg text-base");
                break;
            case "xl":
                classes.push("w-full max-w-xl text-lg");
                break;
            case "full":
                classes.push("w-full");
                break;
        }

        // Centered positioning
        if (centered) {
            classes.push("mx-auto");
        }

        // Shadow classes - ปรับปรุงให้ทำงานได้ดีขึ้น
        switch (shadow) {
            case "sm":
                classes.push("shadow-sm");
                break;
            case "md":
                classes.push("shadow-md");
                break;
            case "lg":
                classes.push("shadow-lg");
                break;
            case "xl":
                classes.push("shadow-xl");
                break;
            case "none":
                // ไม่เพิ่ม shadow class
                break;
            default:
                classes.push("shadow-md"); // default shadow
                break;
        }

        // Interactive states
        if (clickable || onclick) {
            classes.push(
                "cursor-pointer",
                "hover:shadow-lg",
                "transition-shadow",
            );
        }

        if (disabled) {
            classes.push("opacity-50", "cursor-not-allowed");
        }

        if (loading) {
            classes.push("animate-pulse");
        }

        // Custom classes
        if (className) {
            classes.push(className);
        }

        return classes.join(" ");
    });

    const bodyClasses = $derived.by(() => {
        let classes = ["card-body"];

        // Size-specific padding
        switch (size) {
            case "sm":
                classes.push("p-3");
                break;
            case "md":
                // ใช้ default padding ของ DaisyUI
                break;
            case "lg":
                classes.push("p-8");
                break;
            case "xl":
                classes.push("p-10");
                break;
            case "full":
                // ใช้ default padding ของ DaisyUI
                break;
        }

        if (variant === "compact") {
            classes.push("p-4");
        }

        if (bodyClass) {
            classes.push(bodyClass);
        }

        return classes.join(" ");
    });

    const handleClick = () => {
        if (!disabled && !loading && onclick) {
            onclick();
        }
    };
</script>

{#if clickable || onclick}
    <button
        class={cardClasses}
        onclick={handleClick}
        disabled={disabled || loading}
    >
        <!-- Loading State -->
        {#if loading}
            <div class="card-body">
                <div class="flex items-center space-x-4">
                    <div class="skeleton h-12 w-12 shrink-0 rounded-full"></div>
                    <div class="flex flex-col space-y-2 flex-1">
                        <div class="skeleton h-4 w-full"></div>
                        <div class="skeleton h-4 w-3/4"></div>
                    </div>
                </div>
            </div>
        {:else}
            <!-- Image Snippet (optional) -->
            {#if image}
                <figure class="card-image">
                    {@render image()}
                </figure>
            {/if}

            <!-- Card Body -->
            <div class={bodyClasses}>
                <!-- Title & Subtitle -->
                {#if title || subtitle || header}
                    <div class="card-header mb-4">
                        {#if header}
                            {@render header()}
                        {:else}
                            {#if title}
                                <h2 class="card-title {titleClass}">
                                    {#if titleIcon}
                                        <Icon icon={titleIcon} class="w-5 h-5" />
                                    {/if}
                                    {title}
                                    {#if badge}
                                        {@render badge()}
                                    {/if}
                                </h2>
                            {/if}
                            {#if subtitle}
                                <p class="text-base-content/70 text-sm mt-1">
                                    {subtitle}
                                </p>
                            {/if}
                        {/if}
                    </div>
                {/if}

                <!-- Main Content -->
                {#if children}
                    <div class="card-content flex-1">
                        {@render children()}
                    </div>
                {/if}

                <!-- Actions -->
                {#if actions}
                    <div class="card-actions justify-end mt-4">
                        {@render actions()}
                    </div>
                {/if}
            </div>
        {/if}
    </button>
{:else}
    <div class={cardClasses}>
        <!-- Loading State -->
        {#if loading}
            <div class="card-body">
                <div class="flex items-center space-x-4">
                    <div class="skeleton h-12 w-12 shrink-0 rounded-full"></div>
                    <div class="flex flex-col space-y-2 flex-1">
                        <div class="skeleton h-4 w-full"></div>
                        <div class="skeleton h-4 w-3/4"></div>
                    </div>
                </div>
            </div>
        {:else}
            <!-- Image Snippet (optional) -->
            {#if image}
                <figure class="card-image">
                    {@render image()}
                </figure>
            {/if}

            <!-- Card Body -->
            <div class={bodyClasses}>
                <!-- Title & Subtitle -->
                {#if title || subtitle || header}
                    <div class="card-header mb-4">
                        {#if header}
                            {@render header()}
                        {:else}
                            {#if title}
                                <h2 class="card-title {titleClass}">
                                    {#if titleIcon}
                                        <Icon icon={titleIcon} class="size-7 text-primary" />
                                    {/if}
                                    {title}
                                    {#if badge}
                                        {@render badge()}
                                    {/if}
                                </h2>
                            {/if}
                            {#if subtitle}
                                <p class="text-base-content/70 text-sm mt-1">
                                    {subtitle}
                                </p>
                            {/if}
                        {/if}
                    </div>
                {/if}

                <!-- Main Content -->
                {#if children}
                    <div class="card-content flex-1">
                        {@render children()}
                    </div>
                {/if}

                <!-- Actions -->
                {#if actions}
                    <div class="card-actions justify-end mt-4">
                        {@render actions()}
                    </div>
                {/if}
            </div>
        {/if}
    </div>
{/if}
