<script lang="ts">
	import Icon from '@iconify/svelte';
	import { createEventDispatcher } from 'svelte';

	interface MenuItem {
		label: string;
		value?: string | number;
		icon?: string;
		disabled?: boolean;
		divider?: boolean;
		onClick?: () => void;
		children?: MenuItem[];
	}

	interface Props {
		trigger: any;
		items: MenuItem[];
		placement?: 'top' | 'bottom' | 'left' | 'right';
		align?: 'start' | 'center' | 'end';
		className?: string;
		onSelect?: (item: MenuItem) => void;
	}

	let {
		trigger,
		items = [],
		placement = 'bottom',
		align = 'start',
		className = '',
		onSelect
	}: Props = $props();

	const dispatch = createEventDispatcher();

	// State
	let isOpen = $state(false);

	function toggleDropdown() {
		isOpen = !isOpen;
	}

	function handleItemClick(item: MenuItem) {
		if (!item.disabled) {
			item.onClick?.();
			onSelect?.(item);
			dispatch('select', { item });
			isOpen = false;
		}
	}

	function handleClickOutside(event: Event) {
		const target = event.target as HTMLElement;
		if (!target.closest('.dropdown-container')) {
			isOpen = false;
		}
	}

	$effect(() => {
		if (isOpen) {
			document.addEventListener('click', handleClickOutside);
			return () => document.removeEventListener('click', handleClickOutside);
		}
	});
</script>

<div class="dropdown dropdown-container {className}">
	<div 
		onclick={toggleDropdown}
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				e.preventDefault();
				toggleDropdown();
			}
		}}
		role="button"
		tabindex="0"
		aria-expanded={isOpen}
		aria-haspopup="true"
	>
		{@render trigger()}
	</div>
	
	{#if isOpen}
		<ul class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 z-50" role="menu">
			{#each items as item}
				{#if item.divider}
					<li class="divider" role="separator"></li>
				{:else}
					<li role="none">
						<button
							class="flex items-center gap-2 w-full {item.disabled ? 'disabled' : ''}"
							disabled={item.disabled}
							onclick={() => handleItemClick(item)}
							role="menuitem"
						>
							{#if item.icon}
								<Icon icon={item.icon} class="w-4 h-4" />
							{/if}
							<span>{item.label}</span>
						</button>
					</li>
				{/if}
			{/each}
		</ul>
	{/if}
</div> 