<script lang="ts">
	import Icon from "@iconify/svelte";

	interface Option {
		value: string | number;
		label: string;
		disabled?: boolean;
		icon?: string;
		description?: string;
	}

	interface Props {
		options: Option[];
		value?: string | number | (string | number)[];
		placeholder?: string;
		multiple?: boolean;
		searchable?: boolean;
		disabled?: boolean;
		required?: boolean;
		error?: string;
		size?: "xs" | "sm" | "md" | "lg";
		variant?: "bordered" | "ghost" | "filled";
		className?: string;
		onChange?: (value: string | number | (string | number)[]) => void;
		id?: string;
	}

	let {
		options = [],
		value = undefined,
		placeholder = "เลือกตัวเลือก...",
		multiple = false,
		searchable = false,
		disabled = false,
		required = false,
		error = "",
		size = "md",
		variant = "bordered",
		className = "",
		onChange,
		id = "select-button",
	}: Props = $props();

	// State
	let isOpen = $state(false);
	let searchTerm = $state("");
	let selectedValues = $state<Set<string | number>>(new Set());
	let searchInput = $state("");

	// Initialize selected values
	$effect(() => {
		const currentValue = value;
		if (multiple && Array.isArray(currentValue)) {
			selectedValues = new Set(currentValue as (string | number)[]);
		} else if (
			!multiple &&
			currentValue &&
			(typeof currentValue === "string" ||
				typeof currentValue === "number")
		) {
			selectedValues = new Set([currentValue]);
		} else {
			selectedValues.clear();
		}
	});

	// Computed
	const filteredOptions = $derived(
		options.filter((option) =>
			searchable && searchTerm
				? option.label.toLowerCase().includes(searchTerm.toLowerCase())
				: true,
		),
	);

	const selectedLabels = $derived(
		Array.from(selectedValues)
			.map((value) => options.find((opt) => opt.value === value)?.label)
			.filter(Boolean),
	);

	const baseClasses = "select w-full";
	const classes = $derived(
		[
			baseClasses,
			variant !== "bordered" ? `select-${variant}` : "select-bordered",
			size !== "md" ? `select-${size}` : "",
			error ? "select-error" : "",
			className,
		]
			.filter(Boolean)
			.join(" "),
	);

	// Functions
	function toggleDropdown() {
		if (!disabled) {
			isOpen = !isOpen;
			if (isOpen && searchable) {
				setTimeout(() => {
					const input = document.querySelector(
						".select-search-input",
					) as HTMLInputElement;
					input?.focus();
				}, 100);
			}
		}
	}

	function handleOptionClick(option: Option) {
		if (option.disabled) return;

		if (multiple) {
			if (selectedValues.has(option.value)) {
				selectedValues.delete(option.value);
			} else {
				selectedValues.add(option.value);
			}
			const newValue = Array.from(selectedValues);
			onChange?.(newValue);
		} else {
			selectedValues = new Set([option.value]);
			onChange?.(option.value);
			isOpen = false;
		}
	}

	function handleSearchInput(event: Event) {
		const target = event.target as HTMLInputElement;
		searchTerm = target.value;
	}

	function clearSelection() {
		selectedValues.clear();
		onChange?.(multiple ? [] : "");
	}

	function removeSelected(value: string | number) {
		selectedValues.delete(value);
		const newValue = Array.from(selectedValues);
		onChange?.(newValue);
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event: Event) {
		const target = event.target as HTMLElement;
		if (!target.closest(".select-container")) {
			isOpen = false;
		}
	}

	$effect(() => {
		if (isOpen) {
			document.addEventListener("click", handleClickOutside);
			return () =>
				document.removeEventListener("click", handleClickOutside);
		}
	});
</script>

<div class="select-container relative">
	<div class="form-control w-full">
		<button
			type="button"
			{id}
			class={classes}
			class:select-error={error}
			onclick={toggleDropdown}
			onkeydown={(e) => e.key === "Enter" && toggleDropdown()}
			{disabled}
		>
			{#if multiple && selectedLabels.length > 0}
				<div class="flex flex-wrap gap-1 p-1">
					{#each selectedLabels as label}
						<span class="badge badge-sm badge-primary">
							{label}
							<span
								class="ml-1 cursor-pointer"
								onclick={(e) => {
									e.stopPropagation();
									const option = options.find(
										(opt) => opt.label === label,
									);
									if (option) removeSelected(option.value);
								}}
								onkeydown={(e) =>
									e.key === "Enter" &&
									(() => {
										e.stopPropagation();
										const option = options.find(
											(opt) => opt.label === label,
										);
										if (option)
											removeSelected(option.value);
									})()}
								tabindex="0"
								role="button"
							>
								<Icon icon="mdi:close" class="w-3 h-3" />
							</span>
						</span>
					{/each}
					{#if selectedLabels.length > 2}
						<span class="badge badge-sm badge-neutral">
							+{selectedLabels.length - 2} อื่นๆ
						</span>
					{/if}
				</div>
			{:else if !multiple && selectedLabels.length > 0}
				{selectedLabels[0]}
			{:else}
				<span class="text-base-content/50">{placeholder}</span>
			{/if}

			<!-- <Icon
				icon={isOpen ? "mdi:chevron-up" : "mdi:chevron-down"}
				class="w-4 h-4"
			/> -->
		</button>

		{#if error}
			<label for={id} class="label">
				<span class="label-text-alt text-error">{error}</span>
			</label>
		{/if}
	</div>

	{#if isOpen}
		<div
			class="absolute top-full left-0 right-0 z-50 mt-1 bg-base-100 border border-base-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
		>
			{#if searchable}
				<div class="p-2 border-b border-base-300">
					<input
						type="text"
						class="input input-sm input-bordered w-full select-search-input"
						placeholder="ค้นหา..."
						bind:value={searchTerm}
						oninput={handleSearchInput}
					/>
				</div>
			{/if}

			{#if multiple && selectedLabels.length > 0}
				<div class="p-2 border-b border-base-300">
					<button
						class="btn btn-xs btn-ghost text-error"
						onclick={clearSelection}
					>
						<Icon icon="mdi:close" class="w-3 h-3" />
						ล้างการเลือกทั้งหมด
					</button>
				</div>
			{/if}

			<div class="py-1">
				{#if filteredOptions.length === 0}
					<div class="px-4 py-2 text-base-content/50 text-center">
						ไม่พบตัวเลือก
					</div>
				{:else}
					{#each filteredOptions as option}
						<button
							type="button"
							class="px-4 py-2 hover:bg-base-200 cursor-pointer flex items-center gap-2 w-full text-left {option.disabled
								? 'opacity-50 cursor-not-allowed'
								: ''}"
							class:bg-base-200={selectedValues.has(option.value)}
							onclick={() => handleOptionClick(option)}
							onkeydown={(e) =>
								e.key === "Enter" && handleOptionClick(option)}
							disabled={option.disabled}
						>
							{#if multiple}
								<input
									type="checkbox"
									class="checkbox checkbox-sm"
									checked={selectedValues.has(option.value)}
									disabled={option.disabled}
									onclick={(e) => e.stopPropagation()}
								/>
							{/if}

							{#if option.icon}
								<Icon icon={option.icon} class="w-4 h-4" />
							{/if}

							<div class="flex-1">
								<div class="font-medium">{option.label}</div>
								{#if option.description}
									<div class="text-sm text-base-content/70">
										{option.description}
									</div>
								{/if}
							</div>
						</button>
					{/each}
				{/if}
			</div>
		</div>
	{/if}
</div>
