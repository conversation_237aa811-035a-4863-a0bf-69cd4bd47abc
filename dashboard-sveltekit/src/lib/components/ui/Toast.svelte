<script lang="ts">
	import Icon from "@iconify/svelte";
	import { createEventDispatcher } from "svelte";

	interface Props {
		type?: "info" | "success" | "warning" | "error";
		title?: string;
		message: string;
		duration?: number;
		closable?: boolean;
		className?: string;
	}

	let {
		type = "info",
		title,
		message,
		duration = 5000,
		closable = true,
		className = "",
	}: Props = $props();

	const dispatch = createEventDispatcher();

	let visible = $state(true);

	function getIcon() {
		switch (type) {
			case "success":
				return "mdi:check-circle";
			case "warning":
				return "mdi:alert-circle";
			case "error":
				return "mdi:close-circle";
			default:
				return "mdi:information";
		}
	}

	function getAlertClass() {
		switch (type) {
			case "success":
				return "alert-success";
			case "warning":
				return "alert-warning";
			case "error":
				return "alert-error";
			default:
				return "alert-info";
		}
	}

	function close() {
		visible = false;
		dispatch("close");
	}

	// Auto close
	if (duration > 0) {
		setTimeout(() => {
			if (visible) close();
		}, duration);
	}
</script>

{#if visible}
	<div class="alert {getAlertClass()} {className}" role="alert">
		<Icon icon={getIcon()} class="w-6 h-6 flex-shrink-0" />
		<div class="flex-1">
			{#if title}
				<h3 class="font-bold">{title}</h3>
			{/if}
			<div class="text-sm">{message}</div>
		</div>
		{#if closable}
			<button class="btn btn-sm btn-ghost btn-circle" onclick={close}>
				<Icon icon="mdi:close" class="w-4 h-4" />
			</button>
		{/if}
	</div>
{/if}
