# UI Components Library

ระบบ UI Components ที่ใช้ **DaisyUI 5 + Tailwind 4** สำหรับความยืดหยุ่นและการปรับแต่งที่ง่าย

## 🎨 Design System

### Theme Support
- รองรับ DaisyUI themes ทั้งหมด (30+ themes)
- Auto dark/light mode detection
- Cross-tab theme synchronization
- Custom theme configuration

### Color Palette
```typescript
// DaisyUI Color System
primary    // สีหลัก
secondary  // สีรอง  
accent     // สีเน้น
neutral    // สีกลาง
base       // สีพื้นฐาน
info       // สีข้อมูล
success    // สีสำเร็จ
warning    // สีเตือน
error      // สีข้อผิดพลาด
```

## 📦 Available Components

### Basic Components
- **Button** - ปุ่มต่างๆ พร้อม variants และ sizes
- **Input** - ช่องกรอกข้อมูล พร้อม validation
- **Textarea** - ช่องกรอกข้อความยาว
- **Select** - ดรอปดาวน์เลือกตัวเลือก
- **Checkbox** - ช่องติ๊กเลือก
- **Radio** - ปุ่มเลือกแบบเดียว
- **Toggle** - สวิตช์เปิด/ปิด
- **Label** - ป้ายกำกับ

### Layout Components
- **Card** - การ์ดแสดงเนื้อหา
- **Modal** - หน้าต่างป๊อปอัพ
- **Drawer** - แถบเลื่อนข้าง
- **Tabs** - แท็บสลับเนื้อหา
- **Breadcrumb** - เส้นทางนำทาง

### Data Display
- **Table** - ตารางแสดงข้อมูล
- **Badge** - ป้ายแสดงสถานะ
- **Avatar** - รูปโปรไฟล์
- **Stats** - สถิติและตัวเลข
- **Progress** - แถบความคืบหน้า
- **Timeline** - เส้นเวลา
- **Steps** - ขั้นตอนการทำงาน

### Feedback Components
- **Alert** - ข้อความแจ้งเตือน
- **Toast** - การแจ้งเตือนแบบป๊อปอัพ
- **ToastContainer** - คอนเทนเนอร์สำหรับ toasts
- **Skeleton** - โครงร่างขณะโหลด

### Form Components
- **Upload** - อัปโหลดไฟล์
- **DateTime** - เลือกวันที่และเวลา
- **Dropdown** - เมนูดรอปดาวน์

### Utility Components
- **Pagination** - การแบ่งหน้า
- **Chart** - กราฟและแผนภูมิ
- **Image** - รูปภาพ (Cloudinary integration)

## 🚀 Usage Examples

### Basic Button
```svelte
<script>
  import { Button } from '$lib/components/ui';
</script>

<Button color="primary" size="md" onclick={() => console.log('clicked')}>
  Click me
</Button>
```

### Badge with Icon
```svelte
<script>
  import { Badge } from '$lib/components/ui';
</script>

<Badge color="success" icon="mdi:check" iconPosition="left">
  Active
</Badge>
```

### Toast Notifications
```svelte
<script>
  import { toastStore } from '$lib/stores/toast.svelte';
  import { ToastContainer } from '$lib/components/ui';
  
  function showSuccess() {
    toastStore.success('Operation completed successfully!');
  }
</script>

<button onclick={showSuccess}>Show Toast</button>
<ToastContainer position="top-right" />
```

### Stats Display
```svelte
<script>
  import { Stats } from '$lib/components/ui';
  
  const stats = [
    {
      title: 'Total Users',
      value: '1,234',
      icon: 'mdi:account-group',
      color: 'primary',
      trend: 'up',
      trendValue: '+12%'
    }
  ];
</script>

<Stats {stats} horizontal />
```

## 🎯 Best Practices

### 1. Consistent Sizing
```svelte
<!-- Use consistent size props -->
<Button size="sm">Small</Button>
<Input size="sm" />
<Badge size="sm" />
```

### 2. Color Consistency
```svelte
<!-- Use semantic colors -->
<Button color="primary">Primary Action</Button>
<Badge color="success">Success State</Badge>
<Alert type="error">Error Message</Alert>
```

### 3. Accessibility
```svelte
<!-- Always provide proper labels -->
<Input label="Email Address" required />
<Button aria-label="Close dialog">×</Button>
```

### 4. Responsive Design
```svelte
<!-- Use responsive classes -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <Card>Content</Card>
</div>
```

## 🔧 Customization

### Adding New Components
1. สร้างไฟล์ `.svelte` ใน `src/lib/components/ui/`
2. ใช้ DaisyUI classes และ Tailwind utilities
3. Export ใน `index.ts`
4. เพิ่มเอกสารใน README

### Theme Customization
```typescript
// src/lib/utils/theme.ts
export const CUSTOM_THEME = {
  name: "custom",
  displayName: "Custom Theme",
  isDark: false,
  colors: {
    primary: "#your-color",
    // ... other colors
  }
};
```

## 📱 Mobile First

ทุก component ออกแบบมาให้ responsive และ mobile-friendly:

- Touch-friendly sizes
- Proper spacing for mobile
- Responsive breakpoints
- Accessible touch targets

## 🔄 State Management

### Form State
```svelte
<script>
  let formData = $state({
    name: '',
    email: ''
  });
</script>

<Input bind:value={formData.name} label="Name" />
<Input bind:value={formData.email} label="Email" type="email" />
```

### Loading States
```svelte
<script>
  let loading = $state(false);
</script>

<Button {loading} disabled={loading}>
  {loading ? 'Saving...' : 'Save'}
</Button>
```

## 🎨 Design Tokens

### Spacing Scale
- `xs` = 0.5rem (8px)
- `sm` = 0.75rem (12px)  
- `md` = 1rem (16px)
- `lg` = 1.25rem (20px)
- `xl` = 1.5rem (24px)

### Border Radius
- `rounded-sm` = 2px
- `rounded` = 4px
- `rounded-lg` = 8px
- `rounded-xl` = 12px
- `rounded-full` = 50%

---

**Built with ❤️ using DaisyUI 5 + Tailwind 4 + Svelte 5**
