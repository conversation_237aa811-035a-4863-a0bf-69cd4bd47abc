<script lang="ts">
	import { toastStore } from "$lib/stores/toast.svelte";
	import Toast from "./Toast.svelte";

	interface Props {
		position?: "top-left" | "top-right" | "bottom-left" | "bottom-right" | "top-center" | "bottom-center";
		className?: string;
	}

	let {
		position = "top-right",
		className = "",
	}: Props = $props();

	const positionClasses = {
		"top-left": "toast toast-top toast-start",
		"top-right": "toast toast-top toast-end",
		"bottom-left": "toast toast-bottom toast-start",
		"bottom-right": "toast toast-bottom toast-end",
		"top-center": "toast toast-top toast-center",
		"bottom-center": "toast toast-bottom toast-center",
	};

	function handleToastClose(id: string) {
		toastStore.remove(id);
	}
</script>

<div class="{positionClasses[position]} {className}">
	{#each toastStore.items as toast (toast.id)}
		<Toast
			type={toast.type}
			title={toast.title}
			message={toast.message}
			duration={toast.duration}
			closable={toast.closable}
			onclose={() => handleToastClose(toast.id)}
		/>
	{/each}
</div>
