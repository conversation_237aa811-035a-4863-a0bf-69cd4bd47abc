<script lang="ts">
	import Icon from '@iconify/svelte';
	import { createEventDispatcher } from 'svelte';
	interface Props {
		type?: 'date' | 'time' | 'datetime-local';
		value?: string;
		placeholder?: string;
		label?: string;
		error?: string;
		disabled?: boolean;
		required?: boolean;
		min?: string;
		max?: string;
		className?: string;
		onChange?: (value: string) => void;
		id?: string;
	}

	let {
		type = 'date',
		value = '',
		placeholder = '',
		label = '',
		error = '',
		disabled = false,
		required = false,
		min,
		max,
		className = '',
		onChange,
		id = 'date-time-input'
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		change: { value: string };
	}>();

	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		onChange?.(target.value);
		dispatch('change', { value: target.value });
	}

	function getCurrentDateTime() {
		const now = new Date();
		const year = now.getFullYear();
		const month = String(now.getMonth() + 1).padStart(2, '0');
		const day = String(now.getDate()).padStart(2, '0');
		const hours = String(now.getHours()).padStart(2, '0');
		const minutes = String(now.getMinutes()).padStart(2, '0');
		
		if (type === 'date') {
			return `${year}-${month}-${day}`;
		} else if (type === 'time') {
			return `${hours}:${minutes}`;
		} else {
			return `${year}-${month}-${day}T${hours}:${minutes}`;
		}
	}

	function setCurrentDateTime() {
		const currentValue = getCurrentDateTime();
		onChange?.(currentValue);
		dispatch('change', { value: currentValue });
	}
</script>

<div class="form-control w-full">
	{#if label}
		<label class="label" for={id}>
			<span class="label-text">{label}</span>
			{#if required}
				<span class="text-error">*</span>
			{/if}
		</label>
	{/if}

	<div class="relative">
		<input
			type={type}
			bind:value
			{placeholder}
			{disabled}
			{required}
			{min}
			{max}
			{id}
			class="input input-bordered w-full {error ? 'input-error' : ''} {className}"
			onchange={handleChange}
		/>
		
		<div class="absolute inset-y-0 right-0 flex items-center pr-3">
			{#if type === 'date'}
				<Icon icon="mdi:calendar" class="w-5 h-5 text-base-content/50" />
			{:else if type === 'time'}
				<Icon icon="mdi:clock" class="w-5 h-5 text-base-content/50" />
			{:else}
				<Icon icon="mdi:calendar-clock" class="w-5 h-5 text-base-content/50" />
			{/if}
		</div>
		
		{#if !disabled}
			<button
				type="button"
				class="absolute inset-y-0 right-8 flex items-center pr-2"
				onclick={setCurrentDateTime}
				title="ตั้งเวลาปัจจุบัน"
			>
				<Icon icon="mdi:clock-outline" class="w-4 h-4 text-base-content/50 hover:text-primary" />
			</button>
		{/if}
	</div>

	{#if error}
		<div class="label">
			<span class="label-text-alt text-error">{error}</span>
		</div>
	{/if}
</div> 