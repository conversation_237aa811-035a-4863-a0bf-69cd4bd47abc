<script lang="ts">
	import Icon from "@iconify/svelte";

	interface StatItem {
		title: string;
		value: string | number;
		desc?: string;
		icon?: string;
		color?: "primary" | "secondary" | "accent" | "neutral" | "info" | "success" | "warning" | "error";
		trend?: "up" | "down" | "neutral";
		trendValue?: string;
	}

	interface Props {
		stats: StatItem[];
		horizontal?: boolean;
		className?: string;
	}

	let {
		stats,
		horizontal = false,
		className = "",
	}: Props = $props();

	function getTrendIcon(trend?: string) {
		switch (trend) {
			case "up":
				return "mdi:trending-up";
			case "down":
				return "mdi:trending-down";
			default:
				return "mdi:trending-neutral";
		}
	}

	function getTrendColor(trend?: string) {
		switch (trend) {
			case "up":
				return "text-success";
			case "down":
				return "text-error";
			default:
				return "text-base-content/60";
		}
	}
</script>

<div class="stats {horizontal ? 'stats-horizontal' : 'stats-vertical'} shadow {className}">
	{#each stats as stat}
		<div class="stat">
			{#if stat.icon}
				<div class="stat-figure text-{stat.color || 'primary'}">
					<Icon icon={stat.icon} class="w-8 h-8" />
				</div>
			{/if}
			<div class="stat-title">{stat.title}</div>
			<div class="stat-value text-{stat.color || 'primary'}">{stat.value}</div>
			{#if stat.desc || stat.trend}
				<div class="stat-desc">
					{#if stat.trend && stat.trendValue}
						<span class="{getTrendColor(stat.trend)} flex items-center gap-1">
							<Icon icon={getTrendIcon(stat.trend)} class="w-4 h-4" />
							{stat.trendValue}
						</span>
					{/if}
					{#if stat.desc}
						<span class="text-base-content/60">{stat.desc}</span>
					{/if}
				</div>
			{/if}
		</div>
	{/each}
</div>
