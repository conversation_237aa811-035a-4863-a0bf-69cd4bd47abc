<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { Chart, registerables } from 'chart.js';
    import 'chartjs-adapter-date-fns';

    // Register Chart.js components
    Chart.register(...registerables);

    let { data, type = 'line', options = {}, height = '300px' } = $props<{
        data: any;
        type?: 'line' | 'bar' | 'doughnut' | 'pie';
        options?: any;
        height?: string;
    }>();

    let canvas: HTMLCanvasElement;
    let chart: Chart | null = null;

    onMount(() => {
        if (canvas && data) {
            const ctx = canvas.getContext('2d');
            if (ctx) {
                chart = new Chart(ctx, {
                    type,
                    data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        ...options
                    }
                });
            }
        }
    });

    onDestroy(() => {
        if (chart) {
            chart.destroy();
        }
    });

    $effect(() => {
        if (chart && data) {
            chart.data = data;
            chart.update();
        }
    });
</script>

<div style="height: {height}; position: relative;">
    <canvas bind:this={canvas}></canvas>
</div> 