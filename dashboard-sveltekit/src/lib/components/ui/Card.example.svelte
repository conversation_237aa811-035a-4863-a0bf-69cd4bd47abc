<script lang="ts">
    import Card from "./Card.svelte";
    import Icon from "@iconify/svelte";
</script>

<div class="p-8 space-y-8">
    <h1 class="text-3xl font-bold">Card Component Examples</h1>

    <!-- Basic Card -->
    <section>
        <h2 class="text-xl font-semibold mb-4">Basic Cards</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Simple Card -->
            <Card
                title="Simple Card"
                subtitle="Basic card with title and subtitle"
            >
                <p>This is a simple card with some content inside.</p>
            </Card>

            <!-- Card with Actions -->
            <Card title="Card with Actions" subtitle="Has action buttons">
                <p>This card includes action buttons at the bottom.</p>
                {#snippet actions()}
                    <button class="btn btn-primary btn-sm">Action</button>
                    <button class="btn btn-ghost btn-sm">Cancel</button>
                {/snippet}
            </Card>

            <!-- Clickable Card -->
            <Card
                title="Clickable Card"
                subtitle="Click me!"
                clickable={true}
                onclick={() => alert("Card clicked!")}
            >
                <p>This entire card is clickable.</p>
            </Card>
        </div>
    </section>

    <!-- Different Variants -->
    <section>
        <h2 class="text-xl font-semibold mb-4">Card Variants</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Bordered Card -->
            <Card variant="bordered" title="Bordered Card">
                <p>This card has a border around it.</p>
            </Card>

            <!-- Compact Card -->
            <Card variant="compact" title="Compact Card">
                <p>This card has less padding.</p>
            </Card>

            <!-- Glass Card -->
            <Card variant="glass" title="Glass Card">
                <p>This card has a glass effect.</p>
            </Card>

            <!-- Side Card -->
            <Card variant="side" title="Side Card">
                {#snippet image()}
                    <div
                        class="w-24 h-24 bg-primary rounded-lg flex items-center justify-center"
                    >
                        <Icon
                            icon="mdi:image"
                            class="text-2xl text-primary-content"
                        />
                    </div>
                {/snippet}
                <p>This card has side layout.</p>
            </Card>
        </div>
    </section>

    <!-- Different Sizes -->
    <section>
        <h2 class="text-xl font-semibold mb-4">Card Sizes</h2>
        <div class="space-y-4">
            <Card size="sm" title="Small Card" class="mx-auto">
                <p>Small sized card</p>
            </Card>

            <Card size="md" title="Medium Card" class="mx-auto">
                <p>Medium sized card (default)</p>
            </Card>

            <Card size="lg" title="Large Card" class="mx-auto">
                <p>Large sized card</p>
            </Card>

            <Card size="xl" title="Extra Large Card" class="mx-auto">
                <p>Extra large sized card</p>
            </Card>
        </div>
    </section>

    <!-- Advanced Examples -->
    <section>
        <h2 class="text-xl font-semibold mb-4">Advanced Examples</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Card with Custom Header -->
            <Card>
                {#snippet header()}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="avatar">
                                <div
                                    class="w-10 h-10 rounded-full bg-primary flex items-center justify-center"
                                >
                                    <Icon
                                        icon="mdi:account"
                                        class="text-primary-content"
                                    />
                                </div>
                            </div>
                            <div>
                                <h3 class="font-semibold">John Doe</h3>
                                <p class="text-sm text-base-content/70">
                                    2 hours ago
                                </p>
                            </div>
                        </div>
                        <button class="btn btn-ghost btn-sm btn-circle">
                            <Icon icon="mdi:dots-vertical" />
                        </button>
                    </div>
                {/snippet}

                <p>This card has a custom header with avatar and actions.</p>

                {#snippet actions()}
                    <button class="btn btn-ghost btn-sm">
                        <Icon icon="mdi:heart-outline" />
                        Like
                    </button>
                    <button class="btn btn-ghost btn-sm">
                        <Icon icon="mdi:comment-outline" />
                        Comment
                    </button>
                    <button class="btn btn-ghost btn-sm">
                        <Icon icon="mdi:share-outline" />
                        Share
                    </button>
                {/snippet}
            </Card>

            <!-- Loading Card -->
            <Card loading={true} />

            <!-- Disabled Card -->
            <Card
                title="Disabled Card"
                subtitle="This card is disabled"
                disabled={true}
                clickable={true}
            >
                <p>This card is in disabled state.</p>
                {#snippet actions()}
                    <button class="btn btn-primary btn-sm">Action</button>
                {/snippet}
            </Card>

            <!-- Card with Badge -->
            <Card title="Card with Badge" subtitle="Has a status badge">
                {#snippet badge()}
                    <div class="badge badge-secondary">NEW</div>
                {/snippet}
                <p>This card has a badge in the title area.</p>
            </Card>
        </div>
    </section>

    <!-- Custom Styling -->
    <section>
        <h2 class="text-xl font-semibold mb-4">Custom Styling</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card
                title="Custom Classes"
                class="bg-gradient-to-br from-primary to-secondary text-primary-content"
                titleClass="text-white"
                bodyClass="text-primary-content/90"
            >
                <p>This card uses custom CSS classes for styling.</p>
            </Card>

            <Card title="No Shadow" shadow="none" variant="bordered">
                <p>This card has no shadow effect.</p>
            </Card>
        </div>
    </section>
</div>
