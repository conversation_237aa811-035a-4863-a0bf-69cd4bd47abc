<script lang="ts">
	interface Props {
		width?: string;
		height?: string;
		shape?: "rectangle" | "circle" | "text";
		lines?: number;
		className?: string;
		animate?: boolean;
	}

	let {
		width = "100%",
		height = "1rem",
		shape = "rectangle",
		lines = 1,
		className = "",
		animate = true,
	}: Props = $props();

	const baseClass = animate ? "skeleton" : "bg-base-300";
	const shapeClass = shape === "circle" ? "rounded-full" : shape === "text" ? "rounded" : "rounded-lg";
</script>

{#if shape === "text" && lines > 1}
	<div class="space-y-2 {className}">
		{#each Array(lines) as _, index}
			<div 
				class="{baseClass} {shapeClass}" 
				style="width: {index === lines - 1 ? '75%' : width}; height: {height};"
			></div>
		{/each}
	</div>
{:else}
	<div 
		class="{baseClass} {shapeClass} {className}" 
		style="width: {width}; height: {height};"
	></div>
{/if}
