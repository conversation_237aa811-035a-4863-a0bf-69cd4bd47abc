<script lang="ts">
  interface Props {
    title?: string;
    description?: string;
    keywords?: string;
    image?: string;
    url?: string;
    type?: 'website' | 'article' | 'product';
    noindex?: boolean;
    canonical?: string;
  }

  let {
    title = 'WebShop Platform - ระบบเช่าใช้งานเว็บไซต์ครบวงจร',
    description = 'สร้างและจัดการเว็บไซต์ร้านค้าออนไลน์ของคุณได้อย่างง่ายดาย พร้อมระบบครบครัน เริ่มต้นเพียง 29 บาท',
    keywords = 'เว็บไซต์, ร้านค้าออนไลน์, อีคอมเมิร์ส, สร้างเว็บไซต์, ระบบขายของออนไลน์',
    image = '/images/og-image.jpg',
    url = '',
    type = 'website',
    noindex = false,
    canonical
  }: Props = $props();

  // สร้าง full title
  const fullTitle = title.includes('WebShop Platform') 
    ? title 
    : `${title} | WebShop Platform`;

  // สร้าง full URL
  const fullUrl = url ? `https://webshop-platform.com${url}` : 'https://webshop-platform.com';
  const fullImage = image.startsWith('http') ? image : `https://webshop-platform.com${image}`;
</script>

<svelte:head>
  <!-- Primary Meta Tags -->
  <title>{fullTitle}</title>
  <meta name="title" content={fullTitle} />
  <meta name="description" content={description} />
  <meta name="keywords" content={keywords} />
  
  <!-- Robots -->
  {#if noindex}
    <meta name="robots" content="noindex, nofollow" />
  {:else}
    <meta name="robots" content="index, follow" />
  {/if}
  
  <!-- Canonical URL -->
  {#if canonical}
    <link rel="canonical" href={canonical} />
  {:else if url}
    <link rel="canonical" href={fullUrl} />
  {/if}
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content={type} />
  <meta property="og:url" content={fullUrl} />
  <meta property="og:title" content={fullTitle} />
  <meta property="og:description" content={description} />
  <meta property="og:image" content={fullImage} />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:site_name" content="WebShop Platform" />
  <meta property="og:locale" content="th_TH" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content={fullUrl} />
  <meta property="twitter:title" content={fullTitle} />
  <meta property="twitter:description" content={description} />
  <meta property="twitter:image" content={fullImage} />
  <meta name="twitter:site" content="@webshopplatform" />
  
  <!-- Additional Meta Tags -->
  <meta name="format-detection" content="telephone=no" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="WebShop Platform" />
  
  <!-- Structured Data -->
  <script type="application/ld+json">
    {JSON.stringify({
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "WebShop Platform",
      "description": description,
      "url": fullUrl,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": "https://webshop-platform.com/search?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
      }
    })}
  </script>
</svelte:head>