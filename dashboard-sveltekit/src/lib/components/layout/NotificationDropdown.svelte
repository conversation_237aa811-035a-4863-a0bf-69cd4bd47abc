<script lang="ts">
    import { onMount } from "svelte";
    import Icon from "@iconify/svelte";
    import { notificationStore } from "$lib/stores/notification.svelte";
    import NotificationItem from "./NotificationItem.svelte";
    import { goto } from "$app/navigation";

    interface Props {
        onClose: () => void;
        onMarkAllAsRead: () => void;
    }

    let { onClose, onMarkAllAsRead }: Props = $props();

    // Reactive values
    let notifications = $derived(notificationStore.notifications);
    let unreadCount = $derived(notificationStore.unreadCount);
    let isLoading = $derived(notificationStore.isLoading);
    let error = $derived(notificationStore.error);

    onMount(() => {
        // โหลดการแจ้งเตือนล่าสุด
        notificationStore.loadNotifications({ limit: 10 });
    });

    function handleViewAll() {
        onClose();
        goto("/dashboard/notifications");
    }

    function handleMarkAllAsRead() {
        onMarkAllAsRead();
    }

    function handleNotificationClick(notificationId: string) {
        // ทำเครื่องหมายว่าอ่านแล้วเมื่อคลิก
        const notification = notifications.find(
            (n) => n._id === notificationId,
        );
        if (notification && notification.status === "unread") {
            notificationStore.markAsRead([notificationId]);
        }
    }

    function handleDeleteNotification(notificationId: string) {
        notificationStore.deleteNotification(notificationId);
    }
</script>

<div class="card bg-base-100 shadow-xl w-96 max-h-96 overflow-hidden">
    <!-- Header -->
    <div class="card-body p-4">
        <div class="flex items-center justify-between mb-3">
            <h3 class="font-semibold text-lg flex items-center gap-2">
                <Icon icon="solar:bell-bold" class="size-5" />
                การแจ้งเตือน
                {#if unreadCount > 0}
                    <span class="badge badge-error badge-sm">{unreadCount}</span
                    >
                {/if}
            </h3>

            <div class="flex gap-1">
                {#if unreadCount > 0}
                    <button
                        onclick={handleMarkAllAsRead}
                        class="btn btn-ghost btn-xs"
                        title="อ่านทั้งหมด"
                    >
                        <Icon icon="solar:check-read-bold" class="size-4" />
                    </button>
                {/if}

                <button
                    onclick={onClose}
                    class="btn btn-ghost btn-xs"
                    title="ปิด"
                >
                    <Icon icon="solar:close-circle-bold" class="size-4" />
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="max-h-64 overflow-y-auto">
            {#if isLoading}
                <div class="flex justify-center py-4">
                    <span class="loading loading-spinner loading-md"></span>
                </div>
            {:else if error}
                <div class="text-center py-4 text-error">
                    <Icon
                        icon="solar:danger-circle-bold"
                        class="size-8 mx-auto mb-2"
                    />
                    <p class="text-sm">{error}</p>
                    <button
                        onclick={() =>
                            notificationStore.loadNotifications({ limit: 10 })}
                        class="btn btn-error btn-xs mt-2"
                    >
                        ลองใหม่
                    </button>
                </div>
            {:else if notifications.length === 0}
                <div class="text-center py-8 text-base-content/60">
                    <Icon
                        icon="solar:bell-off-bold"
                        class="size-12 mx-auto mb-3 opacity-50"
                    />
                    <p class="text-sm">ไม่มีการแจ้งเตือน</p>
                </div>
            {:else}
                <div class="space-y-2">
                    {#each notifications as notification (notification._id)}
                        <NotificationItem
                            {notification}
                            onClick={() =>
                                handleNotificationClick(notification._id)}
                            onDelete={() =>
                                handleDeleteNotification(notification._id)}
                        />
                    {/each}
                </div>
            {/if}
        </div>

        <!-- Footer -->
        {#if notifications.length > 0}
            <div class="divider my-2"></div>
            <div class="flex justify-between">
                <div class="flex gap-1">
                    <button
                        onclick={handleViewAll}
                        class="btn btn-ghost btn-sm"
                    >
                        <Icon icon="solar:list-bold" class="size-4" />
                        ดูทั้งหมด
                    </button>

                    <a
                        href="/dashboard/notifications/settings"
                        class="btn btn-ghost btn-sm"
                        onclick={onClose}
                    >
                        <Icon icon="solar:settings-bold" class="size-4" />
                        ตั้งค่า
                    </a>
                </div>

                {#if unreadCount > 0}
                    <button
                        onclick={handleMarkAllAsRead}
                        class="btn btn-primary btn-sm"
                    >
                        <Icon icon="solar:check-read-bold" class="size-4" />
                        อ่านทั้งหมด
                    </button>
                {/if}
            </div>
        {/if}
    </div>
</div>

<style>
    .card {
        border: 1px solid hsl(var(--border-color, var(--fallback-b2)));
    }

    /* Custom scrollbar */
    .overflow-y-auto::-webkit-scrollbar {
        width: 4px;
    }

    .overflow-y-auto::-webkit-scrollbar-track {
        background: transparent;
    }

    .overflow-y-auto::-webkit-scrollbar-thumb {
        background: hsl(var(--bc) / 0.2);
        border-radius: 2px;
    }

    .overflow-y-auto::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--bc) / 0.3);
    }
</style>
