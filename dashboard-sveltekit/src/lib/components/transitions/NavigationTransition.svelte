<script lang="ts">
    import { page } from "$app/state";
    import { fade } from "svelte/transition";

    interface Props {
        children: any;
    }

    let { children }: Props = $props();

    // ใช้ page state แทน navigating ที่ deprecated
    let isNavigating = $state(false);
    let currentPath = $derived(page.url.pathname);
    let previousPath = $state("");

    // ตรวจสอบการเปลี่ยนหน้า
    $effect(() => {
        if (currentPath !== previousPath && previousPath !== "") {
            isNavigating = true;
            setTimeout(() => {
                isNavigating = false;
            }, 300);
        }
        previousPath = currentPath;
    });
</script>

<div class="relative">
    <!-- Navigation Loading Bar -->
    {#if isNavigating}
        <div
            in:fade={{ duration: 100 }}
            out:fade={{ duration: 300 }}
            class="fixed top-0 left-0 right-0 z-50"
        >
            <div class="h-1 bg-primary animate-pulse">
                <div class="h-full bg-primary-focus animate-loading-bar"></div>
            </div>
        </div>
    {/if}

    <!-- Page Content -->
    <div class="transition-all duration-500" class:opacity-60={isNavigating}>
        {@render children()}
    </div>
</div>

<style>
    @keyframes loading-bar {
        0% {
            width: 0%;
            margin-left: 0%;
        }
        50% {
            width: 75%;
            margin-left: 12.5%;
        }
        100% {
            width: 100%;
            margin-left: 0%;
        }
    }

    .animate-loading-bar {
        animation: loading-bar 2s ease-in-out infinite;
    }
</style>
