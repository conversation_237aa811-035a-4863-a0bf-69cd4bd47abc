<script lang="ts">
    import { fly, fade } from "svelte/transition";
    import { quintOut } from "svelte/easing";

    interface Props {
        items: any[];
        children: (item: any, index: number) => any;
        staggerDelay?: number;
        animationType?: "fly" | "fade";
        direction?: "up" | "down" | "left" | "right";
    }

    let {
        items,
        children,
        staggerDelay = 80,
        animationType = "fade",
        direction = "up",
    }: Props = $props();

    // กำหนด transition parameters
    function getTransitionParams(index: number) {
        const delay = index * staggerDelay;

        if (animationType === "fade") {
            return {
                transition: fade,
                params: {
                    duration: 300,
                    delay,
                    easing: quintOut,
                },
            };
        }

        // fly animation
        let x = 0,
            y = 0;
        switch (direction) {
            case "up":
                y = 20;
                break;
            case "down":
                y = -20;
                break;
            case "left":
                x = 20;
                break;
            case "right":
                x = -20;
                break;
        }

        return {
            transition: fly,
            params: {
                duration: 400,
                delay,
                x,
                y,
                easing: quintOut,
            },
        };
    }
</script>

<div class="staggered-list">
    {#each items as item, index (item.id || index)}
        {@const transitionConfig = getTransitionParams(index)}
        <div
            in:transitionConfig.transition={transitionConfig.params}
            class="staggered-item"
        >
            {@render children(item, index)}
        </div>
    {/each}
</div>

<style>
    .staggered-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .staggered-item {
        width: 100%;
    }
</style>
