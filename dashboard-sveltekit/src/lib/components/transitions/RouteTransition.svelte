<script lang="ts">
    import { fade } from "svelte/transition";
    import { quintOut } from "svelte/easing";
    import { page } from "$app/state";

    interface Props {
        children: any;
    }

    let { children }: Props = $props();

    let currentPath = $derived(page.url.pathname);

    // กำหนด fade transition parameters สำหรับทุกหน้า
    function getFadeDuration(path: string) {
        if (path.includes("/notifications/settings")) {
            return 500; // ช้าหน่อยสำหรับ settings
        }
        if (path.includes("/notifications")) {
            return 400; // ปานกลางสำหรับ notifications
        }
        return 350; // เร็วสำหรับหน้าอื่นๆ
    }

    let fadeParams = $derived({
        duration: getFadeDuration(currentPath),
        easing: quintOut,
    });
</script>

{#key currentPath}
    <div
        in:fade={fadeParams}
        out:fade={{
            ...fadeParams,
            duration: fadeParams.duration / 2,
        }}
        class="route-container"
    >
        {@render children()}
    </div>
{/key}

<style>
    .route-container {
        width: 100%;
        min-height: 100%;
    }
</style>
