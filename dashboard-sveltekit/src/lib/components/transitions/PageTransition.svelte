<script lang="ts">
    import { fade } from "svelte/transition";
    import { quintOut } from "svelte/easing";
    import { page } from "$app/state";

    interface Props {
        children: any;
        duration?: number;
    }

    let { children, duration = 400 }: Props = $props();

    // ใช้ page.url.pathname เป็น key สำหรับ transition
    let currentPath = $derived(page.url.pathname);

    // กำหนด fade transition parameters
    const fadeParams = {
        duration,
        easing: quintOut,
    };
</script>

{#key currentPath}
    <div
        in:fade={fadeParams}
        out:fade={{ ...fadeParams, duration: duration / 2 }}
    >
        {@render children()}
    </div>
{/key}

<style>
    div {
        width: 100%;
        height: 100%;
    }
</style>
