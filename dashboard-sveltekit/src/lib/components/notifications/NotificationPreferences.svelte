<script lang="ts">
    import Icon from "@iconify/svelte";

    interface Props {
        preferences: Record<string, boolean>;
        onSave: (preferences: Record<string, boolean>) => void;
        isSaving: boolean;
    }

    let { preferences, onSave, isSaving }: Props = $props();

    // สร้าง local state สำหรับการแก้ไข
    let localPreferences = $state({ ...preferences });

    // อัปเดต local state เมื่อ props เปลี่ยน
    $effect(() => {
        localPreferences = { ...preferences };
    });

    // ตัวเลือกการแจ้งเตือน
    const preferenceOptions = [
        {
            key: "orderUpdates",
            label: "อัปเดตคำสั่งซื้อ",
            description: "แจ้งเตือนเมื่อมีการเปลี่ยนแปลงสถานะคำสั่งซื้อ",
            icon: "solar:bag-smile-bold",
            color: "text-success",
            category: "การขาย",
        },
        {
            key: "productAlerts",
            label: "แจ้งเตือนสินค้า",
            description: "แจ้งเตือนเกี่ยวกับสินค้าใหม่และการเปลี่ยนแปลง",
            icon: "solar:box-bold",
            color: "text-info",
            category: "การขาย",
        },
        {
            key: "promotions",
            label: "โปรโมชั่น",
            description: "แจ้งเตือนเกี่ยวกับโปรโมชั่นและส่วนลด",
            icon: "solar:gift-bold",
            color: "text-secondary",
            category: "การตลาด",
        },
        {
            key: "systemMessages",
            label: "ข้อความระบบ",
            description: "แจ้งเตือนสำคัญจากระบบ",
            icon: "solar:settings-bold",
            color: "text-warning",
            category: "ระบบ",
        },
        {
            key: "chatMessages",
            label: "ข้อความแชท",
            description: "แจ้งเตือนข้อความใหม่ในแชท",
            icon: "solar:chat-round-dots-bold",
            color: "text-primary",
            category: "การสื่อสาร",
        },
        {
            key: "affiliateUpdates",
            label: "อัปเดตพันธมิตร",
            description: "แจ้งเตือนเกี่ยวกับโปรแกรมพันธมิตร",
            icon: "solar:users-group-rounded-bold",
            color: "text-accent",
            category: "การตลาด",
        },
        {
            key: "topupAlerts",
            label: "แจ้งเตือนเติมเงิน",
            description: "แจ้งเตือนเมื่อมีการเติมเงินเข้าบัญชี",
            icon: "solar:wallet-money-bold",
            color: "text-success",
            category: "การเงิน",
        },
        {
            key: "membershipUpdates",
            label: "อัปเดตสมาชิก",
            description: "แจ้งเตือนเกี่ยวกับสมาชิกใหม่",
            icon: "solar:user-plus-bold",
            color: "text-info",
            category: "การจัดการ",
        },
        {
            key: "expiryWarnings",
            label: "แจ้งเตือนวันหมดอายุ",
            description: "แจ้งเตือนก่อนบัญชีหมดอายุ",
            icon: "solar:calendar-mark-bold",
            color: "text-error",
            category: "ระบบ",
        },
        {
            key: "inventoryAlerts",
            label: "แจ้งเตือนคลังสินค้า",
            description: "แจ้งเตือนเมื่อสต็อกสินค้าต่ำ (สำหรับแอดมิน)",
            icon: "solar:box-minimalistic-bold",
            color: "text-warning",
            category: "การจัดการ",
        },
        {
            key: "paymentNotifications",
            label: "แจ้งเตือนการชำระเงิน",
            description: "แจ้งเตือนเกี่ยวกับการชำระเงิน",
            icon: "solar:card-bold",
            color: "text-success",
            category: "การเงิน",
        },
        {
            key: "securityAlerts",
            label: "แจ้งเตือนความปลอดภัย",
            description: "แจ้งเตือนเกี่ยวกับความปลอดภัยของบัญชี",
            icon: "solar:shield-check-bold",
            color: "text-error",
            category: "ระบบ",
        },
        {
            key: "marketingMessages",
            label: "ข้อความการตลาด",
            description: "แจ้งเตือนเกี่ยวกับการตลาดและข่าวสาร",
            icon: "solar:megaphone-loud-bold",
            color: "text-secondary",
            category: "การตลาด",
        },
    ];

    // จัดกลุ่มตามหมวดหมู่
    let groupedPreferences = $derived(() => {
        const groups: Record<string, typeof preferenceOptions> = {};

        preferenceOptions.forEach((option) => {
            if (!groups[option.category]) {
                groups[option.category] = [];
            }
            groups[option.category].push(option);
        });

        return groups;
    });

    // จัดการการเปลี่ยนแปลง
    function handleToggle(key: string) {
        localPreferences[key] = !localPreferences[key];
    }

    // บันทึกการตั้งค่า
    function handleSave() {
        onSave(localPreferences);
    }

    // เปิด/ปิดทั้งหมดในหมวดหมู่
    function toggleCategory(category: string, enabled: boolean) {
        const categoryOptions = groupedPreferences()[category];
        categoryOptions.forEach((option) => {
            localPreferences[option.key] = enabled;
        });
    }

    // ตรวจสอบว่าหมวดหมู่เปิดทั้งหมดหรือไม่
    function isCategoryEnabled(category: string): boolean {
        const categoryOptions = groupedPreferences()[category];
        return categoryOptions.every((option) => localPreferences[option.key]);
    }

    // ตรวจสอบว่าหมวดหมู่เปิดบางส่วนหรือไม่
    function isCategoryPartial(category: string): boolean {
        const categoryOptions = groupedPreferences()[category];
        const enabledCount = categoryOptions.filter(
            (option) => localPreferences[option.key],
        ).length;
        return enabledCount > 0 && enabledCount < categoryOptions.length;
    }

    // ตรวจสอบว่ามีการเปลี่ยนแปลงหรือไม่
    let hasChanges = $derived(() => {
        return JSON.stringify(preferences) !== JSON.stringify(localPreferences);
    });
</script>

<div class="card bg-base-100 shadow-xl">
    <div class="card-body">
        <div class="flex items-center justify-between mb-4">
            <h2 class="card-title flex items-center gap-2">
                <Icon icon="solar:bell-bing-bold" class="size-6" />
                ประเภทการแจ้งเตือน
            </h2>

            {#if hasChanges()}
                <button
                    onclick={handleSave}
                    class="btn btn-primary btn-sm"
                    class:loading={isSaving}
                    disabled={isSaving}
                >
                    {#if !isSaving}
                        <Icon icon="solar:diskette-bold" class="size-4" />
                    {/if}
                    บันทึก
                </button>
            {/if}
        </div>

        <p class="text-base-content/70 mb-6">
            เลือกประเภทการแจ้งเตือนที่คุณต้องการรับ
        </p>

        <div class="space-y-6">
            {#each Object.entries(groupedPreferences()) as [category, options]}
                <div class="space-y-3">
                    <!-- Category Header -->
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold text-lg">{category}</h3>

                        <div class="flex items-center gap-2">
                            <button
                                onclick={() => toggleCategory(category, false)}
                                class="btn btn-ghost btn-xs"
                                disabled={!options.some(
                                    (option) => localPreferences[option.key],
                                )}
                            >
                                ปิดทั้งหมด
                            </button>
                            <button
                                onclick={() => toggleCategory(category, true)}
                                class="btn btn-ghost btn-xs"
                                disabled={isCategoryEnabled(category)}
                            >
                                เปิดทั้งหมด
                            </button>
                        </div>
                    </div>

                    <!-- Category Options -->
                    <div class="grid grid-cols-1 gap-3">
                        {#each options as option}
                            <div class="form-control">
                                <label
                                    class="label cursor-pointer justify-start gap-4 p-4 rounded-lg border hover:bg-base-50 transition-colors"
                                >
                                    <input
                                        type="checkbox"
                                        class="checkbox checkbox-primary"
                                        checked={localPreferences[option.key]}
                                        onchange={() =>
                                            handleToggle(option.key)}
                                    />

                                    <div class="flex items-center gap-3 flex-1">
                                        <Icon
                                            icon={option.icon}
                                            class="size-6 {option.color}"
                                        />

                                        <div class="flex-1">
                                            <div class="font-medium">
                                                {option.label}
                                            </div>
                                            <div
                                                class="text-sm text-base-content/70"
                                            >
                                                {option.description}
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        {/each}
                    </div>
                </div>
            {/each}
        </div>

        <!-- Summary -->
        <div class="divider"></div>
        <div
            class="flex items-center justify-between text-sm text-base-content/70"
        >
            <span>
                เปิดใช้งาน {Object.values(localPreferences).filter(Boolean)
                    .length} จาก {Object.keys(localPreferences).length} ประเภท
            </span>

            {#if hasChanges()}
                <span class="text-warning"
                    >มีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก</span
                >
            {/if}
        </div>
    </div>
</div>

<style>
    .hover\:bg-base-50:hover {
        background-color: hsl(var(--b1) / 0.5);
    }
</style>
