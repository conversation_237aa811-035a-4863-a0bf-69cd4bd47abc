import { $fetch } from 'ofetch';
import { apiUrl } from '$lib/config';

// ✅ Base API client with ofetch
export const apiClient = $fetch.create({
    baseURL: apiUrl,

    // Default headers
    headers: {
        'Content-Type': 'application/json'
    },

    // Request timeout
    timeout: 30000,

    // Retry configuration
    retry: 1,
    retryDelay: 1000,

    // Global request interceptor
    onRequest({ options }) {
        console.log('🚀 API Request:', options.method, options.baseURL);
    },

    // Global response interceptor
    onResponse({ response }) {
        console.log('📡 API Response:', response.status, response.url);
    },

    // Global error interceptor
    onResponseError({ response, error }) {
        console.error('❌ API Error:', {
            status: response.status,
            url: response.url,
            data: response._data
        });

        // Transform error message
        const message = response._data?.message ||
            response._data?.error ||
            `HTTP ${response.status} Error`;

        throw new Error(message);
    }
});

// ✅ Helper function to create auth headers
export function createAuthHeaders(token: string) {
    return {
        'Authorization': `Bearer ${token}`
    };
}

// ✅ Helper function to get token (for client-side)
export function getToken(): string | null {
    if (typeof window !== 'undefined') {
        return localStorage.getItem('auth_token');
    }
    return null;
}