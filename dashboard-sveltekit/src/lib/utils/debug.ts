import { readable } from 'svelte/store';

/**
 * Composable สำหรับแสดงข้อมูล object ในรูปแบบ JSON
 * @param data - ข้อมูลที่ต้องการแสดง
 * @param indent - จำนวน space สำหรับ indent (default: 2)
 * @returns readable store ที่มี JSON string
 */
export function useJsonDebug(data: any, indent: number = 2) {
    return readable('', (set) => {
        const update = () => {
            try {
                const jsonString = JSON.stringify(data, null, indent);
                set(jsonString);
            } catch (error) {
                set(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        };

        update();
        
        // ถ้า data เป็น reactive (Svelte 5 state) จะ auto update
        if (data && typeof data === 'object' && '$state' in data) {
            // สำหรับ Svelte 5 reactive state
            return () => {};
        }
        
        return () => {};
    });
}

/**
 * ฟังก์ชันสำหรับแสดงข้อมูล object ในรูปแบบ JSON string
 * @param data - ข้อมูลที่ต้องการแสดง
 * @param indent - จำนวน space สำหรับ indent (default: 2)
 * @returns JSON string
 */
export function toJsonString(data: any, indent: number = 2): string {
    try {
        return JSON.stringify(data, null, indent);
    } catch (error) {
        return `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
}

/**
 * ฟังก์ชันสำหรับแสดงข้อมูล object ในรูปแบบ JSON ที่มี syntax highlighting
 * @param data - ข้อมูลที่ต้องการแสดง
 * @param indent - จำนวน space สำหรับ indent (default: 2)
 * @returns HTML string ที่มี syntax highlighting
 */
export function toJsonHtml(data: any, indent: number = 2): string {
    try {
        const jsonString = JSON.stringify(data, null, indent);
        // ใช้ CSS classes สำหรับ syntax highlighting
        return `<pre class="text-xs bg-base-200 p-2 rounded overflow-auto max-h-40"><code>${jsonString}</code></pre>`;
    } catch (error) {
        return `<pre class="text-xs text-error">Error: ${error instanceof Error ? error.message : 'Unknown error'}</pre>`;
    }
} 