import { safeParse, type BaseSchema } from 'valibot';

export interface ValidationResult<T> {
	success: boolean;
	data?: T;
	errors?: Record<string, string>;
}
export function validateForm<T>(
	schema: BaseSchema<unknown, T, any>,
	data: unknown
): ValidationResult<T> {
	const result = safeParse(schema, data);
	if (result.success) {
		return {
			success: true,
			data: result.output
		};
	}
	
	// Convert ValiError to Record<string, string>
	const errors: Record<string, string> = {};
	
	if (result.issues) {
		result.issues.forEach((issue) => {
			const path = issue.path?.[0]?.key;
			if (path && typeof path === 'string') {
				errors[path] = issue.message;
			}
		});
	}
	
	return {
		success: false,
		errors
	};
}

export function getFieldError(
	errors: Record<string, string> | undefined,
	fieldName: string
): string | undefined {
	return errors?.[fieldName];
}

export function hasFieldError(
	errors: Record<string, string> | undefined,
	fieldName: string
): boolean {
	return !!getFieldError(errors, fieldName);
}

export function clearFieldError(
	errors: Record<string, string>,
	fieldName: string
): Record<string, string> {
	const newErrors = { ...errors };
	delete newErrors[fieldName];
	return newErrors;
}

export function clearAllErrors(): Record<string, string> {
	return {};
} 