import { redirect } from '@sveltejs/kit';

export interface Locals {
    user?: any;
    token?: string;
}

export function requireAuth(locals: Locals) {
    if (!locals.user || !locals.token) {
        throw redirect(302, '/signin');
    }
}

export function getAuthData(locals: Locals) {
    return {
        user: locals.user,
        token: locals.token
    };
}

export function requireSiteAccess(locals: Locals, siteId: string) {
    requireAuth(locals);
    
    // เพิ่มการตรวจสอบสิทธิ์เข้าถึง site ถ้าจำเป็น
    // เช่น ตรวจสอบว่า user เป็น owner หรือ member ของ site นี้
    
    return {
        user: locals.user,
        token: locals.token,
        siteId
    };
} 