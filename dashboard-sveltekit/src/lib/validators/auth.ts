import { object, string, boolean, optional } from 'valibot';

// Simple validation functions
const validateEmail = (value: string) => {
	if (!value) return 'กรุณากรอกอีเมล';
	if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'รูปแบบอีเมลไม่ถูกต้อง';
	return undefined;
};

const validatePassword = (value: string) => {
	if (!value) return 'กรุณากรอกรหัสผ่าน';
	if (value.length < 6) return 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
	if (value.length > 128) return 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร';
	return undefined;
};

const validateStrongPassword = (value: string) => {
	if (!value) return 'กรุณากรอกรหัสผ่าน';
	if (value.length < 6) return 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
	if (value.length > 128) return 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร';
	// ตรวจสอบให้ใช้เฉพาะภาษาอังกฤษ ตัวเลข และอักขระพิเศษเท่านั้น
	if (!/^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]+$/.test(value)) {
		return 'รหัสผ่านต้องใช้เฉพาะภาษาอังกฤษ ตัวเลข และอักขระพิเศษเท่านั้น';
	}
	return undefined;
};

// Login Schema
export const loginSchema = object({
	email: string(),
	password: string(),
	rememberMe: boolean()
});

// Register Schema
export const registerSchema = object({
	email: string(),
	password: string(),
	confirmPassword: string(),
	agreeToTerms: boolean()
});

// Password Reset Schema
export const passwordResetSchema = object({
	email: string()
});

// Change Password Schema
export const changePasswordSchema = object({
	currentPassword: string(),
	newPassword: string(),
	confirmNewPassword: string()
});

// Profile Update Schema
export const profileUpdateSchema = object({
	firstName: string(),
	lastName: string(),
	email: string(),
	phone: optional(string()),
	bio: optional(string())
});

// Validation functions
export function validateLoginForm(data: any) {
	const errors: Record<string, string> = {};
	
	const emailError = validateEmail(data.email);
	if (emailError) errors.email = emailError;
	
	const passwordError = validatePassword(data.password);
	if (passwordError) errors.password = passwordError;
	
	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

export function validateRegisterForm(data: any) {
	const errors: Record<string, string> = {};
	
	const emailError = validateEmail(data.email);
	if (emailError) errors.email = emailError;
	
	const passwordError = validateStrongPassword(data.password);
	if (passwordError) errors.password = passwordError;
	
	if (data.password !== data.confirmPassword) {
		errors.confirmPassword = 'รหัสผ่านไม่ตรงกัน';
	}
	
	if (!data.agreeToTerms) {
		errors.agreeToTerms = 'กรุณายอมรับเงื่อนไขการใช้งาน';
	}
	
	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

// Types
export interface LoginForm {
	email: string;
	password: string;
	rememberMe: boolean;
}

export interface RegisterForm {
	email: string;
	password: string;
	confirmPassword: string;
	agreeToTerms: boolean;
}

export interface PasswordResetForm {
	email: string;
}

export interface ChangePasswordForm {
	currentPassword: string;
	newPassword: string;
	confirmNewPassword: string;
}

export interface ProfileUpdateForm {
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
	bio?: string;
} 