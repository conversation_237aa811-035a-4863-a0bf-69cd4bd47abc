# Valibot Validation - แก้ไขแล้ว

## การแก้ไขปัญหา Valibot v1.1.0

### ปัญหาที่พบ:
- Valibot v1.1.0 มี API ที่เปลี่ยนไป
- Linter errors เกี่ยวกับ type compatibility
- การใช้งาน `string([...])` ไม่ทำงานกับ validators หลายตัว

### วิธีแก้ไข:
1. **Downgrade Valibot** เป็น v0.31.0
2. **สร้าง Custom Validation Functions** แทนการใช้ Valibot validators
3. **ใช้ Simple Schema** ที่ไม่มี complex validators

## การใช้งาน Validation ใหม่

### 1. การใช้งานใน Component

```svelte
<script lang="ts">
	import { validateLoginForm, type LoginForm } from '$lib/validators/auth-simple';
	
	let formData = $state<LoginForm>({
		email: '',
		password: '',
		rememberMe: false
	});
	let errors = $state({});
	
	function handleSubmit() {
		const result = validateLoginForm(formData);
		if (result.success) {
			// Submit form
			console.log('Form is valid:', result.data);
		} else {
			// Handle errors
			errors = result.errors;
		}
	}
</script>
```

### 2. Validation Functions ที่มี

#### **Auth Validation (`auth-simple.ts`)**
- `validateLoginForm(data)` - ตรวจสอบ email format และ password length
- `validateRegisterForm(data)` - ตรวจสอบ strong password, confirmation, และ terms

#### **Common Validation (`common.ts`)**
- `validateSearchForm(data)` - ตรวจสอบ search query length
- `validateFileUploadForm(data)` - ตรวจสอบ file size และ type
- `validateAddressForm(data)` - ตรวจสอบ address fields
- `validateSocialMediaForm(data)` - ตรวจสอบ URL format
- `validateSettingsForm(data)` - ตรวจสอบ enum values

### 3. Error Handling

```typescript
// ตัวอย่างการใช้งาน
const result = validateLoginForm({
	email: '<EMAIL>',
	password: 'password123',
	rememberMe: true
});

if (result.success) {
	console.log('Valid form');
} else {
	console.log('Errors:', result.errors);
	// { email: 'รูปแบบอีเมลไม่ถูกต้อง' }
}
```

### 4. Types ที่มี

#### **Auth Types**
```typescript
export interface LoginForm {
	email: string;
	password: string;
	rememberMe: boolean;
}

export interface RegisterForm {
	email: string;
	password: string;
	confirmPassword: string;
	agreeToTerms: boolean;
}
```

#### **Common Types**
```typescript
export interface SearchForm {
	query?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: string;
}

export interface FileUploadForm {
	filename: string;
	size: number;
	type: string;
}

export interface AddressForm {
	street: string;
	city: string;
	state: string;
	postalCode: string;
	country: string;
}

export interface SocialMediaForm {
	facebook?: string;
	twitter?: string;
	instagram?: string;
	linkedin?: string;
	youtube?: string;
}

export interface SettingsForm {
	notifications: {
		email: boolean;
		push: boolean;
		sms: boolean;
	};
	privacy: {
		profileVisibility: string;
		showEmail: boolean;
		showPhone: boolean;
	};
	theme: string;
}
```

## ข้อดีของการแก้ไข

1. **No Linter Errors** - ไม่มี TypeScript errors
2. **Simple & Clean** - โค้ดอ่านง่าย
3. **Custom Validation** - สามารถปรับแต่งได้ตามต้องการ
4. **Type Safety** - ยังคง type safety
5. **Performance** - เร็วและมีประสิทธิภาพ

## การใช้งานใน SvelteKit

### Signin Page
```svelte
<script lang="ts">
	import { validateLoginForm } from '$lib/validators/auth-simple';
	
	function handleSubmit() {
		const result = validateLoginForm(formData);
		if (result.success) {
			// Process login
		} else {
			errors = result.errors;
		}
	}
</script>
```

### File Upload
```svelte
<script lang="ts">
	import { validateFileUploadForm } from '$lib/validators/common';
	
	function handleFileUpload(file: File) {
		const data = {
			filename: file.name,
			size: file.size,
			type: file.type
		};
		
		const result = validateFileUploadForm(data);
		if (result.success) {
			// Upload file
		} else {
			errors = result.errors;
		}
	}
</script>
```

### Address Form
```svelte
<script lang="ts">
	import { validateAddressForm } from '$lib/validators/common';
	
	function handleAddressSubmit() {
		const result = validateAddressForm(formData);
		if (result.success) {
			// Save address
		} else {
			errors = result.errors;
		}
	}
</script>
```

## การเพิ่ม Validation Rules

หากต้องการเพิ่ม validation rules ใหม่:

```typescript
// เพิ่มใน auth-simple.ts หรือ common.ts
const validatePhone = (value: string) => {
	if (!value) return undefined; // optional
	if (!/^[0-9+\-\s()]*$/.test(value)) return 'หมายเลขโทรศัพท์ไม่ถูกต้อง';
	if (value.length > 20) return 'หมายเลขโทรศัพท์ต้องไม่เกิน 20 ตัวอักษร';
	return undefined;
};

// ใช้ใน validation function
export function validateProfileForm(data: any) {
	const errors: Record<string, string> = {};
	
	const phoneError = validatePhone(data.phone);
	if (phoneError) errors.phone = phoneError;
	
	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}
```

## Custom Validation Functions

### Email Validation
```typescript
const validateEmail = (value: string) => {
	if (!value) return 'กรุณากรอกอีเมล';
	if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'รูปแบบอีเมลไม่ถูกต้อง';
	return undefined;
};
```

### Password Validation
```typescript
const validateStrongPassword = (value: string) => {
	if (!value) return 'กรุณากรอกรหัสผ่าน';
	if (value.length < 8) return 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร';
	if (value.length > 128) return 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร';
	if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
		return 'รหัสผ่านต้องมีตัวพิมพ์เล็ก ตัวพิมพ์ใหญ่ และตัวเลข';
	}
	return undefined;
};
```

### File Validation
```typescript
const validateFileSize = (value: number) => {
	if (value > 10 * 1024 * 1024) return 'ไฟล์ต้องมีขนาดไม่เกิน 10MB';
	return undefined;
};

const validateFileType = (value: string) => {
	if (!/^(image\/|application\/pdf|text\/)/.test(value)) {
		return 'ไฟล์ต้องเป็นรูปภาพ, PDF หรือข้อความเท่านั้น';
	}
	return undefined;
};
```

### Enum Validation
```typescript
const validateEnum = (value: string, allowedValues: string[], fieldName: string) => {
	if (!allowedValues.includes(value)) {
		return `${fieldName} ต้องเป็น ${allowedValues.join(', ')}`;
	}
	return undefined;
};
```

## สรุป

การแก้ไขนี้ทำให้:
- ✅ ไม่มี linter errors
- ✅ Build สำเร็จ
- ✅ Validation ทำงานได้ปกติ
- ✅ Type safety ยังคงอยู่
- ✅ โค้ดอ่านง่ายและบำรุงรักษาง่าย
- ✅ รองรับ validation หลากหลายรูปแบบ 