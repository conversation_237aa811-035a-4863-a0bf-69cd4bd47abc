/* Page Transition Animations - Fade Effects */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes fadeInSlow {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOutSlow {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

/* Loading Animations */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -30px, 0);
    }

    70% {
        transform: translate3d(0, -15px, 0);
    }

    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Notification Animations - Fade Effects */
@keyframes fadeInFromTop {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOutToTop {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes wiggle {

    0%,
    7% {
        transform: rotateZ(0);
    }

    15% {
        transform: rotateZ(-15deg);
    }

    20% {
        transform: rotateZ(10deg);
    }

    25% {
        transform: rotateZ(-10deg);
    }

    30% {
        transform: rotateZ(6deg);
    }

    35% {
        transform: rotateZ(-4deg);
    }

    40%,
    100% {
        transform: rotateZ(0);
    }
}

/* Utility Classes - Fade Effects */
.animate-fade-in {
    animation: fadeIn 0.4s ease-out;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-in;
}

.animate-fade-in-slow {
    animation: fadeInSlow 0.6s ease-out;
}

.animate-fade-out-slow {
    animation: fadeOutSlow 0.5s ease-in;
}

.animate-fade-in-up {
    animation: fadeInUp 0.4s ease-out;
}

.animate-fade-out-down {
    animation: fadeOutDown 0.3s ease-in;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out;
}

.animate-scale-out {
    animation: scaleOut 0.3s ease-in;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
    animation: shimmer 2s infinite;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent);
    background-size: 200px 100%;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-fade-in-top {
    animation: fadeInFromTop 0.4s ease-out;
}

.animate-fade-out-top {
    animation: fadeOutToTop 0.3s ease-in;
}

.animate-wiggle {
    animation: wiggle 0.5s ease-in-out;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.2s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Smooth Transitions */
.transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg,
            hsl(var(--b2)) 25%,
            hsl(var(--b3)) 50%,
            hsl(var(--b2)) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Page Transition Container */
.page-transition {
    position: relative;
    overflow: hidden;
}

.page-transition-enter {
    animation: fadeInUp 0.4s ease-out;
}

.page-transition-leave {
    animation: fadeOutDown 0.3s ease-in;
}

/* Notification Bell Animation */
.notification-bell {
    transition: transform 0.2s ease;
}

.notification-bell:hover {
    transform: scale(1.1);
}

.notification-bell.has-notifications {
    animation: wiggle 0.5s ease-in-out;
}

/* Stagger Animation Delays */
.stagger-1 {
    animation-delay: 0.1s;
}

.stagger-2 {
    animation-delay: 0.2s;
}

.stagger-3 {
    animation-delay: 0.3s;
}

.stagger-4 {
    animation-delay: 0.4s;
}

.stagger-5 {
    animation-delay: 0.5s;
}

/* Responsive Animations */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}