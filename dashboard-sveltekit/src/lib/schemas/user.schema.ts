// ✅ User Schema - Types และ Validation
import type { User, UpdateUserData } from '$lib/types/user';

// User Form Data Types
export interface UpdateProfileData {
    firstName?: string;
    lastName?: string;
    avatar?: string;
    cover?: string;
}

export interface ChangePasswordData {
    currentPassword: string;
    newPassword: string;
    confirmPassword?: string;
}

export interface UpdateAvatarData {
    avatar: File;
}

// Validation Functions
export function validateUpdateProfileData(data: UpdateProfileData): string | null {
    // firstName และ lastName เป็น optional แต่ถ้ามีต้องไม่เป็นค่าว่าง
    if (data.firstName !== undefined && !data.firstName.trim()) {
        return 'ชื่อจริงไม่สามารถเป็นค่าว่างได้';
    }

    if (data.lastName !== undefined && !data.lastName.trim()) {
        return 'นามสกุลไม่สามารถเป็นค่าว่างได้';
    }

    // ตรวจสอบความยาวของชื่อ
    if (data.firstName && data.firstName.trim().length > 50) {
        return 'ชื่อจริงต้องไม่เกิน 50 ตัวอักษร';
    }

    if (data.lastName && data.lastName.trim().length > 50) {
        return 'นามสกุลต้องไม่เกิน 50 ตัวอักษร';
    }

    return null;
}

export function validateChangePasswordData(data: ChangePasswordData): string | null {
    if (!data.currentPassword) {
        return 'กรุณากรอกรหัสผ่านปัจจุบัน';
    }

    if (!data.newPassword) {
        return 'กรุณากรอกรหัสผ่านใหม่';
    }

    if (data.newPassword.length < 6) {
        return 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
    }

    if (data.confirmPassword && data.newPassword !== data.confirmPassword) {
        return 'รหัสผ่านใหม่ไม่ตรงกัน';
    }

    if (data.currentPassword === data.newPassword) {
        return 'รหัสผ่านใหม่ต้องแตกต่างจากรหัสผ่านปัจจุบัน';
    }

    return null;
}

export function validateAvatarFile(file: File): string | null {
    // ตรวจสอบประเภทไฟล์
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        return 'รองรับเฉพาะไฟล์รูปภาพ (JPEG, PNG, WebP)';
    }

    // ตรวจสอบขนาดไฟล์ (5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        return 'ขนาดไฟล์ต้องไม่เกิน 5MB';
    }

    return null;
}

// Helper Functions
export function sanitizeUserData<T extends Record<string, any>>(data: T): T {
    const sanitized = { ...data };

    // Trim string fields
    Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'string') {
            sanitized[key] = sanitized[key].trim();
        }
    });

    return sanitized;
}

export function formatUserDisplayName(user: User): string {
    if (user.firstName && user.lastName) {
        return `${user.firstName} ${user.lastName}`;
    }

    if (user.firstName) {
        return user.firstName;
    }

    if (user.lastName) {
        return user.lastName;
    }

    return user.email.split('@')[0]; // ใช้ส่วนแรกของอีเมลเป็นชื่อ
}

export function getUserInitials(user: User): string {
    if (user.firstName && user.lastName) {
        return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
    }

    if (user.firstName) {
        return user.firstName.charAt(0).toUpperCase();
    }

    if (user.lastName) {
        return user.lastName.charAt(0).toUpperCase();
    }

    return user.email.charAt(0).toUpperCase();
}