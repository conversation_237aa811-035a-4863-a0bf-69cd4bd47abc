import type { Product } from '$lib/types/product';

// ✅ Product Data Types
export interface CreateProductData {
  name: string;
  description?: string;
  price: number;
  stock: number;
  categoryId?: string;
  images?: string[];
  isActive?: boolean;
  isDigital?: boolean;
  variants?: ProductVariantData[];
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id?: string;
}

export interface ProductStockData {
  stock: number;
  variantId?: string;
}

export interface ProductVariantData {
  name: string;
  price: number;
  stock: number;
  sku?: string;
}

// ✅ Validation Functions
export function validateCreateProductData(data: CreateProductData): string | null {
  if (!data.name?.trim()) {
    return 'กรุณากรอกชื่อสินค้า';
  }

  if (data.name.trim().length < 2) {
    return 'ชื่อสินค้าต้องมีอย่างน้อย 2 ตัวอักษร';
  }

  if (data.name.trim().length > 100) {
    return 'ชื่อสินค้าต้องไม่เกิน 100 ตัวอักษร';
  }

  if (data.price < 0) {
    return 'ราคาต้องไม่น้อยกว่า 0';
  }

  if (data.stock < 0) {
    return 'สต็อกต้องไม่น้อยกว่า 0';
  }

  if (data.description && data.description.trim().length > 1000) {
    return 'คำอธิบายต้องไม่เกิน 1000 ตัวอักษร';
  }

  return null;
}

export function validateUpdateProductData(data: UpdateProductData): string | null {
  if (data.name !== undefined) {
    if (!data.name.trim()) {
      return 'กรุณากรอกชื่อสินค้า';
    }

    if (data.name.trim().length < 2) {
      return 'ชื่อสินค้าต้องมีอย่างน้อย 2 ตัวอักษร';
    }

    if (data.name.trim().length > 100) {
      return 'ชื่อสินค้าต้องไม่เกิน 100 ตัวอักษร';
    }
  }

  if (data.price !== undefined && data.price < 0) {
    return 'ราคาต้องไม่น้อยกว่า 0';
  }

  if (data.stock !== undefined && data.stock < 0) {
    return 'สต็อกต้องไม่น้อยกว่า 0';
  }

  if (data.description && data.description.trim().length > 1000) {
    return 'คำอธิบายต้องไม่เกิน 1000 ตัวอักษร';
  }

  return null;
}

export function validateProductStockData(data: ProductStockData): string | null {
  if (data.stock < 0) {
    return 'สต็อกต้องไม่น้อยกว่า 0';
  }

  if (data.stock > 999999) {
    return 'สต็อกต้องไม่เกิน 999,999';
  }

  return null;
}

// ✅ Sanitization Functions
export function sanitizeProductData(data: CreateProductData | UpdateProductData): CreateProductData | UpdateProductData {
  const sanitized = { ...data };

  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }

  if (sanitized.description) {
    sanitized.description = sanitized.description.trim();
  }

  if (sanitized.price !== undefined) {
    sanitized.price = Number(sanitized.price);
  }

  if (sanitized.stock !== undefined) {
    sanitized.stock = Number(sanitized.stock);
  }

  if (sanitized.images && Array.isArray(sanitized.images)) {
    sanitized.images = sanitized.images.filter(img => img && img.trim());
  }

  return sanitized;
}

// ✅ Type Guards
export function isValidProductData(data: any): data is CreateProductData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.name === 'string' &&
    typeof data.price === 'number' &&
    typeof data.stock === 'number'
  );
}

export function isValidProductStockData(data: any): data is ProductStockData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.stock === 'number'
  );
} 