import type { Customer } from '$lib/types/customer';

// ✅ Customer Data Types
export interface CreateCustomerData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  isActive?: boolean;
  notes?: string;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  id?: string;
}

// ✅ Validation Functions
export function validateCreateCustomerData(data: CreateCustomerData): string | null {
  if (!data.firstName?.trim()) {
    return 'กรุณากรอกชื่อจริง';
  }

  if (data.firstName.trim().length < 2) {
    return 'ชื่อจริงต้องมีอย่างน้อย 2 ตัวอักษร';
  }

  if (data.firstName.trim().length > 50) {
    return 'ชื่อจริงต้องไม่เกิน 50 ตัวอักษร';
  }

  if (!data.lastName?.trim()) {
    return 'กรุณากรอกนามสกุล';
  }

  if (data.lastName.trim().length < 2) {
    return 'นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร';
  }

  if (data.lastName.trim().length > 50) {
    return 'นามสกุลต้องไม่เกิน 50 ตัวอักษร';
  }

  if (!data.email?.trim()) {
    return 'กรุณากรอกอีเมล';
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email.trim())) {
    return 'รูปแบบอีเมลไม่ถูกต้อง';
  }

  if (data.phone && data.phone.trim().length > 20) {
    return 'เบอร์โทรศัพท์ต้องไม่เกิน 20 ตัวอักษร';
  }

  if (data.notes && data.notes.trim().length > 500) {
    return 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร';
  }

  return null;
}

export function validateUpdateCustomerData(data: UpdateCustomerData): string | null {
  if (data.firstName !== undefined) {
    if (!data.firstName.trim()) {
      return 'กรุณากรอกชื่อจริง';
    }

    if (data.firstName.trim().length < 2) {
      return 'ชื่อจริงต้องมีอย่างน้อย 2 ตัวอักษร';
    }

    if (data.firstName.trim().length > 50) {
      return 'ชื่อจริงต้องไม่เกิน 50 ตัวอักษร';
    }
  }

  if (data.lastName !== undefined) {
    if (!data.lastName.trim()) {
      return 'กรุณากรอกนามสกุล';
    }

    if (data.lastName.trim().length < 2) {
      return 'นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร';
    }

    if (data.lastName.trim().length > 50) {
      return 'นามสกุลต้องไม่เกิน 50 ตัวอักษร';
    }
  }

  if (data.email !== undefined) {
    if (!data.email.trim()) {
      return 'กรุณากรอกอีเมล';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email.trim())) {
      return 'รูปแบบอีเมลไม่ถูกต้อง';
    }
  }

  if (data.phone && data.phone.trim().length > 20) {
    return 'เบอร์โทรศัพท์ต้องไม่เกิน 20 ตัวอักษร';
  }

  if (data.notes && data.notes.trim().length > 500) {
    return 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร';
  }

  return null;
}

// ✅ Sanitization Functions
export function sanitizeCustomerData(data: CreateCustomerData | UpdateCustomerData): CreateCustomerData | UpdateCustomerData {
  const sanitized = { ...data };

  if (sanitized.firstName) {
    sanitized.firstName = sanitized.firstName.trim();
  }

  if (sanitized.lastName) {
    sanitized.lastName = sanitized.lastName.trim();
  }

  if (sanitized.email) {
    sanitized.email = sanitized.email.trim().toLowerCase();
  }

  if (sanitized.phone) {
    sanitized.phone = sanitized.phone.trim();
  }

  if (sanitized.notes) {
    sanitized.notes = sanitized.notes.trim();
  }

  if (sanitized.address) {
    if (sanitized.address.street) {
      sanitized.address.street = sanitized.address.street.trim();
    }
    if (sanitized.address.city) {
      sanitized.address.city = sanitized.address.city.trim();
    }
    if (sanitized.address.state) {
      sanitized.address.state = sanitized.address.state.trim();
    }
    if (sanitized.address.zipCode) {
      sanitized.address.zipCode = sanitized.address.zipCode.trim();
    }
    if (sanitized.address.country) {
      sanitized.address.country = sanitized.address.country.trim();
    }
  }

  return sanitized;
}

// ✅ Type Guards
export function isValidCustomerData(data: any): data is CreateCustomerData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.firstName === 'string' &&
    typeof data.lastName === 'string' &&
    typeof data.email === 'string'
  );
}

export function isValidUpdateCustomerData(data: any): data is UpdateCustomerData {
  return (
    typeof data === 'object' &&
    data !== null &&
    (data.firstName === undefined || typeof data.firstName === 'string') &&
    (data.lastName === undefined || typeof data.lastName === 'string') &&
    (data.email === undefined || typeof data.email === 'string')
  );
} 