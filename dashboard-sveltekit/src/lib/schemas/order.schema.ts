// import type { Order } from '$lib/types/order';

// ✅ Order Data Types
export interface CreateOrderData {
  customerId: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    variantId?: string;
  }>;
  shippingAddress: {
    street: string;
    city: string;
    state?: string;
    zipCode: string;
    country: string;
  };
  paymentMethod: string;
  note?: string;
}

export interface UpdateOrderData extends Partial<CreateOrderData> {
  id?: string;
  status?: string;
}

// ✅ Validation Functions
export function validateCreateOrderData(data: CreateOrderData): string | null {
  if (!data.customerId?.trim()) {
    return 'กรุณาเลือกลูกค้า';
  }
  if (!Array.isArray(data.items) || data.items.length === 0) {
    return 'ต้องมีสินค้าอย่างน้อย 1 รายการ';
  }
  for (const item of data.items) {
    if (!item.productId?.trim()) {
      return 'สินค้าแต่ละรายการต้องมีรหัสสินค้า';
    }
    if (item.quantity <= 0) {
      return 'จำนวนสินค้าต้องมากกว่า 0';
    }
    if (item.price < 0) {
      return 'ราคาสินค้าต้องไม่น้อยกว่า 0';
    }
  }
  if (!data.shippingAddress?.street?.trim()) {
    return 'กรุณากรอกที่อยู่จัดส่ง';
  }
  if (!data.shippingAddress?.city?.trim()) {
    return 'กรุณากรอกเมือง';
  }
  if (!data.shippingAddress?.zipCode?.trim()) {
    return 'กรุณากรอกรหัสไปรษณีย์';
  }
  if (!data.shippingAddress?.country?.trim()) {
    return 'กรุณากรอกประเทศ';
  }
  if (!data.paymentMethod?.trim()) {
    return 'กรุณาเลือกวิธีชำระเงิน';
  }
  return null;
}

export function validateUpdateOrderData(data: UpdateOrderData): string | null {
  if (data.items) {
    if (!Array.isArray(data.items) || data.items.length === 0) {
      return 'ต้องมีสินค้าอย่างน้อย 1 รายการ';
    }
    for (const item of data.items) {
      if (!item.productId?.trim()) {
        return 'สินค้าแต่ละรายการต้องมีรหัสสินค้า';
      }
      if (item.quantity <= 0) {
        return 'จำนวนสินค้าต้องมากกว่า 0';
      }
      if (item.price < 0) {
        return 'ราคาสินค้าต้องไม่น้อยกว่า 0';
      }
    }
  }
  if (data.shippingAddress) {
    if (!data.shippingAddress.street?.trim()) {
      return 'กรุณากรอกที่อยู่จัดส่ง';
    }
    if (!data.shippingAddress.city?.trim()) {
      return 'กรุณากรอกเมือง';
    }
    if (!data.shippingAddress.zipCode?.trim()) {
      return 'กรุณากรอกรหัสไปรษณีย์';
    }
    if (!data.shippingAddress.country?.trim()) {
      return 'กรุณากรอกประเทศ';
    }
  }
  if (data.paymentMethod !== undefined && !data.paymentMethod?.trim()) {
    return 'กรุณาเลือกวิธีชำระเงิน';
  }
  return null;
}

// ✅ Sanitization Functions
export function sanitizeOrderData(data: CreateOrderData | UpdateOrderData): CreateOrderData | UpdateOrderData {
  const sanitized = { ...data };
  if (sanitized.note) {
    sanitized.note = sanitized.note.trim();
  }
  if (sanitized.paymentMethod) {
    sanitized.paymentMethod = sanitized.paymentMethod.trim();
  }
  if (sanitized.shippingAddress) {
    sanitized.shippingAddress = { ...sanitized.shippingAddress };
    if (sanitized.shippingAddress.street) {
      sanitized.shippingAddress.street = sanitized.shippingAddress.street.trim();
    }
    if (sanitized.shippingAddress.city) {
      sanitized.shippingAddress.city = sanitized.shippingAddress.city.trim();
    }
    if (sanitized.shippingAddress.state) {
      sanitized.shippingAddress.state = sanitized.shippingAddress.state.trim();
    }
    if (sanitized.shippingAddress.zipCode) {
      sanitized.shippingAddress.zipCode = sanitized.shippingAddress.zipCode.trim();
    }
    if (sanitized.shippingAddress.country) {
      sanitized.shippingAddress.country = sanitized.shippingAddress.country.trim();
    }
  }
  return sanitized;
} 