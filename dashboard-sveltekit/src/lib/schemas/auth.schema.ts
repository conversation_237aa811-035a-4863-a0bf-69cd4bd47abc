// ✅ Auth Schema - Types และ Validation
import type { User } from '$lib/types/user';

// Auth Data Types
export interface SigninData {
    email: string;
    password: string;
}

export interface SignupData {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    confirmPassword?: string;
}

export interface ForgotPasswordData {
    email: string;
}

export interface ResetPasswordData {
    token: string;
    password: string;
    confirmPassword?: string;
}

export interface VerifyEmailData {
    token: string;
}

// Auth Response Types
export interface AuthResponse {
    success: boolean;
    data?: {
        user: User;
        token: string;
        refreshToken: string;
        sessionId?: string;
    };
    error?: string;
    message?: string;
}

export interface RefreshTokenResponse {
    success: boolean;
    data?: {
        token: string;
        refreshToken: string;
        user: User;
    };
    error?: string;
}

// Validation Functions
export function validateSigninData(data: SigninData): string | null {
    console.log("data", data);
    if (!data.email?.trim()) {
        return 'กรุณากรอกอีเมล';
    }

    if (!isValidEmail(data.email)) {
        return 'รูปแบบอีเมลไม่ถูกต้อง';
    }

    if (!data.password) {
        return 'กรุณากรอกรหัสผ่าน';
    }

    if (data.password.length < 6) {
        return 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
    }

    return null;
}

export function validateSignupData(data: SignupData): string | null {
    if (!data.email?.trim()) {
        return 'กรุณากรอกอีเมล';
    }

    if (!isValidEmail(data.email)) {
        return 'รูปแบบอีเมลไม่ถูกต้อง';
    }

    if (!data.password) {
        return 'กรุณากรอกรหัสผ่าน';
    }

    if (data.password.length < 6) {
        return 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
    }

    if (data.confirmPassword && data.password !== data.confirmPassword) {
        return 'รหัสผ่านไม่ตรงกัน';
    }

    return null;
}

export function validateForgotPasswordData(data: ForgotPasswordData): string | null {
    if (!data.email?.trim()) {
        return 'กรุณากรอกอีเมล';
    }

    if (!isValidEmail(data.email)) {
        return 'รูปแบบอีเมลไม่ถูกต้อง';
    }

    return null;
}

export function validateResetPasswordData(data: ResetPasswordData): string | null {
    if (!data.token?.trim()) {
        return 'Token ไม่ถูกต้อง';
    }

    if (!data.password) {
        return 'กรุณากรอกรหัสผ่านใหม่';
    }

    if (data.password.length < 6) {
        return 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
    }

    if (data.confirmPassword && data.password !== data.confirmPassword) {
        return 'รหัสผ่านไม่ตรงกัน';
    }

    return null;
}

// Helper Functions
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

export function sanitizeAuthData<T extends Record<string, any>>(data: T): T {
    const sanitized = { ...data } as T;

    // Trim string fields
    Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'string') {
            (sanitized as any)[key] = (sanitized as any)[key].trim();
        }
    });

    return sanitized;
}