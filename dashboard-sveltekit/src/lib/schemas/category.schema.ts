import type { Category } from '$lib/types/category';

// ✅ Category Data Types
export interface CreateCategoryData {
  name: string;
  description?: string;
  parentId?: string;
  isActive?: boolean;
  image?: string;
  slug?: string;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id?: string;
}

// ✅ Validation Functions
export function validateCreateCategoryData(data: CreateCategoryData): string | null {
  if (!data.name?.trim()) {
    return 'กรุณากรอกชื่อหมวดหมู่';
  }

  if (data.name.trim().length < 2) {
    return 'ชื่อหมวดหมู่ต้องมีอย่างน้อย 2 ตัวอักษร';
  }

  if (data.name.trim().length > 50) {
    return 'ชื่อหมวดหมู่ต้องไม่เกิน 50 ตัวอักษร';
  }

  if (data.description && data.description.trim().length > 500) {
    return 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร';
  }

  if (data.slug && data.slug.trim().length > 100) {
    return 'Slug ต้องไม่เกิน 100 ตัวอักษร';
  }

  return null;
}

export function validateUpdateCategoryData(data: UpdateCategoryData): string | null {
  if (data.name !== undefined) {
    if (!data.name.trim()) {
      return 'กรุณากรอกชื่อหมวดหมู่';
    }

    if (data.name.trim().length < 2) {
      return 'ชื่อหมวดหมู่ต้องมีอย่างน้อย 2 ตัวอักษร';
    }

    if (data.name.trim().length > 50) {
      return 'ชื่อหมวดหมู่ต้องไม่เกิน 50 ตัวอักษร';
    }
  }

  if (data.description && data.description.trim().length > 500) {
    return 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร';
  }

  if (data.slug && data.slug.trim().length > 100) {
    return 'Slug ต้องไม่เกิน 100 ตัวอักษร';
  }

  return null;
}

// ✅ Sanitization Functions
export function sanitizeCategoryData(data: CreateCategoryData | UpdateCategoryData): CreateCategoryData | UpdateCategoryData {
  const sanitized = { ...data };

  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }

  if (sanitized.description) {
    sanitized.description = sanitized.description.trim();
  }

  if (sanitized.slug) {
    sanitized.slug = sanitized.slug.trim().toLowerCase().replace(/\s+/g, '-');
  }

  return sanitized;
}

// ✅ Type Guards
export function isValidCategoryData(data: any): data is CreateCategoryData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.name === 'string'
  );
}

export function isValidUpdateCategoryData(data: any): data is UpdateCategoryData {
  return (
    typeof data === 'object' &&
    data !== null &&
    (data.name === undefined || typeof data.name === 'string')
  );
} 