import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { showLoginSuccess, showLoginError, showRegisterSuccess, showRegisterError, showSignoutSuccess } from '../utils/sweetalert';
import { logger, measurePerformance, logSecurityEvent, LogCategory } from '../utils/logger';
import { tokenCache, cacheUtils } from '../utils/cache';
import { generateSessionId, SECURITY_CONFIG } from '../utils/security';
import { authService } from '../services/auth';

export interface User {
    _id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    cover?: string;
    isEmailVerified: boolean;
    moneyPoint: number;
    goldPoint: number;
    role?: 'admin' | 'user' | 'moderator';
    status?: 'active' | 'inactive';
    createdAt: string;
    updatedAt: string;
}

export interface SigninData {
    email: string;
    password: string;
    rememberMe?: boolean;
}

export interface SignupData {
    email: string;
    password: string;
    confirmPassword: string;
}

class AuthStore {
    private _user = $state<User | null>(null);
    private _isLoading = $state(false);
    private _error = $state<string | null>(null);
    private _refreshToken = $state<string | null>(null);
    private _accessToken = $state<string | null>(null);
    private _isRefreshing = $state(false);
    private _broadcastChannel: BroadcastChannel | null = null;
    private _storageListener: ((event: StorageEvent) => void) | null = null;
    private _isInitialized = $state(false);
    private _currentTabId: string = '';

    constructor() {
        if (browser) {
            this._currentTabId = generateSessionId();
            this.setupCrossTabSync();
            this.setupTokenRefresh();
            // โหลดข้อมูลจาก localStorage เมื่อเริ่มต้น
            this.loadUserFromStorage();
            // ตั้งค่า isInitialized เป็น true เมื่อเริ่มต้น
            this._isInitialized = true;

            logger.info(LogCategory.AUTH, 'store_initialized', 'Auth store initialized', {
                tabId: this._currentTabId,
                hasUser: !!this._user,
                hasAccessToken: !!this._accessToken,
                hasRefreshToken: !!this._refreshToken
            });
        }
    }

    get user() {
        return this._user;
    }

    get isAuthenticated() {
        return this._user !== null;
    }

    get isLoading() {
        return this._isLoading;
    }

    get error() {
        return this._error;
    }

    get isAdmin() {
        return this._user?.role === 'admin';
    }

    get isModerator() {
        return this._user?.role === 'moderator' || this._user?.role === 'admin';
    }

    get isInitialized() {
        return this._isInitialized;
    }

    // ตั้งค่า user จาก SSR
    setUserFromSSR(user: User | null) {
        logger.info(LogCategory.AUTH, 'ssr_user_set', 'Setting user from SSR', {
            hasUser: !!user,
            userId: user?._id,
            tabId: this._currentTabId
        });

        this._user = user;
        this._isInitialized = true;

        // Cache user data if available
        if (user) {
            tokenCache.cacheUser(user._id, user);
        }
    }

    private setupCrossTabSync() {
        // ใช้ BroadcastChannel API สำหรับ modern browsers
        if ('BroadcastChannel' in window) {
            try {
                this._broadcastChannel = new BroadcastChannel('auth-sync');
                this._broadcastChannel.onmessage = (event) => {
                    this.handleAuthMessage(event.data);
                };
            } catch (error) {
                console.error('Failed to create BroadcastChannel:', error);
            }
        }

        // ใช้ localStorage events สำหรับ fallback
        this._storageListener = (event) => {
            if (event.key === 'auth-event' && event.newValue) {
                try {
                    const data = JSON.parse(event.newValue);
                    this.handleAuthMessage(data);
                } catch (error) {
                    console.error('Error parsing auth event:', error);
                }
            }
        };

        window.addEventListener('storage', this._storageListener);
    }

    private broadcastAuthEvent(type: string, payload?: any) {
        const message = {
            type,
            payload,
            timestamp: Date.now(),
            tabId: this._currentTabId // ใช้ tabId เดียวกัน
        };
        console.log('Broadcasting auth event:', message);

        // ส่งผ่าน BroadcastChannel (ใช้ try-catch แทนการตรวจสอบ readyState)
        if (this._broadcastChannel) {
            try {
                this._broadcastChannel.postMessage(message);
                console.log('Message sent via BroadcastChannel');
            } catch (error) {
                console.error('Failed to send message via BroadcastChannel:', error);
            }
        }

        // ส่งผ่าน localStorage (fallback) - เพิ่มการตรวจสอบ
        if (browser && type !== 'token-refresh') { // ไม่ส่ง token-refresh ผ่าน localStorage
            try {
                localStorage.setItem('auth-event', JSON.stringify(message));
                // ลบ event หลังจากส่ง
                setTimeout(() => {
                    localStorage.removeItem('auth-event');
                }, 100);
                console.log('Message sent via localStorage');
            } catch (error) {
                console.error('Failed to send message via localStorage:', error);
            }
        }
    }

    private handleAuthMessage(data: { type: string; payload?: any; tabId?: string }) {
        console.log('Received auth message:', data);

        // ตรวจสอบว่าเป็น event จากแท็บเดียวกันหรือไม่
        if (data.tabId === this._currentTabId) {
            console.log('Ignoring event from same tab');
            return;
        }

        switch (data.type) {
            case 'login':
                if (data.payload) {
                    console.log('Syncing login across tabs');
                    console.log('Previous user:', $state.snapshot(this._user));
                    console.log('New user:', data.payload.user);

                    // อัปเดต state
                    this._user = data.payload.user;
                    this._refreshToken = data.payload.refreshToken;
                    this.saveUserToStorage(data.payload.user, data.payload.refreshToken);
                    this._isInitialized = true;

                    console.log('Updated user:', $state.snapshot(this._user));
                    console.log('isAuthenticated:', this.isAuthenticated);

                    // ตรวจสอบว่า state ถูกอัปเดตจริงหรือไม่
                    setTimeout(() => {
                        console.log('User state after update:', $state.snapshot(this._user));
                        console.log('isAuthenticated after update:', this.isAuthenticated);

                        // Redirect ไป dashboard ถ้าอยู่ที่หน้า signin
                        if (window.location.pathname === '/signin') {
                            console.log('Redirecting to dashboard...');
                            goto('/dashboard');
                        }
                    }, 100);
                }
                break;
            case 'signout':
                console.log('Syncing signout across tabs');
                console.log('Previous user:', $state.snapshot(this._user));

                this._user = null;
                this._refreshToken = null;
                this._accessToken = null;
                this.clearStorage();
                this._isInitialized = true;

                console.log('Updated user:', $state.snapshot(this._user));
                console.log('isAuthenticated:', this.isAuthenticated);

                goto('/signin');
                break;
            case 'token-refresh':
                if (data.payload) {
                    console.log('Syncing token refresh across tabs');
                    this._user = data.payload.user;
                    this._refreshToken = data.payload.refreshToken;
                    this._accessToken = data.payload.token;
                    this.saveUserToStorage(data.payload.user, data.payload.refreshToken, data.payload.token);
                }
                break;
        }
    }

    private loadUserFromStorage() {
        try {
            // ✅ ลบการใช้ localStorage สำหรับ user และ refreshToken
            // ใช้เฉพาะ cookies ที่มี httpOnly สำหรับความปลอดภัย
            const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                acc[key] = value;
                return acc;
            }, {} as Record<string, string>);

            const accessToken = cookies['auth_token'];
            const refreshToken = cookies['refreshToken'];

            console.log('AuthStore: Loading from cookies only', {
                hasAccessToken: !!accessToken,
                hasRefreshToken: !!refreshToken,
                cookies: Object.keys(cookies)
            });

            // ✅ ไม่โหลด user จาก localStorage แล้ว
            // user data จะมาจาก SSR หรือ API call เท่านั้น
            if (accessToken) {
                this._accessToken = accessToken;
                this._refreshToken = refreshToken;

                console.log('AuthStore: Loaded tokens from cookies', {
                    hasAccessToken: !!this._accessToken,
                    hasRefreshToken: !!this._refreshToken
                });
            }
        } catch (error) {
            console.error('AuthStore: Error loading from cookies:', error);
            this.clearStorage();
        }
    }

    private saveUserToStorage(user: User, refreshToken: string, accessToken?: string) {
        if (browser) {
            // ✅ ลบการเก็บข้อมูลใน localStorage
            // ข้อมูล user และ tokens จะเก็บใน httpOnly cookies เท่านั้น
            // เก็บเฉพาะใน memory สำหรับการใช้งานใน client
            this._user = user;
            if (accessToken) {
                this._accessToken = accessToken;
            }
            this._refreshToken = refreshToken;

            console.log('AuthStore: User data saved to memory only (no localStorage)', {
                userId: user._id,
                hasAccessToken: !!accessToken,
                hasRefreshToken: !!refreshToken
            });
        }
    }

    private clearStorage() {
        if (browser) {
            console.log('AuthStore: Clearing all storage and cookies');

            // ✅ ไม่ต้องลบ localStorage เพราะไม่ได้ใช้แล้ว

            // ลบ cookies อย่างละเอียด
            const cookiesToClear = ['auth_token', 'refreshToken', 'session_id', 'session'];
            cookiesToClear.forEach(cookieName => {
                // ลบ cookie สำหรับ path หลายแบบ
                document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
                document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
            });

            // ลบ state variables
            this._refreshToken = null;
            this._accessToken = null;

            console.log('AuthStore: Storage and cookies cleared');
        }
    }

    private setupTokenRefresh() {
        // ตรวจสอบ token ทุก 5 นาที
        setInterval(() => {
            this.checkAndRefreshToken();
        }, 5 * 60 * 1000);
    }

    private async checkAndRefreshToken() {
        if (this._isRefreshing) return;

        try {
            this._isRefreshing = true;
            
            // ใช้ authService แทนการเรียก API โดยตรง
            const result = await authService.refreshToken(this._refreshToken || '');

            if (result.success && result.data) {
                this._user = result.data.user;
                this._refreshToken = result.data.refreshToken;
                this._accessToken = result.data.token;
                this.saveUserToStorage(result.data.user, result.data.refreshToken, result.data.token);

                // ส่ง event ไปยังแท็บอื่น
                this.broadcastAuthEvent('token-refresh', {
                    user: result.data.user,
                    refreshToken: result.data.refreshToken,
                    token: result.data.token
                });

                console.log('Token refreshed successfully');
            } else {
                // Token หมดอายุ ให้ signout
                console.log('Token refresh failed, logging out');
                this.signout();
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.signout();
        } finally {
            this._isRefreshing = false;
        }
    }

    // ฟังก์ชันสำหรับ refresh token แบบ manual
    async refreshToken() {
        try {
            // ใช้ authService แทนการเรียก API โดยตรง
            const result = await authService.refreshToken(this._refreshToken || '');

            if (result.success && result.data) {
                this._user = result.data.user;
                this._refreshToken = result.data.refreshToken;
                this.saveUserToStorage(result.data.user, result.data.refreshToken);

                console.log('Token refreshed manually');
                return true;
            } else {
                console.log('Manual token refresh failed');
                this.signout();
                return false;
            }
        } catch (error) {
            console.error('Manual token refresh error:', error);
            this.signout();
            return false;
        }
    }

    async signin(credentials: SigninData): Promise<boolean> {
        const endTimer = measurePerformance('store_signin');
        this._isLoading = true;
        this._error = null;

        logger.info(LogCategory.AUTH, 'store_signin_attempt', 'User attempting signin from store', {
            email: credentials.email?.substring(0, 3) + '***',
            rememberMe: credentials.rememberMe,
            tabId: this._currentTabId
        });

        try {
            // ใช้ authService แทนการเรียก API โดยตรง
            const result = await authService.signin(credentials);

            if (result.success && result.data) {
                this._user = result.data.user;
                this._accessToken = result.data.token;
                this.saveUserToStorage(result.data.user, result.data.refreshToken, result.data.token);

                // Cache user data
                if (result.data.user) {
                    tokenCache.cacheUser(result.data.user._id, result.data.user);
                    if (result.data.token) {
                        tokenCache.cacheToken(result.data.user._id, result.data.token);
                    }
                }

                // ส่ง event ไปยังแท็บอื่น
                this.broadcastAuthEvent('login', {
                    user: result.data.user,
                    refreshToken: result.data.refreshToken,
                    token: result.data.token
                });

                logger.info(LogCategory.AUTH, 'store_signin_success', 'User signed in successfully from store', {
                    userId: result.data.user._id,
                    sessionId: result.data.sessionId,
                    tabId: this._currentTabId
                });

                // แสดง SweetAlert success
                showLoginSuccess();

                // Redirect ไป dashboard
                setTimeout(() => {
                    goto('/dashboard');
                }, 1500);

                endTimer();
                return true;
            } else {
                throw new Error(result.error || 'Login failed');
            }
        } catch (error) {
            endTimer();

            const errorMessage = error instanceof Error ? error.message : 'Login failed';
            this._error = errorMessage;

            logger.error(LogCategory.AUTH, 'store_signin_error', 'Signin error in store', {
                error: errorMessage,
                tabId: this._currentTabId
            });

            // แสดง SweetAlert error
            showLoginError(errorMessage);

            return false;
        } finally {
            this._isLoading = false;
        }
    }

    async signup(credentials: SignupData): Promise<boolean> {
        this._isLoading = true;
        this._error = null;

        try {
            // ใช้ authService แทนการเรียก API โดยตรง
            const result = await authService.signup(credentials);

            if (result.success && result.data) {
                this._user = result.data.user;
                this.saveUserToStorage(result.data.user, result.data.refreshToken);

                // ส่ง event ไปยังแท็บอื่น
                this.broadcastAuthEvent('login', {
                    user: result.data.user,
                    refreshToken: result.data.refreshToken
                });

                // แสดง SweetAlert success
                showRegisterSuccess();

                // Redirect ไป dashboard
                setTimeout(() => {
                    goto('/dashboard');
                }, 1500);

                return true;
            } else {
                throw new Error(result.error || 'Registration failed');
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Registration failed';
            this._error = errorMessage;

            // แสดง SweetAlert error
            showRegisterError(errorMessage);

            return false;
        } finally {
            this._isLoading = false;
        }
    }

    async signout() {
        console.log('AuthStore: Starting signout process', {
            hasUser: !!this._user,
            hasAccessToken: !!this._accessToken,
            hasRefreshToken: !!this._refreshToken
        });

        this._isLoading = true;

        try {
            // ดึง access token จาก cookies หากไม่มีใน store
            let accessToken = this._accessToken;
            if (!accessToken && browser) {
                const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                    const [key, value] = cookie.trim().split('=');
                    acc[key] = value;
                    return acc;
                }, {} as Record<string, string>);
                accessToken = cookies['auth_token'];
                console.log('AuthStore: Retrieved access token from cookies', { hasToken: !!accessToken });
            }

            // ใช้ authService แทนการเรียก API โดยตรง
            // ส่ง access token สำหรับ signout
            if (accessToken) {
                const result = await authService.signout(accessToken);
                console.log('AuthStore: Signout API result', result);
            } else {
                console.log('AuthStore: No access token available, proceeding with local signout only');
            }
        } catch (error) {
            console.error('AuthStore: Signout API call failed:', error);
        } finally {
            console.log('AuthStore: Clearing user data and storage');
            this._user = null;
            this._accessToken = null;
            this._refreshToken = null;
            this.clearStorage();
            this._isLoading = false;

            // ส่ง event ไปยังแท็บอื่น
            this.broadcastAuthEvent('signout');

            // แสดง SweetAlert success
            showSignoutSuccess({
                timer: 1000,
            });

            console.log('AuthStore: Redirecting to signin page');
            // ใช้ window.location.href แทน goto เพื่อให้แน่ใจว่า page จะ reload
            setTimeout(() => {
                window.location.href = '/signin';
            }, 1500);
        }
    }

    async refreshUser() {
        try {
            // ใช้ authService แทนการเรียก API โดยตรง
            const result = await authService.getCurrentUser(this._refreshToken || '');

            if (result.success && result.data) {
                this._user = result.data;
                if (this._refreshToken) {
                    this.saveUserToStorage(result.data, this._refreshToken);
                }
            } else {
                this.signout();
            }
        } catch (error) {
            console.error('Failed to refresh user:', error);
            this.signout();
        }
    }

    clearError() {
        this._error = null;
    }

    // ฟังก์ชันสำหรับอัปเดตข้อมูล user
    updateUser(user: User) {
        console.log('AuthStore: Updating user data', {
            oldUser: this._user,
            newUser: user,
            oldMoneyPoint: this._user?.moneyPoint,
            newMoneyPoint: user.moneyPoint
        });
        
        this._user = user;
        if (this._refreshToken) {
            this.saveUserToStorage(user, this._refreshToken);
        }

        // ส่ง event ไปยังแท็บอื่น
        this.broadcastAuthEvent('user-updated', { user });
        
        console.log('AuthStore: User updated successfully', {
            currentUser: this._user,
            moneyPoint: this._user?.moneyPoint
        });
    }

    // Cleanup method สำหรับ destroy store
    destroy() {
        // ไม่ปิด BroadcastChannel เพื่อให้ยังสามารถส่ง message ได้
        // BroadcastChannel จะถูกปิดอัตโนมัติเมื่อ tab ถูกปิด
        if (this._storageListener) {
            window.removeEventListener('storage', this._storageListener);
        }
    }
}

export const authStore = new AuthStore();