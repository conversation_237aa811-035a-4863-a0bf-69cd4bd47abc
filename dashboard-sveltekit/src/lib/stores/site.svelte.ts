import { writable } from 'svelte/store';

export interface Site {
  _id: string;
  name: string;
  typeDomain: 'subdomain' | 'custom';
  fullDomain: string;
  isActive: boolean;
  expiredAt: string;
  createdAt: string;
  updatedAt: string;
  userId?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  themeSettings?: any;
  seoSettings?: any;
  navigationSettings?: any;
  pageSettings?: any;
  loadingSettings?: any;
  // เพิ่มข้อมูลสถานะใหม่
  isExpired?: boolean;
  daysUntilExpiry?: number;
  hasAccess?: boolean;
}

interface SiteStore {
  site: Site | null;
  loading: boolean;
  error: string | null;
}

function createSiteStore() {
  const { subscribe, set, update } = writable<SiteStore>({
    site: null,
    loading: false,
    error: null
  });

  return {
    subscribe,
    setSite: (site: Site | null) => {
      update(state => ({ ...state, site, error: null }));
    },
    setLoading: (loading: boolean) => {
      update(state => ({ ...state, loading }));
    },
    setError: (error: string | null) => {
      update(state => ({ ...state, error, loading: false }));
    },
    reset: () => {
      set({ site: null, loading: false, error: null });
    }
  };
}

export const siteStore = createSiteStore(); 