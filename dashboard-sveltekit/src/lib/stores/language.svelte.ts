import { browser } from '$app/environment';
import { locale } from 'svelte-i18n';
import { logger, LogCategory } from '../utils/logger';

export type Language = 'th' | 'en' | 'lo';

class LanguageStore {
    private _language = $state<Language>('th');
    private _broadcastChannel: BroadcastChannel | null = null;
    private _storageListener: ((event: StorageEvent) => void) | null = null;
    private _currentTabId: string = '';

    constructor() {
        if (browser) {
            this._currentTabId = Math.random().toString(36).substring(2, 11);
            this.setupCrossTabSync();
            this.loadLanguage();

            logger.info(LogCategory.SYSTEM, 'language_store_initialized', 'Language store initialized', {
                tabId: this._currentTabId,
                initialLanguage: this._language
            });
        }
    }

    get language() {
        return this._language;
    }

    private setupCrossTabSync() {
        // ใช้ BroadcastChannel API สำหรับ modern browsers
        if ('BroadcastChannel' in window) {
            try {
                this._broadcastChannel = new BroadcastChannel('language-sync');
                this._broadcastChannel.onmessage = (event) => {
                    this.handleLanguageMessage(event.data);
                };
            } catch (error) {
                console.error('Failed to create BroadcastChannel for language:', error);
            }
        }

        // ใช้ localStorage events สำหรับ fallback
        this._storageListener = (event) => {
            if (event.key === 'language-event' && event.newValue) {
                try {
                    const data = JSON.parse(event.newValue);
                    this.handleLanguageMessage(data);
                } catch (error) {
                    console.error('Error parsing language event:', error);
                }
            }
        };

        window.addEventListener('storage', this._storageListener);
    }

    private handleLanguageMessage(data: { type: string; payload?: any; tabId?: string }) {
        // ตรวจสอบว่าเป็น event จากแท็บเดียวกันหรือไม่
        if (data.tabId === this._currentTabId) {
            logger.debug(LogCategory.SYSTEM, 'language_event_ignored', 'Language event from same tab ignored', {
                tabId: this._currentTabId
            });
            return;
        }

        if (data.type === 'language-change' && data.payload) {
            logger.info(LogCategory.SYSTEM, 'language_sync_received', 'Language change synced from another tab', {
                fromTabId: data.tabId,
                toTabId: this._currentTabId,
                newLanguage: data.payload.language,
                previousLanguage: this._language
            });

            this.setLanguage(data.payload.language, false); // ไม่ส่ง event กลับ
        }
    }

    private broadcastLanguageEvent(language: Language) {
        const message = {
            type: 'language-change',
            payload: { language },
            timestamp: Date.now(),
            tabId: this._currentTabId
        };

        // ส่งผ่าน BroadcastChannel
        if (this._broadcastChannel) {
            try {
                this._broadcastChannel.postMessage(message);
            } catch (error) {
                console.error('Failed to send language message via BroadcastChannel:', error);
            }
        }

        // ส่งผ่าน localStorage (fallback)
        if (browser) {
            try {
                localStorage.setItem('language-event', JSON.stringify(message));
                setTimeout(() => {
                    localStorage.removeItem('language-event');
                }, 100);
            } catch (error) {
                console.error('Failed to send language message via localStorage:', error);
            }
        }
    }

    private loadLanguage() {
        if (browser) {
            const savedLanguage = localStorage.getItem('language') as Language;
            if (savedLanguage && this.isValidLanguage(savedLanguage)) {
                this.setLanguage(savedLanguage, false);
            } else {
                // ตรวจสอบภาษาจาก browser
                const browserLanguage = this.detectBrowserLanguage();
                this.setLanguage(browserLanguage, false);
            }
        }
    }

    private isValidLanguage(lang: string): lang is Language {
        return ['th', 'en', 'lo'].includes(lang);
    }

    private detectBrowserLanguage(): Language {
        if (!browser) return 'th';

        try {
            const browserLang = navigator.language.split('-')[0];

            // ตรวจสอบว่าภาษาจาก browser รองรับหรือไม่
            if (this.isValidLanguage(browserLang)) {
                return browserLang;
            }

            // ตรวจสอบภาษาสำรองจาก navigator.languages
            for (const lang of navigator.languages) {
                const langCode = lang.split('-')[0];
                if (this.isValidLanguage(langCode)) {
                    return langCode;
                }
            }
        } catch (error) {
            console.warn('Failed to detect browser language:', error);
        }

        // ใช้ภาษาเริ่มต้น
        return 'th';
    }

    setLanguage(language: Language, broadcast: boolean = true) {
        const previousLanguage = this._language;
        this._language = language;

        if (browser) {
            try {
                locale.set(language);
                localStorage.setItem('language', language);

                logger.info(LogCategory.SYSTEM, 'language_changed', 'Language changed successfully', {
                    previousLanguage,
                    newLanguage: language,
                    broadcast,
                    tabId: this._currentTabId
                });

                if (broadcast) {
                    this.broadcastLanguageEvent(language);

                    logger.info(LogCategory.SYSTEM, 'language_broadcast_sent', 'Language change broadcasted to other tabs', {
                        language,
                        tabId: this._currentTabId
                    });
                }
            } catch (error) {
                logger.error(LogCategory.SYSTEM, 'language_change_error', 'Failed to change language', {
                    language,
                    error: error instanceof Error ? error.message : 'Unknown error',
                    tabId: this._currentTabId
                });
            }
        }
    }

    destroy() {
        if (this._storageListener) {
            window.removeEventListener('storage', this._storageListener);
        }
    }
}

export const languageStore = new LanguageStore(); 