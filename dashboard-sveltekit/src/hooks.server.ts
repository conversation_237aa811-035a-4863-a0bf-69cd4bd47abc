import type { Handle } from '@sveltejs/kit';
import { apiUrl } from '$lib/config';
import { getClientIP, getUserAgent, generateSessionId } from '$lib/utils/security';
import { logger, measurePerformance, logSecurityEvent, LogCategory } from '$lib/utils/logger';
import { tokenCache } from '$lib/utils/cache';
import { $fetch } from 'ofetch';

export const handle: Handle = async ({ event, resolve }) => {
  const endTimer = measurePerformance('hooks_handle');
  const clientIP = getClientIP(event.request);
  const userAgent = getUserAgent(event.request);
  const sessionId = event.cookies.get('session_id') || generateSessionId();

  logger.setRequestId(sessionId);

  // ดึง token จาก cookie
  const authToken = event.cookies.get('auth_token');

  logger.debug(LogCategory.SYSTEM, 'hooks_auth_check', 'Checking authentication in hooks', {
    hasAuthToken: !!authToken,
    path: event.url.pathname,
    method: event.request.method,
    sessionId
  }, { ip: clientIP, userAgent, sessionId });

  if (authToken) {
    try {
      // Check cache first for performance
      const cachedUser = tokenCache.getUser(authToken.substring(0, 10)); // Use token prefix as key
      if (cachedUser) {
        event.locals.user = cachedUser;
        event.locals.token = authToken;

        logger.debug(LogCategory.SYSTEM, 'hooks_cache_hit', 'User data retrieved from cache', {
          userId: cachedUser._id,
          sessionId
        }, { ip: clientIP, userAgent, sessionId });

        endTimer();
        return resolve(event);
      }

      logger.info(LogCategory.SYSTEM, 'hooks_token_validation', 'Validating token with backend', {
        sessionId
      }, { ip: clientIP, userAgent, sessionId });

      // ตรวจสอบ token กับ backend
      const validationTimer = measurePerformance('backend_token_validation');
      const response = await $fetch(`${apiUrl}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'User-Agent': userAgent,
          'X-Forwarded-For': clientIP,
          'X-Real-IP': clientIP,
          'X-Session-ID': sessionId,
        },
        responseType: 'json'
      });
      validationTimer();

      logger.info(LogCategory.SYSTEM, 'hooks_validation_response', 'Backend validation response received', {
        success: true,
        sessionId
      }, { ip: clientIP, userAgent, sessionId });

      if (response.success && response.data) {
        // เพิ่ม user และ token ลงใน locals
        event.locals.user = response.data.user;
        event.locals.token = authToken;

        // Cache user data
        tokenCache.cacheUser(authToken.substring(0, 10), response.data.user);

        logger.info(LogCategory.SYSTEM, 'hooks_auth_success', 'User authenticated successfully', {
          userId: response.data.user._id,
          email: `${response.data.user.email?.substring(0, 3)}***`,
          sessionId
        }, { ip: clientIP, userAgent, sessionId });
      }
    } catch (error) {
      // Token หมดอายุหรือไม่ถูกต้อง ลอง refresh token
      logger.info(LogCategory.SYSTEM, 'hooks_token_expired', 'Token expired or invalid, attempting refresh', {
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, { ip: clientIP, userAgent, sessionId });

      const refreshToken = event.cookies.get('refreshToken');

      if (refreshToken) {
        try {
          const refreshTimer = measurePerformance('backend_token_refresh');
          const refreshData = await $fetch(`${apiUrl}/user/refresh-token`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': userAgent,
              'X-Forwarded-For': clientIP,
              'X-Real-IP': clientIP,
              'X-Session-ID': sessionId,
            },
            body: JSON.stringify({ refreshToken }),
            responseType: 'json'
          });
          refreshTimer();

          if (refreshData.success && refreshData.data) {
            // อัปเดต cookies
            const cookieOptions = {
              path: '/',
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax' as const,
            };

            event.cookies.set('auth_token', refreshData.data.token, {
              ...cookieOptions,
              maxAge: 60 * 60 * 24 * 7, // 7 วัน
            });

            event.cookies.set('refreshToken', refreshData.data.refreshToken, {
              ...cookieOptions,
              maxAge: 60 * 60 * 24 * 30, // 30 วัน
            });

            event.cookies.set('session_id', sessionId, {
              ...cookieOptions,
              maxAge: 60 * 60 * 24, // 1 วัน
            });

            // อัปเดต locals และ cache
            event.locals.user = refreshData.data.user;
            event.locals.token = refreshData.data.token;

            tokenCache.cacheUser(refreshData.data.token.substring(0, 10), refreshData.data.user);
            tokenCache.cacheToken(refreshData.data.user._id, refreshData.data.token);

            logger.info(LogCategory.SYSTEM, 'hooks_token_refreshed', 'Token refreshed successfully', {
              userId: refreshData.data.user._id,
              sessionId
            }, { ip: clientIP, userAgent, sessionId });
          }
        } catch (refreshError) {
          logger.error(LogCategory.SYSTEM, 'hooks_refresh_error', 'Error during token refresh', {
            error: refreshError instanceof Error ? refreshError.message : 'Unknown error',
            sessionId
          }, { ip: clientIP, userAgent, sessionId });

          logSecurityEvent(
            'token_refresh_exception',
            'Exception during token refresh in hooks',
            'high',
            { error: refreshError instanceof Error ? refreshError.message : 'Unknown error' },
            { ip: clientIP, userAgent, sessionId }
          );

          event.cookies.delete('auth_token', { path: '/' });
          event.cookies.delete('refreshToken', { path: '/' });
          event.cookies.delete('session_id', { path: '/' });
        }
      } else {
        logger.warn(LogCategory.SYSTEM, 'hooks_no_refresh_token', 'No refresh token available, clearing auth cookie', {
          sessionId
        }, { ip: clientIP, userAgent, sessionId });

        logSecurityEvent(
          'no_refresh_token',
          'No refresh token available in hooks',
          'medium',
          {},
          { ip: clientIP, userAgent, sessionId }
        );

        event.cookies.delete('auth_token', { path: '/' });
        event.cookies.delete('refreshToken', { path: '/' });
        event.cookies.delete('session_id', { path: '/' });
      }
    }
  } else {
    logger.debug(LogCategory.SYSTEM, 'hooks_no_token', 'No auth token found', {
      path: event.url.pathname,
      sessionId
    }, { ip: clientIP, userAgent, sessionId });
  }

  endTimer();
  return resolve(event);
};
