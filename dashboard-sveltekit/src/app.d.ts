// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			user?: {
				_id: string;
				email: string;
				firstName?: string;
				lastName?: string;
				avatar?: string;
				cover?: string;
				isEmailVerified: boolean;
				moneyPoint: number;
				goldPoint: number;
				role?: 'admin' | 'user' | 'moderator';
				status?: 'active' | 'inactive';
				createdAt: string;
				updatedAt: string;
			};
			token?: string;
		}
		interface PageData {
			site?: import('$lib/services/site').Site | null;
			user?: Locals['user'];
			error?: string | null;
		}
		// interface PageState {}
		// interface Platform {}
	}
}

export { };
