import type { PageServerLoad, Actions } from './$types';
import { redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async () => {
  // Redirect ไปหน้า dashboard หากเข้ามาโดยตรง
  throw redirect(302, '/dashboard');
};

export const actions: Actions = {
  default: async ({ cookies }) => {
    console.log('Server Action: Signout called');
    
    // ลบ cookies ทั้งหมด
    const cookiesToClear = ['auth_token', 'refreshToken', 'session_id', 'session'];
    cookiesToClear.forEach(cookieName => {
      cookies.delete(cookieName, { path: '/' });
    });
    
    console.log('Server Action: Cookies cleared, redirecting to signin');
    throw redirect(302, '/signin');
  }
};
