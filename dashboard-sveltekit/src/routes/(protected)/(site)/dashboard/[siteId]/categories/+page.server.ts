import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { categoryService } from '$lib/services/category';
import type { Category } from '$lib/types';
import { requireAuth } from '$lib/utils/server-auth';

export const load: PageServerLoad = async ({ locals, params }) => {
    // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
    const { siteId } = params;
    
    try {
        const response = await categoryService.getCategories(siteId, locals.token!);

        if (!response.success) {
            throw error(500, response.error || 'Failed to load categories');
        }

        return {
            categories: response.data || []
        };
    } catch (err) {
        console.error('Error loading categories:', err);
        throw error(500, 'Failed to load categories');
    }
};

export const actions: Actions = {
    createCategory: async ({ request, locals, params }) => {
        // ไม่ต้องตรวจสอบ auth อีก
        const { siteId } = params;
        const formData = await request.formData();
        
        const categoryData = {
            target: (formData.get('target') as string || 'product') as 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news',
            name: formData.get('name') as string,
            cover: formData.get('cover') as string || undefined,
            parentId: formData.get('parentId') as string || undefined,
            siteId
        };

        const response = await categoryService.createCategory(categoryData, siteId, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    },

    updateCategory: async ({ request, locals, params }) => {
        const { siteId } = params;
        const formData = await request.formData();
        const categoryId = formData.get('categoryId') as string;
        
        const categoryData = {
            target: (formData.get('target') as string || 'product') as 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news',
            name: formData.get('name') as string,
            cover: formData.get('cover') as string || undefined,
            parentId: formData.get('parentId') as string || undefined
        };

        const response = await categoryService.updateCategory(categoryId, categoryData, siteId, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    },

    deleteCategory: async ({ request, locals, params }) => {
        const { siteId } = params;
        const formData = await request.formData();
        const categoryId = formData.get('categoryId') as string;

        const response = await categoryService.deleteCategory(categoryId, siteId, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    }
}; 