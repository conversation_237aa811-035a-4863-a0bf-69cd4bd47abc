import type { LayoutServerLoad } from './$types';
import { siteService } from '$lib/services/site';
import { requireAuth } from '$lib/utils/server-auth';

// ไม่ต้อง disable SSR เพราะต้องการให้ load function ทำงาน

export const load: LayoutServerLoad = async ({ locals, params }) => {
  const { siteId } = params;
  
  // ตรวจสอบ authentication (ได้จาก parent layout แล้ว)
  requireAuth(locals);
  
  try {
    console.log('Fetching site data for siteId:', siteId);

    // ใช้ site service แทนการเรียก API โดยตรง
    const result = await siteService.getSite(siteId, locals.token!);
    console.log('Site service result:', result);

    if (!result.success) {
      console.error('Site service returned error:', result.error);
      return {
        siteId,
        error: result.error || 'ไม่สามารถดึงข้อมูลเว็บไซต์ได้'
      };
    }

    return {
      site: result.data,
      siteId,
      error: null
    };

  } catch (error) {
    console.error('Load function error:', error);
    return {
      siteId,
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์'
    };
  }
}; 
