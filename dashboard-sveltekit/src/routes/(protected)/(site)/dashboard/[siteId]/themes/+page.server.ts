import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { themeService } from '$lib/services/theme';
import type { ThemeSettings } from '$lib/types';

export const load: PageServerLoad = async ({ locals, params }) => {
    // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
    const { siteId } = params;
    
    try {
        // ดึงข้อมูล theme จาก API
        const themeResponse = await themeService.getTheme(siteId, locals.token!);
        
        if (!themeResponse.success) {
            // ถ้าไม่มี theme ให้ใช้ค่า default
            const defaultTheme = await themeService.getDefaultTheme();
            defaultTheme.siteId = siteId;
            return {
                theme: defaultTheme,
                isNew: true
            };
        }

        return {
            theme: themeResponse.data,
            isNew: false
        };
    } catch (err) {
        console.error('Error loading theme:', err);
        throw error(500, 'Failed to load theme settings');
    }
};

export const actions: Actions = {
    saveTheme: async ({ request, locals, params }) => {
        const { siteId } = params;
        const formData = await request.formData();
        
        const themeData: ThemeSettings = {
            siteId,
            layout: formData.get('layout') as string,
            headerStyle: formData.get('headerStyle') as string,
            footerStyle: formData.get('footerStyle') as string,
            showSearch: formData.get('showSearch') === 'true',
            showLanguageSelector: formData.get('showLanguageSelector') === 'true',
            showThemeToggle: formData.get('showThemeToggle') === 'true',
            mobileMenuStyle: formData.get('mobileMenuStyle') as string,
            desktopMenuStyle: formData.get('desktopMenuStyle') as string,
            primaryColor: formData.get('primaryColor') as string,
            secondaryColor: formData.get('secondaryColor') as string,
            backgroundColor: formData.get('backgroundColor') as string,
            textColor: formData.get('textColor') as string,
            accentColor: formData.get('accentColor') as string,
            fontFamily: formData.get('fontFamily') as string,
            fontSize: formData.get('fontSize') as string,
            lineHeight: formData.get('lineHeight') as string,
            fontWeight: formData.get('fontWeight') as string,
            containerPadding: formData.get('containerPadding') as string,
            sectionSpacing: formData.get('sectionSpacing') as string,
            elementSpacing: formData.get('elementSpacing') as string
        };

        const response = await themeService.saveTheme(themeData, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    },

    resetTheme: async ({ locals, params }) => {
        const { siteId } = params;
        const defaultTheme = await themeService.getDefaultTheme();
        defaultTheme.siteId = siteId;

        const response = await themeService.saveTheme(defaultTheme, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    },

    deleteTheme: async ({ locals, params }) => {
        const { siteId } = params;
        const response = await themeService.deleteTheme(siteId, locals.token!);
        
        return response.success ? response : fail(400, { error: response.error });
    }
}; 