<script lang="ts">
  import { subscriptionService } from "$lib/services/subscription";
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import {
    <PERSON><PERSON>,
    Card,
    Badge,
    Alert,
    Checkbox,
    Pagination,
  } from "$lib/components/ui";
  import Icon from "@iconify/svelte";
  import type { NotificationPageData, PageProps } from "$lib/types";

  const { data }: PageProps<NotificationPageData> = $props();

  // Reactive data
  const { site, notifications = [], pagination, error } = $derived(data);
  const siteId = $derived(page.params.siteId);

  // State management
  let loading = $state(false);
  let unreadOnly = $state(false);
  let selectedNotifications = $state<string[]>([]);
  let markAllLoading = $state(false);
  let successMessage = $state("");
  let errorMessage = $state("");

  // Computed values
  const unreadNotifications = $derived(notifications.filter((n) => !n.isRead));
  const readNotifications = $derived(notifications.filter((n) => n.isRead));

  // Functions
  async function markNotificationAsRead(notificationId: string) {
    loading = true;
    try {
      const result =
        await subscriptionService.markNotificationAsRead(notificationId);

      if (result.success) {
        successMessage = "อ่านการแจ้งเตือนสำเร็จ";
        // Refresh page data
        await goto(`/dashboard/${siteId}/subscriptions/notifications`, {
          replaceState: true,
        });
      } else {
        errorMessage = result.error || "เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน";
      }
    } catch (error) {
      errorMessage = "เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน";
    } finally {
      loading = false;
    }
  }

  async function markAllAsRead() {
    if (unreadNotifications.length === 0) return;

    markAllLoading = true;
    try {
      // Mark all unread notifications as read
      const promises = unreadNotifications.map((notification) =>
        subscriptionService.markNotificationAsRead(notification._id),
      );

      await Promise.all(promises);
      successMessage = "อ่านการแจ้งเตือนทั้งหมดสำเร็จ";

      // Refresh page data
      await goto(`/dashboard/${siteId}/subscriptions/notifications`, {
        replaceState: true,
      });
    } catch (error) {
      errorMessage = "เกิดข้อผิดพลาดในการอ่านการแจ้งเตือนทั้งหมด";
    } finally {
      markAllLoading = false;
    }
  }

  function getNotificationIcon(type: string) {
    switch (type) {
      case "expiry_warning":
        return "mdi:alert-triangle";
      case "renewal_success":
        return "mdi:check-circle";
      case "renewal_failure":
        return "mdi:close-circle";
      case "auto_renewal_disabled":
        return "mdi:cog";
      case "payment_failed":
        return "mdi:credit-card";
      default:
        return "mdi:bell";
    }
  }

  function getNotificationColor(type: string) {
    switch (type) {
      case "expiry_warning":
        return "warning";
      case "renewal_success":
        return "success";
      case "renewal_failure":
        return "error";
      case "auto_renewal_disabled":
        return "info";
      case "payment_failed":
        return "error";
      default:
        return "neutral";
    }
  }

  function getNotificationTitle(type: string) {
    switch (type) {
      case "expiry_warning":
        return "แจ้งเตือนก่อนหมดอายุ";
      case "renewal_success":
        return "ต่ออายุสำเร็จ";
      case "renewal_failure":
        return "ต่ออายุล้มเหลว";
      case "auto_renewal_disabled":
        return "ปิดการต่ออายุอัตโนมัติ";
      case "payment_failed":
        return "การชำระเงินล้มเหลว";
      default:
        return "การแจ้งเตือน";
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("th-TH", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  function filterNotifications() {
    const params = new URLSearchParams();
    if (unreadOnly) {
      params.append("unreadOnly", "true");
    }
    goto(`?${params.toString()}`);
  }

  // Watch for filter changes
  $effect(() => {
    if (unreadOnly !== undefined) {
      filterNotifications();
    }
  });
</script>

<svelte:head>
  <title>การแจ้งเตือน Subscription - {site?.name || "Dashboard"}</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        การแจ้งเตือน Subscription
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        จัดการการแจ้งเตือนเกี่ยวกับแพ็คเกจและวันใช้งาน
      </p>
    </div>
    <div class="flex items-center space-x-2">
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          bind:checked={unreadOnly}
          class="checkbox checkbox-sm"
        />
        <span class="text-sm">แสดงเฉพาะที่ยังไม่อ่าน</span>
      </label>
      {#if unreadNotifications.length > 0}
        <Button
          onclick={markAllAsRead}
          color="primary"
          size="sm"
          disabled={markAllLoading}
        >
          {#if markAllLoading}
            <div
              class="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"
            ></div>
          {/if}
          อ่านทั้งหมด
        </Button>
      {/if}
    </div>
  </div>

  <!-- Error Alert -->
  {#if error}
    <Alert color="error">
      <Icon icon="mdi:alert-triangle" class="w-5 h-5" />
      <span>{error}</span>
    </Alert>
  {/if}

  <!-- Success Message -->
  {#if successMessage}
    <Alert
      color="success"
      dismissible={true}
      ondismiss={() => (successMessage = "")}
    >
      <Icon icon="mdi:check-circle" class="w-5 h-5" />
      <span>{successMessage}</span>
    </Alert>
  {/if}

  <!-- Error Message -->
  {#if errorMessage}
    <Alert
      color="error"
      dismissible={true}
      ondismiss={() => (errorMessage = "")}
    >
      <Icon icon="mdi:alert-triangle" class="w-5 h-5" />
      <span>{errorMessage}</span>
    </Alert>
  {/if}

  <!-- Stats -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Icon
              icon="mdi:bell"
              class="w-6 h-6 text-blue-600 dark:text-blue-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              การแจ้งเตือนทั้งหมด
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {notifications.length}
            </p>
          </div>
        </div>
      </div>
    </Card>

    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <Icon
              icon="mdi:eye"
              class="w-6 h-6 text-yellow-600 dark:text-yellow-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              ยังไม่อ่าน
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {unreadNotifications.length}
            </p>
          </div>
        </div>
      </div>
    </Card>

    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <Icon
              icon="mdi:check-circle"
              class="w-6 h-6 text-green-600 dark:text-green-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              อ่านแล้ว
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {readNotifications.length}
            </p>
          </div>
        </div>
      </div>
    </Card>
  </div>

  <!-- Notifications List -->
  <div class="space-y-4">
    {#if notifications.length === 0}
      <Card>
        <div class="p-8 text-center">
          <Icon icon="mdi:bell" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            ไม่มีการแจ้งเตือน
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            {unreadOnly
              ? "ไม่มีการแจ้งเตือนที่ยังไม่อ่าน"
              : "ไม่มีการแจ้งเตือนใดๆ"}
          </p>
        </div>
      </Card>
    {:else}
      {#each notifications as notification (notification._id)}
        <Card
          class="transition-all duration-200 hover:shadow-lg {!notification.isRead
            ? 'border-l-4 border-l-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : ''}"
        >
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex items-start space-x-4 flex-1">
                <div class="flex-shrink-0">
                  <div class="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <Icon
                      icon={getNotificationIcon(notification.type)}
                      class="w-6 h-6 text-gray-600 dark:text-gray-400"
                    />
                  </div>
                </div>

                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-2 mb-2">
                    <Badge color={getNotificationColor(notification.type)}>
                      {getNotificationTitle(notification.type)}
                    </Badge>
                    {#if !notification.isRead}
                      <Badge color="info">ใหม่</Badge>
                    {/if}
                  </div>

                  <h3
                    class="text-lg font-medium text-gray-900 dark:text-white mb-1"
                  >
                    {notification.title}
                  </h3>

                  <p class="text-gray-600 dark:text-gray-400 mb-3">
                    {notification.message}
                  </p>

                  <div
                    class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"
                  >
                    <div class="flex items-center">
                      <Icon icon="mdi:clock" class="w-4 h-4 mr-1" />
                      {formatDate(notification.createdAt)}
                    </div>

                    {#if notification.subscriptionId}
                      <div class="flex items-center">
                        <Icon icon="mdi:link" class="w-4 h-4 mr-1" />
                        Subscription ID: {notification.subscriptionId}
                      </div>
                    {/if}
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-2 ml-4">
                {#if !notification.isRead}
                  <Button
                    size="sm"
                    color="primary"
                    onclick={() => markNotificationAsRead(notification._id)}
                    disabled={loading}
                  >
                    <Icon icon="mdi:eye" class="w-4 h-4 mr-1" />
                    อ่าน
                  </Button>
                {/if}
              </div>
            </div>

            {#if notification.data}
              <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4
                  class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  ข้อมูลเพิ่มเติม:
                </h4>
                <pre
                  class="text-xs text-gray-600 dark:text-gray-400 overflow-x-auto">
                  {JSON.stringify(notification.data, null, 2)}
                </pre>
              </div>
            {/if}
          </div>
        </Card>
      {/each}
    {/if}
  </div>

  <!-- Pagination -->
  {#if pagination && pagination.totalPages > 1}
    <div class="flex justify-center">
      <Pagination
        page={pagination.page}
        totalPages={pagination.totalPages}
        onPageChange={(page) => goto(`?page=${page}`)}
      />
    </div>
  {/if}
</div>
