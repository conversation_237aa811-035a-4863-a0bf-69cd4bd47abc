<script lang="ts">
  import { subscriptionService } from "$lib/services/subscription";
  import type {
    PackageInfo,
    Subscription,
    CreateSubscriptionData,
    DiscountValidation,
  } from "$lib/types";
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import {
    <PERSON>ton,
    Card,
    Badge,
    Alert,
    Modal,
    Input,
    Pagination,
    Select,
    Checkbox,
  } from "$lib/components/ui";
  import Icon from "@iconify/svelte";

  import type { PageProps, SubscriptionPageData } from "$lib/types";

  const { data }: PageProps<SubscriptionPageData> = $props();

  // Reactive data
  const {
    site,
    packages = [],
    subscriptions = [],
    pagination,
    notifications = [],
    error,
  } = $derived(data);
  const siteId = $derived(page.params.siteId);

  // State management
  let loading = $state(false);
  let showCreateModal = $state(false);
  let showRenewModal = $state(false);
  let showCancelModal = $state(false);
  let selectedSubscription = $state<Subscription | null>(null);
  let selectedPackage = $state<PackageInfo | null>(null);
  let discountCode = $state("");
  let discountValidation = $state<{
    valid: boolean;
    message: string;
    discount?: number;
  } | null>(null);
  let discountLoading = $state(false);
  let autoRenew = $state(false);
  let paymentMethod = $state("moneyPoint");
  let formError = $state("");
  let successMessage = $state("");
  let activeTab = $state("active");

  // Form data
  let createForm = $state<CreateSubscriptionData>({
    packageType: "monthly",
    autoRenew: false,
    paymentMethod: "moneyPoint",
  });

  // Computed values
  const activeSubscriptions = $derived(
    subscriptions.filter((sub) => sub.status === "active"),
  );
  const expiredSubscriptions = $derived(
    subscriptions.filter((sub) => sub.status === "expired"),
  );
  const cancelledSubscriptions = $derived(
    subscriptions.filter((sub) => sub.status === "cancelled"),
  );
  const totalSpent = $derived(
    subscriptions.reduce((sum, sub) => sum + sub.stats.totalSpent, 0),
  );
  const unreadNotifications = $derived(notifications.filter((n) => !n.isRead));

  // Functions
  async function validateDiscount() {
    if (!discountCode.trim()) {
      discountValidation = null;
      return;
    }

    discountLoading = true;
    try {
      const result = await subscriptionService.validateDiscount(discountCode);
      discountValidation = result.success
        ? result.data || null
        : {
            valid: false,
            message: result.error || "ไม่สามารถตรวจสอบรหัสส่วนลดได้",
          };
    } catch (error) {
      discountValidation = {
        valid: false,
        message: "เกิดข้อผิดพลาดในการตรวจสอบรหัสส่วนลด",
      };
    } finally {
      discountLoading = false;
    }
  }

  async function createSubscription() {
    if (!selectedPackage) return;

    loading = true;
    formError = "";

    try {
      const formData: CreateSubscriptionData = {
        packageType: selectedPackage.type as any,
        autoRenew,
        paymentMethod,
        discountCode: discountCode.trim() || undefined,
      };

      const result = await subscriptionService.createSiteSubscription(
        siteId!,
        formData,
      );

      if (result.success) {
        successMessage = "สร้าง subscription สำเร็จ!";
        showCreateModal = false;
        resetForm();
        // Refresh page data
        await goto(`/dashboard/${siteId}/subscriptions`, {
          replaceState: true,
        });
      } else {
        formError = result.error || "เกิดข้อผิดพลาดในการสร้าง subscription";
      }
    } catch (error) {
      formError = "เกิดข้อผิดพลาดในการสร้าง subscription";
    } finally {
      loading = false;
    }
  }

  async function toggleAutoRenew(subscription: Subscription) {
    loading = true;
    try {
      const result = await subscriptionService.toggleAutoRenew(
        siteId!,
        subscription._id,
        !subscription.autoRenew,
      );

      if (result.success) {
        successMessage = `${!subscription.autoRenew ? "เปิด" : "ปิด"}การต่ออายุอัตโนมัติสำเร็จ!`;
        // Refresh page data
        await goto(`/dashboard/${siteId}/subscriptions`, {
          replaceState: true,
        });
      } else {
        formError = result.error || "เกิดข้อผิดพลาดในการเปลี่ยนการตั้งค่า";
      }
    } catch (error) {
      formError = "เกิดข้อผิดพลาดในการเปลี่ยนการตั้งค่า";
    } finally {
      loading = false;
    }
  }

  async function cancelSubscription(subscription: Subscription) {
    loading = true;
    try {
      const result = await subscriptionService.cancelSubscription(
        siteId!,
        subscription._id,
      );

      if (result.success) {
        successMessage = "ยกเลิก subscription สำเร็จ!";
        showCancelModal = false;
        selectedSubscription = null;
        // Refresh page data
        await goto(`/dashboard/${siteId}/subscriptions`, {
          replaceState: true,
        });
      } else {
        formError = result.error || "เกิดข้อผิดพลาดในการยกเลิก subscription";
      }
    } catch (error) {
      formError = "เกิดข้อผิดพลาดในการยกเลิก subscription";
    } finally {
      loading = false;
    }
  }

  async function renewSubscription(subscription: Subscription) {
    loading = true;
    try {
      const result = await subscriptionService.renewSubscription(
        siteId!,
        subscription._id,
      );

      if (result.success) {
        successMessage = "ต่ออายุ subscription สำเร็จ!";
        showRenewModal = false;
        selectedSubscription = null;
        // Refresh page data
        await goto(`/dashboard/${siteId}/subscriptions`, {
          replaceState: true,
        });
      } else {
        formError = result.error || "เกิดข้อผิดพลาดในการต่ออายุ subscription";
      }
    } catch (error) {
      formError = "เกิดข้อผิดพลาดในการต่ออายุ subscription";
    } finally {
      loading = false;
    }
  }

  function resetForm() {
    createForm = {
      packageType: "monthly",
      autoRenew: false,
      paymentMethod: "moneyPoint",
    };
    selectedPackage = null;
    discountCode = "";
    discountValidation = null;
    autoRenew = false;
    paymentMethod = "moneyPoint";
    formError = "";
  }

  function openCreateModal() {
    showCreateModal = true;
    resetForm();
  }

  function closeCreateModal() {
    showCreateModal = false;
    resetForm();
  }

  function openRenewModal(subscription: Subscription) {
    selectedSubscription = subscription;
    showRenewModal = true;
  }

  function openCancelModal(subscription: Subscription) {
    selectedSubscription = subscription;
    showCancelModal = true;
  }

  function closeModals() {
    showRenewModal = false;
    showCancelModal = false;
    selectedSubscription = null;
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("th-TH", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  function formatCurrency(amount: number) {
    return new Intl.NumberFormat("th-TH", {
      style: "currency",
      currency: "THB",
    }).format(amount);
  }

  function getStatusColor(status: string) {
    switch (status) {
      case "active":
        return "success";
      case "expired":
        return "error";
      case "cancelled":
        return "neutral";
      case "paused":
        return "warning";
      default:
        return "neutral";
    }
  }

  function getStatusText(status: string) {
    switch (status) {
      case "active":
        return "ใช้งาน";
      case "expired":
        return "หมดอายุ";
      case "cancelled":
        return "ยกเลิก";
      case "paused":
        return "หยุดชั่วคราว";
      default:
        return status;
    }
  }

  function getPackageTypeText(type: string) {
    switch (type) {
      case "monthly":
        return "รายเดือน";
      case "yearly":
        return "รายปี";
      case "permanent":
        return "ถาวร";
      default:
        return type;
    }
  }

  // Watch for discount code changes
  $effect(() => {
    if (discountCode) {
      validateDiscount();
    }
  });
</script>

<svelte:head>
  <title>การจัดการ Subscription - {site?.name || "Dashboard"}</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        การจัดการ Subscription
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        จัดการแพ็คเกจและวันใช้งานของเว็บไซต์
      </p>
    </div>
    <Button onclick={openCreateModal} color="primary" size="lg">
      <Icon icon="mdi:plus" class="w-4 h-4 mr-2" />
      เช่าแพ็คเกจใหม่
    </Button>
  </div>

  <!-- Error Alert -->
  {#if error}
    <Alert color="error">
      <Icon icon="mdi:exclamation-triangle" class="w-5 h-5" />
      <span>{error}</span>
    </Alert>
  {/if}

  <!-- Success Message -->
  {#if successMessage}
    <Alert
      color="success"
      dismissible={true}
      ondismiss={() => (successMessage = "")}
    >
      <Icon icon="mdi:check-circle" class="w-5 h-5" />
      <span>{successMessage}</span>
    </Alert>
  {/if}

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Icon
              icon="mdi:calendar"
              class="w-6 h-6 text-blue-600 dark:text-blue-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              แพ็คเกจที่ใช้งาน
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {activeSubscriptions.length}
            </p>
          </div>
        </div>
      </div>
    </Card>

    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <Icon
              icon="mdi:credit-card"
              class="w-6 h-6 text-green-600 dark:text-green-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              ยอดรวมที่ใช้
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {formatCurrency(totalSpent)}
            </p>
          </div>
        </div>
      </div>
    </Card>

    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <Icon
              icon="mdi:clock"
              class="w-6 h-6 text-yellow-600 dark:text-yellow-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              หมดอายุ
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {expiredSubscriptions.length}
            </p>
          </div>
        </div>
      </div>
    </Card>

    <Card>
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <Icon
              icon="mdi:bell"
              class="w-6 h-6 text-purple-600 dark:text-purple-400"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              การแจ้งเตือน
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {unreadNotifications.length}
            </p>
          </div>
        </div>
      </div>
    </Card>
  </div>

  <!-- Tab Navigation -->
  <div class="border-b border-gray-200 dark:border-gray-700">
    <nav class="-mb-px flex space-x-8">
      <button
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
        class:border-blue-500={activeTab === "active"}
        class:text-blue-600={activeTab === "active"}
        class:border-transparent={activeTab !== "active"}
        class:text-gray-500={activeTab !== "active"}
        onclick={() => (activeTab = "active")}
      >
        แพ็คเกจที่ใช้งาน ({activeSubscriptions.length})
      </button>
      <button
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
        class:border-blue-500={activeTab === "expired"}
        class:text-blue-600={activeTab === "expired"}
        class:border-transparent={activeTab !== "expired"}
        class:text-gray-500={activeTab !== "expired"}
        onclick={() => (activeTab = "expired")}
      >
        แพ็คเกจที่หมดอายุ ({expiredSubscriptions.length})
      </button>
      <button
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
        class:border-blue-500={activeTab === "cancelled"}
        class:text-blue-600={activeTab === "cancelled"}
        class:border-transparent={activeTab !== "cancelled"}
        class:text-gray-500={activeTab !== "cancelled"}
        onclick={() => (activeTab = "cancelled")}
      >
        แพ็คเกจที่ยกเลิก ({cancelledSubscriptions.length})
      </button>
    </nav>
  </div>

  <!-- Tab Content -->
  <div class="mt-6">
    {#if activeTab === "active"}
      <!-- Active Subscriptions -->
      <div class="space-y-4">
        {#if activeSubscriptions.length === 0}
          <Card>
            <div class="p-8 text-center">
              <Icon
                icon="mdi:calendar"
                class="w-12 h-12 text-gray-400 mx-auto mb-4"
              />
              <h3
                class="text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                ไม่มีแพ็คเกจที่ใช้งาน
              </h3>
              <p class="text-gray-600 dark:text-gray-400 mb-4">
                คุณยังไม่ได้เช่าแพ็คเกจใดๆ สำหรับเว็บไซต์นี้
              </p>
              <Button onclick={openCreateModal} color="primary">
                เช่าแพ็คเกจใหม่
              </Button>
            </div>
          </Card>
        {:else}
          {#each activeSubscriptions as subscription (subscription._id)}
            <Card>
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <Badge color={getStatusColor(subscription.status)}>
                      {getStatusText(subscription.status)}
                    </Badge>
                    <Badge color="info">
                      {getPackageTypeText(subscription.packageType)}
                    </Badge>
                    {#if subscription.autoRenew}
                      <Badge color="success">ต่ออายุอัตโนมัติ</Badge>
                    {/if}
                  </div>
                  <div class="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onclick={() => toggleAutoRenew(subscription)}
                      disabled={loading}
                    >
                      <Icon icon="mdi:settings" class="w-4 h-4 mr-1" />
                      {subscription.autoRenew ? "ปิด" : "เปิด"} Auto Renew
                    </Button>
                    <Button
                      size="sm"
                      color="primary"
                      onclick={() => openRenewModal(subscription)}
                      disabled={loading}
                    >
                      <Icon icon="mdi:refresh" class="w-4 h-4 mr-1" />
                      ต่ออายุ
                    </Button>
                    <Button
                      size="sm"
                      color="error"
                      variant="outline"
                      onclick={() => openCancelModal(subscription)}
                      disabled={loading}
                    >
                      <Icon icon="mdi:close-circle" class="w-4 h-4 mr-1" />
                      ยกเลิก
                    </Button>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      วันที่เริ่ม
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {formatDate(subscription.startDate)}
                    </p>
                  </div>
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      วันที่หมดอายุ
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {subscription.endDate
                        ? formatDate(subscription.endDate)
                        : "ไม่ระบุ"}
                    </p>
                  </div>
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      ราคา
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(subscription.pricing.amount)}
                    </p>
                  </div>
                </div>

                {#if subscription.renewalHistory.length > 0}
                  <div class="border-t pt-4">
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2"
                    >
                      ประวัติการต่ออายุ
                    </p>
                    <div class="space-y-2">
                      {#each subscription.renewalHistory.slice(0, 3) as renewal}
                        <div class="flex items-center justify-between text-sm">
                          <span class="text-gray-600 dark:text-gray-400">
                            {formatDate(renewal.renewedAt)} - {renewal.method ===
                            "auto"
                              ? "อัตโนมัติ"
                              : "มือ"}
                          </span>
                          <span class="text-gray-900 dark:text-white"
                            >{formatCurrency(renewal.amount)}</span
                          >
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}
              </div>
            </Card>
          {/each}
        {/if}
      </div>
    {:else if activeTab === "expired"}
      <!-- Expired Subscriptions -->
      <div class="space-y-4">
        {#if expiredSubscriptions.length === 0}
          <Card>
            <div class="p-8 text-center">
              <Icon
                icon="mdi:check-circle"
                class="w-12 h-12 text-green-400 mx-auto mb-4"
              />
              <h3
                class="text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                ไม่มีแพ็คเกจที่หมดอายุ
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                แพ็คเกจทั้งหมดยังใช้งานได้ปกติ
              </p>
            </div>
          </Card>
        {:else}
          {#each expiredSubscriptions as subscription (subscription._id)}
            <Card>
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <Badge color="error">หมดอายุ</Badge>
                    <Badge color="info"
                      >{getPackageTypeText(subscription.packageType)}</Badge
                    >
                  </div>
                  <Button
                    size="sm"
                    color="primary"
                    onclick={() => openRenewModal(subscription)}
                    disabled={loading}
                  >
                    <Icon icon="mdi:refresh" class="w-4 h-4 mr-1" />
                    ต่ออายุ
                  </Button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      วันที่เริ่ม
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {formatDate(subscription.startDate)}
                    </p>
                  </div>
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      วันที่หมดอายุ
                    </p>
                    <p
                      class="text-sm text-red-600 dark:text-red-400 font-medium"
                    >
                      {subscription.endDate
                        ? formatDate(subscription.endDate)
                        : "ไม่ระบุ"}
                    </p>
                  </div>
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      ราคา
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(subscription.pricing.amount)}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          {/each}
        {/if}
      </div>
    {:else if activeTab === "cancelled"}
      <!-- Cancelled Subscriptions -->
      <div class="space-y-4">
        {#if cancelledSubscriptions.length === 0}
          <Card>
            <div class="p-8 text-center">
              <Icon
                icon="mdi:check-circle"
                class="w-12 h-12 text-green-400 mx-auto mb-4"
              />
              <h3
                class="text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                ไม่มีแพ็คเกจที่ยกเลิก
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                ไม่มีแพ็คเกจที่ถูกยกเลิก
              </p>
            </div>
          </Card>
        {:else}
          {#each cancelledSubscriptions as subscription (subscription._id)}
            <Card>
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <Badge color="neutral">ยกเลิก</Badge>
                    <Badge color="info"
                      >{getPackageTypeText(subscription.packageType)}</Badge
                    >
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      วันที่เริ่ม
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {formatDate(subscription.startDate)}
                    </p>
                  </div>
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      วันที่ยกเลิก
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {subscription.updatedAt
                        ? formatDate(subscription.updatedAt)
                        : "ไม่ระบุ"}
                    </p>
                  </div>
                  <div>
                    <p
                      class="text-sm font-medium text-gray-600 dark:text-gray-400"
                    >
                      ราคา
                    </p>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(subscription.pricing.amount)}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          {/each}
        {/if}
      </div>
    {/if}
  </div>

  <!-- Pagination -->
  {#if pagination && pagination.totalPages > 1}
    <div class="flex justify-center">
      <Pagination
        page={pagination.page}
        totalPages={pagination.totalPages}
        onPageChange={(page) => goto(`?page=${page}`)}
      />
    </div>
  {/if}
</div>

<!-- Create Subscription Modal -->
<Modal bind:open={showCreateModal} title="เช่าแพ็คเกจใหม่" size="lg">
  <div class="space-y-6">
    <!-- Package Selection -->
    <div>
      <label
        for="package"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        เลือกแพ็คเกจ
      </label>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        {#each packages as pkg (pkg.type)}
          <Card
            class="cursor-pointer transition-all duration-200 hover:shadow-lg {selectedPackage?.type ===
            pkg.type
              ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : ''}"
            onclick={() => (selectedPackage = pkg)}
          >
            <div class="p-4">
              <div class="flex items-center justify-between mb-2">
                <h3 class="font-semibold text-gray-900 dark:text-white">
                  {pkg.name}
                </h3>
                <Badge color="primary">{formatCurrency(pkg.moneyPoint)}</Badge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {pkg.description}
              </p>
              <div class="space-y-1">
                {#each pkg.features as feature}
                  <div
                    class="flex items-center text-sm text-gray-600 dark:text-gray-400"
                  >
                    <Icon
                      icon="mdi:check"
                      class="w-4 h-4 text-green-500 mr-2"
                    />
                    {feature}
                  </div>
                {/each}
              </div>
            </div>
          </Card>
        {/each}
      </div>
    </div>

    <!-- Discount Code -->
    <div>
      <label
        for="discountCode"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        รหัสส่วนลด (ไม่บังคับ)
      </label>
      <div class="flex space-x-2">
        <Input
          id="discountCode"
          bind:value={discountCode}
          placeholder="ใส่รหัสส่วนลด"
          class="flex-1"
        />
        {#if discountLoading}
          <div class="flex items-center justify-center w-8 h-8">
            <Icon icon="mdi:loading" class="w-4 h-4 animate-spin" />
          </div>
        {/if}
      </div>
      {#if discountValidation}
        <div class="mt-2">
          {#if discountValidation.valid}
            <div
              class="flex items-center text-sm text-green-600 dark:text-green-400"
            >
              <Icon icon="mdi:check-circle" class="w-4 h-4 mr-1" />
              {discountValidation.message}
              {#if discountValidation.discount}
                <span class="ml-2 font-medium"
                  >ส่วนลด {discountValidation.discount} บาท</span
                >
              {/if}
            </div>
          {:else}
            <div
              class="flex items-center text-sm text-red-600 dark:text-red-400"
            >
              <Icon icon="mdi:close-circle" class="w-4 h-4 mr-1" />
              {discountValidation.message}
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- Payment Method -->
    <div>
      <label
        for="paymentMethod"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        วิธีการชำระเงิน
      </label>
      <Select
        id="paymentMethod"
        value={paymentMethod}
        onChange={(value) => (paymentMethod = value as string)}
        options={[
          { value: "moneyPoint", label: "MoneyPoint" },
          { value: "creditCard", label: "บัตรเครดิต" },
          { value: "bankTransfer", label: "โอนเงิน" },
        ]}
      />
    </div>

    <!-- Auto Renew -->
    <div class="flex items-center">
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          bind:checked={autoRenew}
          class="checkbox checkbox-sm"
        />
        <span class="text-sm">ต่ออายุอัตโนมัติเมื่อใกล้หมดอายุ</span>
      </label>
    </div>

    <!-- Error Message -->
    {#if formError}
      <Alert color="error">
        <Icon icon="mdi:exclamation-triangle" class="w-5 h-5" />
        <span>{formError}</span>
      </Alert>
    {/if}

    <!-- Selected Package Summary -->
    {#if selectedPackage}
      <Card class="bg-gray-50 dark:bg-gray-800">
        <div class="p-4">
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">
            สรุปการสั่งซื้อ
          </h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">แพ็คเกจ:</span>
              <span class="text-gray-900 dark:text-white"
                >{selectedPackage.name}</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">ระยะเวลา:</span>
              <span class="text-gray-900 dark:text-white"
                >{selectedPackage.days} วัน</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">ราคา:</span>
              <span class="text-gray-900 dark:text-white"
                >{formatCurrency(selectedPackage.moneyPoint)}</span
              >
            </div>
            {#if discountValidation?.valid && discountValidation.discount}
              <div
                class="flex justify-between text-green-600 dark:text-green-400"
              >
                <span>ส่วนลด:</span>
                <span>-{formatCurrency(discountValidation.discount)}</span>
              </div>
              <div class="flex justify-between font-medium border-t pt-2">
                <span>ราคารวม:</span>
                <span
                  >{formatCurrency(
                    selectedPackage.moneyPoint -
                      (discountValidation.discount || 0),
                  )}</span
                >
              </div>
            {/if}
          </div>
        </div>
      </Card>
    {/if}
  </div>

  <div class="flex justify-end space-x-2">
    <Button variant="outline" onclick={closeCreateModal} disabled={loading}>
      ยกเลิก
    </Button>
    <Button
      color="primary"
      onclick={createSubscription}
      disabled={loading || !selectedPackage}
    >
      {#if loading}
        <Icon icon="mdi:loading" class="w-4 h-4 animate-spin mr-2" />
      {/if}
      เช่าแพ็คเกจ
    </Button>
  </div>
</Modal>

<!-- Renew Subscription Modal -->
<Modal bind:open={showRenewModal} title="ต่ออายุ Subscription" size="md">
  {#if selectedSubscription}
    <div class="space-y-4">
      <p class="text-gray-600 dark:text-gray-400">
        คุณต้องการต่ออายุ subscription นี้หรือไม่?
      </p>

      <Card>
        <div class="p-4">
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">แพ็คเกจ:</span>
              <span class="text-gray-900 dark:text-white"
                >{getPackageTypeText(selectedSubscription.packageType)}</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">ราคา:</span>
              <span class="text-gray-900 dark:text-white"
                >{formatCurrency(selectedSubscription.pricing.amount)}</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400"
                >วันที่หมดอายุ:</span
              >
              <span class="text-gray-900 dark:text-white">
                {selectedSubscription.endDate
                  ? formatDate(selectedSubscription.endDate)
                  : "ไม่ระบุ"}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {#if formError}
        <Alert color="error">
          <Icon icon="mdi:exclamation-triangle" class="w-5 h-5" />
          <span>{formError}</span>
        </Alert>
      {/if}
    </div>
  {/if}

  <div class="flex justify-end space-x-2">
    <Button variant="outline" onclick={closeModals} disabled={loading}>
      ยกเลิก
    </Button>
    <Button
      color="primary"
      onclick={() => renewSubscription(selectedSubscription!)}
      disabled={loading || !selectedSubscription}
    >
      {#if loading}
        <Icon icon="mdi:loading" class="w-4 h-4 animate-spin mr-2" />
      {/if}
      ต่ออายุ
    </Button>
  </div>
</Modal>

<!-- Cancel Subscription Modal -->
<Modal bind:open={showCancelModal} title="ยกเลิก Subscription" size="md">
  {#if selectedSubscription}
    <div class="space-y-4">
      <Alert color="warning">
        <Icon icon="mdi:exclamation-triangle" class="w-5 h-5" />
        <span
          >การยกเลิก subscription จะทำให้เว็บไซต์หยุดทำงานเมื่อแพ็คเกจหมดอายุ</span
        >
      </Alert>

      <p class="text-gray-600 dark:text-gray-400">
        คุณต้องการยกเลิก subscription นี้หรือไม่?
        การดำเนินการนี้ไม่สามารถยกเลิกได้
      </p>

      <Card>
        <div class="p-4">
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">แพ็คเกจ:</span>
              <span class="text-gray-900 dark:text-white"
                >{getPackageTypeText(selectedSubscription.packageType)}</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">สถานะ:</span>
              <Badge color={getStatusColor(selectedSubscription.status)}>
                {getStatusText(selectedSubscription.status)}
              </Badge>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400"
                >วันที่หมดอายุ:</span
              >
              <span class="text-gray-900 dark:text-white">
                {selectedSubscription.endDate
                  ? formatDate(selectedSubscription.endDate)
                  : "ไม่ระบุ"}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {#if formError}
        <Alert color="error">
          <Icon icon="mdi:exclamation-triangle" class="w-5 h-5" />
          <span>{formError}</span>
        </Alert>
      {/if}
    </div>
  {/if}

  <div class="flex justify-end space-x-2">
    <Button variant="outline" onclick={closeModals} disabled={loading}>
      ยกเลิก
    </Button>
    <Button
      color="error"
      onclick={() => cancelSubscription(selectedSubscription!)}
      disabled={loading || !selectedSubscription}
    >
      {#if loading}
        <Icon icon="mdi:loading" class="w-4 h-4 animate-spin mr-2" />
      {/if}
      ยกเลิก Subscription
    </Button>
  </div>
</Modal>
