import type { PageServerLoad, Actions } from './$types';
import { subscriptionService } from '$lib/services/subscription';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;
  const token = locals.token;

  if (!token) {
    return {
      packages: [],
      subscriptions: [],
      notifications: [],
      error: 'ไม่พบ token การยืนยันตัวตน'
    };
  }

  try {
    // ดึงข้อมูลแพ็คเกจ
    const packagesResult = await subscriptionService.getPackages(token);
    
    // ดึงรายการ subscription ของ site
    const page = url.searchParams.get('page') || '1';
    const limit = url.searchParams.get('limit') || '10';
    const status = url.searchParams.get('status') || '';
    
    const subscriptionsResult = await subscriptionService.getSiteSubscriptions(siteId, {
      page,
      limit,
      status
    }, token);

    // ดึงการแจ้งเตือน
    const notificationsResult = await subscriptionService.getSiteNotifications(siteId, {
      page: '1',
      limit: '5',
      unreadOnly: 'true'
    }, token);

    return {
      packages: packagesResult.success ? packagesResult.data : [],
      subscriptions: subscriptionsResult.success ? subscriptionsResult.data?.subscriptions || [] : [],
      pagination: subscriptionsResult.success ? subscriptionsResult.data?.pagination : null,
      notifications: notificationsResult.success ? notificationsResult.data?.notifications || [] : [],
      error: null
    };

  } catch (error) {
    console.error('Error loading subscriptions:', error);
    return {
      packages: [],
      subscriptions: [],
      notifications: [],
      error: 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
    };
  }
};

export const actions: Actions = {
  // สร้าง subscription ใหม่
  create: async ({ request, locals, params }) => {
    const { siteId } = params;
    const token = locals.token;

    if (!token) {
      return fail(401, { error: 'ไม่พบ token การยืนยันตัวตน' });
    }

    try {
      const formData = await request.formData();
      const packageType = formData.get('packageType') as string;
      const autoRenew = formData.get('autoRenew') === 'true';
      const paymentMethod = formData.get('paymentMethod') as string;
      const discountCode = formData.get('discountCode') as string;

      const result = await subscriptionService.createSiteSubscription(siteId, {
        packageType: packageType as any,
        autoRenew,
        paymentMethod,
        discountCode: discountCode || undefined
      }, token);

      if (result.success) {
        return { success: true, message: 'สร้าง subscription สำเร็จ' };
      } else {
        return fail(400, { error: result.error || 'เกิดข้อผิดพลาดในการสร้าง subscription' });
      }
    } catch (error) {
      console.error('Create subscription action error:', error);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการสร้าง subscription' });
    }
  },

  // ต่ออายุ subscription
  renew: async ({ request, locals, params }) => {
    const { siteId } = params;
    const token = locals.token;

    if (!token) {
      return fail(401, { error: 'ไม่พบ token การยืนยันตัวตน' });
    }

    try {
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;

      if (!subscriptionId) {
        return fail(400, { error: 'ไม่พบ subscription ID' });
      }

      const result = await subscriptionService.renewSubscription(siteId, subscriptionId, token);

      if (result.success) {
        return { success: true, message: 'ต่ออายุ subscription สำเร็จ' };
      } else {
        return fail(400, { error: result.error || 'เกิดข้อผิดพลาดในการต่ออายุ subscription' });
      }
    } catch (error) {
      console.error('Renew subscription action error:', error);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการต่ออายุ subscription' });
    }
  },

  // ยกเลิก subscription
  cancel: async ({ request, locals, params }) => {
    const { siteId } = params;
    const token = locals.token;

    if (!token) {
      return fail(401, { error: 'ไม่พบ token การยืนยันตัวตน' });
    }

    try {
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;

      if (!subscriptionId) {
        return fail(400, { error: 'ไม่พบ subscription ID' });
      }

      const result = await subscriptionService.cancelSubscription(siteId, subscriptionId, token);

      if (result.success) {
        return { success: true, message: 'ยกเลิก subscription สำเร็จ' };
      } else {
        return fail(400, { error: result.error || 'เกิดข้อผิดพลาดในการยกเลิก subscription' });
      }
    } catch (error) {
      console.error('Cancel subscription action error:', error);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการยกเลิก subscription' });
    }
  },

  // เปิด/ปิด auto renew
  toggleAutoRenew: async ({ request, locals, params }) => {
    const { siteId } = params;
    const token = locals.token;

    if (!token) {
      return fail(401, { error: 'ไม่พบ token การยืนยันตัวตน' });
    }

    try {
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;
      const autoRenew = formData.get('autoRenew') === 'true';

      if (!subscriptionId) {
        return fail(400, { error: 'ไม่พบ subscription ID' });
      }

      const result = await subscriptionService.toggleAutoRenew(siteId, subscriptionId, autoRenew, token);

      if (result.success) {
        return { 
          success: true, 
          message: `${autoRenew ? 'เปิด' : 'ปิด'}การต่ออายุอัตโนมัติสำเร็จ` 
        };
      } else {
        return fail(400, { error: result.error || 'เกิดข้อผิดพลาดในการเปลี่ยนการตั้งค่า' });
      }
    } catch (error) {
      console.error('Toggle auto renew action error:', error);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการเปลี่ยนการตั้งค่า' });
    }
  },

  // อ่านการแจ้งเตือน
  markNotificationAsRead: async ({ request, locals }) => {
    const token = locals.token;

    if (!token) {
      return fail(401, { error: 'ไม่พบ token การยืนยันตัวตน' });
    }

    try {
      const formData = await request.formData();
      const notificationId = formData.get('notificationId') as string;

      if (!notificationId) {
        return fail(400, { error: 'ไม่พบ notification ID' });
      }

      const result = await subscriptionService.markNotificationAsRead(notificationId, token);

      if (result.success) {
        return { success: true, message: 'อ่านการแจ้งเตือนสำเร็จ' };
      } else {
        return fail(400, { error: result.error || 'เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน' });
      }
    } catch (error) {
      console.error('Mark notification as read action error:', error);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน' });
    }
  }
}; 