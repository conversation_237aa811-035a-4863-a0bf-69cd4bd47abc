<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { apiClient } from '$lib/api/client';
	import { toast } from 'svelte-sonner';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '$lib/components/ui/select';
	import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '$lib/components/ui/dialog';
	import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '$lib/components/ui/table';
	import { 
		Truck, Search, Package, MapPin, Clock, 
		Eye, Edit, Plus, CheckCircle, AlertCircle
	} from 'lucide-svelte';

	interface Shipping {
		_id: string;
		orderId: string;
		trackingNumber?: string;
		carrier: string;
		method: string;
		status: 'preparing' | 'shipped' | 'in_transit' | 'delivered' | 'failed' | 'returned';
		shippingAddress: {
			name: string;
			phone: string;
			address: string;
			district: string;
			province: string;
			postalCode: string;
		};
		estimatedDeliveryDate?: string;
		actualDeliveryDate?: string;
		shippingCost: number;
		notes?: string;
		createdAt: string;
		updatedAt: string;
	}

	interface ShippingMethod {
		_id: string;
		name: string;
		carrier: string;
		cost: number;
		estimatedDays: number;
		isActive: boolean;
	}

	let shippings: Shipping[] = $state([]);
	let shippingMethods: ShippingMethod[] = $state([]);
	let loading = $state(true);
	let searchQuery = $state('');
	let statusFilter = $state('all');
	let carrierFilter = $state('all');
	let selectedShipping: Shipping | null = $state(null);
	let viewDialogOpen = $state(false);
	let updateDialogOpen = $state(false);
	let methodDialogOpen = $state(false);
	let processing = $state(false);

	// ฟอร์มอัปเดตการจัดส่ง
	let updateForm = $state({
		trackingNumber: '',
		status: 'preparing' as Shipping['status'],
		estimatedDeliveryDate: '',
		notes: ''
	});

	// ฟอร์มวิธีการจัดส่ง
	let methodForm = $state({
		name: '',
		carrier: '',
		cost: 0,
		estimatedDays: 1,
		isActive: true
	});

	$: siteId = $page.params.siteId;
	$: filteredShippings = shippings.filter(shipping => {
		const matchesSearch = 
			shipping.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
			(shipping.trackingNumber && shipping.trackingNumber.toLowerCase().includes(searchQuery.toLowerCase())) ||
			shipping.shippingAddress.name.toLowerCase().includes(searchQuery.toLowerCase());

		const matchesStatus = 
			statusFilter === 'all' || shipping.status === statusFilter;

		const matchesCarrier = 
			carrierFilter === 'all' || shipping.carrier === carrierFilter;

		return matchesSearch && matchesStatus && matchesCarrier;
	});

	// ดึงข้อมูลการจัดส่ง
	async function fetchShippings() {
		try {
			loading = true;
			const response = await apiClient.get(`/shipping/${siteId}`);
			
			if (response.success) {
				shippings = response.data.shippings || [];
			} else {
				toast.error(response.message || 'ไม่สามารถดึงข้อมูลการจัดส่งได้');
			}
		} catch (error: any) {
			console.error('Error fetching shippings:', error);
			toast.error('เกิดข้อผิดพลาดในการดึงข้อมูลการจัดส่ง');
		} finally {
			loading = false;
		}
	}

	// ดึงข้อมูลวิธีการจัดส่ง
	async function fetchShippingMethods() {
		try {
			const response = await apiClient.get(`/shipping/${siteId}/methods`);
			
			if (response.success) {
				shippingMethods = response.data.methods || [];
			}
		} catch (error: any) {
			console.error('Error fetching shipping methods:', error);
		}
	}

	// อัปเดตการจัดส่ง
	async function updateShipping() {
		if (!selectedShipping) return;

		try {
			processing = true;
			const response = await apiClient.put(`/shipping/${siteId}/${selectedShipping._id}`, updateForm);
			
			if (response.success) {
				toast.success('อัปเดตการจัดส่งเรียบร้อยแล้ว');
				updateDialogOpen = false;
				await fetchShippings();
			} else {
				toast.error(response.message || 'ไม่สามารถอัปเดตการจัดส่งได้');
			}
		} catch (error: any) {
			console.error('Error updating shipping:', error);
			toast.error('เกิดข้อผิดพลาดในการอัปเดตการจัดส่ง');
		} finally {
			processing = false;
		}
	}

	// สร้างวิธีการจัดส่งใหม่
	async function createShippingMethod() {
		if (!methodForm.name.trim() || !methodForm.carrier.trim()) {
			toast.error('กรุณากรอกข้อมูลให้ครบถ้วน');
			return;
		}

		try {
			processing = true;
			const response = await apiClient.post(`/shipping/${siteId}/methods`, methodForm);
			
			if (response.success) {
				toast.success('สร้างวิธีการจัดส่งเรียบร้อยแล้ว');
				methodDialogOpen = false;
				resetMethodForm();
				await fetchShippingMethods();
			} else {
				toast.error(response.message || 'ไม่สามารถสร้างวิธีการจัดส่งได้');
			}
		} catch (error: any) {
			console.error('Error creating shipping method:', error);
			toast.error('เกิดข้อผิดพลาดในการสร้างวิธีการจัดส่ง');
		} finally {
			processing = false;
		}
	}

	// เปิดดูรายละเอียด
	function viewShipping(shipping: Shipping) {
		selectedShipping = shipping;
		viewDialogOpen = true;
	}

	// เปิดฟอร์มอัปเดต
	function openUpdateDialog(shipping: Shipping) {
		selectedShipping = shipping;
		updateForm = {
			trackingNumber: shipping.trackingNumber || '',
			status: shipping.status,
			estimatedDeliveryDate: shipping.estimatedDeliveryDate 
				? new Date(shipping.estimatedDeliveryDate).toISOString().split('T')[0] 
				: '',
			notes: shipping.notes || ''
		};
		updateDialogOpen = true;
	}

	// รีเซ็ตฟอร์มวิธีการจัดส่ง
	function resetMethodForm() {
		methodForm = {
			name: '',
			carrier: '',
			cost: 0,
			estimatedDays: 1,
			isActive: true
		};
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตามสถานะ
	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case 'preparing': return 'secondary';
			case 'shipped': return 'default';
			case 'in_transit': return 'default';
			case 'delivered': return 'default';
			case 'failed': return 'destructive';
			case 'returned': return 'destructive';
			default: return 'outline';
		}
	}

	// ฟังก์ชันสำหรับแปลงสถานะเป็นภาษาไทย
	function getStatusText(status: string) {
		switch (status) {
			case 'preparing': return 'กำลังเตรียม';
			case 'shipped': return 'จัดส่งแล้ว';
			case 'in_transit': return 'อยู่ระหว่างขนส่ง';
			case 'delivered': return 'ส่งมอบแล้ว';
			case 'failed': return 'จัดส่งไม่สำเร็จ';
			case 'returned': return 'ส่งคืนแล้ว';
			default: return status;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	// ฟังก์ชันสำหรับแปลงเงิน
	function formatCurrency(amount: number) {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB'
		}).format(amount);
	}

	// คำนวณสถิติการจัดส่ง
	$: shippingStats = {
		total: shippings.length,
		preparing: shippings.filter(s => s.status === 'preparing').length,
		shipped: shippings.filter(s => s.status === 'shipped').length,
		inTransit: shippings.filter(s => s.status === 'in_transit').length,
		delivered: shippings.filter(s => s.status === 'delivered').length
	};

	// รายการผู้ให้บริการขนส่ง
	$: carriers = [...new Set(shippings.map(s => s.carrier))];

	onMount(() => {
		fetchShippings();
		fetchShippingMethods();
	});
</script>

<svelte:head>
	<title>จัดการการจัดส่ง - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการการจัดส่ง</h1>
			<p class="text-muted-foreground">จัดการการจัดส่งสินค้าและติดตามสถานะ</p>
		</div>
		
		<Dialog bind:open={methodDialogOpen} onOpenChange={() => resetMethodForm()}>
			<DialogTrigger asChild>
				<Button>
					<Plus class="h-4 w-4 mr-2" />
					เพิ่มวิธีการจัดส่ง
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>เพิ่มวิธีการจัดส่งใหม่</DialogTitle>
					<DialogDescription>
						สร้างวิธีการจัดส่งใหม่สำหรับเว็บไซต์ของคุณ
					</DialogDescription>
				</DialogHeader>
				
				<div class="space-y-4">
					<div class="space-y-2">
						<Label for="method-name">ชื่อวิธีการจัดส่ง</Label>
						<Input 
							id="method-name"
							placeholder="เช่น จัดส่งด่วน"
							bind:value={methodForm.name}
						/>
					</div>
					
					<div class="space-y-2">
						<Label for="carrier">ผู้ให้บริการขนส่ง</Label>
						<Input 
							id="carrier"
							placeholder="เช่น Kerry Express"
							bind:value={methodForm.carrier}
						/>
					</div>
					
					<div class="grid grid-cols-2 gap-4">
						<div class="space-y-2">
							<Label for="cost">ค่าจัดส่ง (บาท)</Label>
							<Input 
								id="cost"
								type="number"
								min="0"
								step="0.01"
								bind:value={methodForm.cost}
							/>
						</div>
						<div class="space-y-2">
							<Label for="days">ระยะเวลา (วัน)</Label>
							<Input 
								id="days"
								type="number"
								min="1"
								bind:value={methodForm.estimatedDays}
							/>
						</div>
					</div>
					
					<div class="flex gap-2">
						<Button 
							onclick={createShippingMethod}
							disabled={processing}
							class="flex-1"
						>
							{#if processing}
								<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							{:else}
								<Plus class="h-4 w-4 mr-2" />
							{/if}
							สร้างวิธีการจัดส่ง
						</Button>
						<Button variant="outline" onclick={() => methodDialogOpen = false}>
							ยกเลิก
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	</div>

	<!-- สถิติการจัดส่ง -->
	<div class="grid gap-4 md:grid-cols-5">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">ทั้งหมด</CardTitle>
				<Package class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{shippingStats.total}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">กำลังเตรียม</CardTitle>
				<Clock class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-orange-600">{shippingStats.preparing}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">จัดส่งแล้ว</CardTitle>
				<Truck class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-blue-600">{shippingStats.shipped}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">ระหว่างขนส่ง</CardTitle>
				<Truck class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-purple-600">{shippingStats.inTransit}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">ส่งมอบแล้ว</CardTitle>
				<CheckCircle class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-green-600">{shippingStats.delivered}</div>
			</CardContent>
		</Card>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card>
		<CardContent class="p-4">
			<div class="flex flex-col md:flex-row gap-4">
				<div class="relative flex-1">
					<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<Input 
						placeholder="ค้นหาการจัดส่ง..."
						bind:value={searchQuery}
						class="pl-10"
					/>
				</div>
				<Select bind:value={statusFilter}>
					<SelectTrigger class="w-full md:w-48">
						<SelectValue placeholder="สถานะ" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">ทั้งหมด</SelectItem>
						<SelectItem value="preparing">กำลังเตรียม</SelectItem>
						<SelectItem value="shipped">จัดส่งแล้ว</SelectItem>
						<SelectItem value="in_transit">ระหว่างขนส่ง</SelectItem>
						<SelectItem value="delivered">ส่งมอบแล้ว</SelectItem>
						<SelectItem value="failed">จัดส่งไม่สำเร็จ</SelectItem>
						<SelectItem value="returned">ส่งคืนแล้ว</SelectItem>
					</SelectContent>
				</Select>
				<Select bind:value={carrierFilter}>
					<SelectTrigger class="w-full md:w-48">
						<SelectValue placeholder="ผู้ให้บริการ" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">ทั้งหมด</SelectItem>
						{#each carriers as carrier}
							<SelectItem value={carrier}>{carrier}</SelectItem>
						{/each}
					</SelectContent>
				</Select>
			</div>
		</CardContent>
	</Card>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Truck class="h-5 w-5" />
					การจัดส่งทั้งหมด ({filteredShippings.length})
				</CardTitle>
			</CardHeader>
			<CardContent>
				{#if filteredShippings.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || statusFilter !== 'all' || carrierFilter !== 'all'
							? 'ไม่พบการจัดส่งที่ตรงกับเงื่อนไข' 
							: 'ยังไม่มีการจัดส่ง'}
					</div>
				{:else}
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>คำสั่งซื้อ</TableHead>
								<TableHead>ผู้รับ</TableHead>
								<TableHead>ผู้ให้บริการ</TableHead>
								<TableHead>เลขติดตาม</TableHead>
								<TableHead>สถานะ</TableHead>
								<TableHead>ค่าจัดส่ง</TableHead>
								<TableHead>วันที่สร้าง</TableHead>
								<TableHead class="text-right">การจัดการ</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each filteredShippings as shipping (shipping._id)}
								<TableRow>
									<TableCell>
										<span class="font-mono text-sm">{shipping.orderId}</span>
									</TableCell>
									<TableCell>
										<div>
											<p class="font-medium">{shipping.shippingAddress.name}</p>
											<p class="text-xs text-muted-foreground">{shipping.shippingAddress.phone}</p>
											<p class="text-xs text-muted-foreground">
												{shipping.shippingAddress.province}
											</p>
										</div>
									</TableCell>
									<TableCell>
										<div>
											<p class="font-medium">{shipping.carrier}</p>
											<p class="text-xs text-muted-foreground">{shipping.method}</p>
										</div>
									</TableCell>
									<TableCell>
										{#if shipping.trackingNumber}
											<span class="font-mono text-sm bg-muted px-2 py-1 rounded">
												{shipping.trackingNumber}
											</span>
										{:else}
											<span class="text-muted-foreground">-</span>
										{/if}
									</TableCell>
									<TableCell>
										<Badge variant={getStatusBadgeVariant(shipping.status)}>
											{getStatusText(shipping.status)}
										</Badge>
									</TableCell>
									<TableCell>
										{formatCurrency(shipping.shippingCost)}
									</TableCell>
									<TableCell class="text-sm text-muted-foreground">
										{formatDate(shipping.createdAt)}
									</TableCell>
									<TableCell class="text-right">
										<div class="flex items-center justify-end gap-2">
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => viewShipping(shipping)}
											>
												<Eye class="h-4 w-4" />
											</Button>
											{#if shipping.status !== 'delivered'}
												<Button 
													variant="ghost" 
													size="sm"
													onclick={() => openUpdateDialog(shipping)}
												>
													<Edit class="h-4 w-4" />
												</Button>
											{/if}
										</div>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				{/if}
			</CardContent>
		</Card>
	{/if}
</div>

<!-- Dialog ดูรายละเอียด -->
<Dialog bind:open={viewDialogOpen}>
	<DialogContent class="max-w-2xl">
		{#if selectedShipping}
			<DialogHeader>
				<DialogTitle>รายละเอียดการจัดส่ง</DialogTitle>
				<DialogDescription>
					คำสั่งซื้อ {selectedShipping.orderId}
				</DialogDescription>
			</DialogHeader>
			
			<div class="space-y-4">
				<div class="grid grid-cols-2 gap-4">
					<div>
						<p class="text-sm font-medium">ผู้ให้บริการขนส่ง</p>
						<p class="text-sm">{selectedShipping.carrier}</p>
						<p class="text-xs text-muted-foreground">{selectedShipping.method}</p>
					</div>
					<div>
						<p class="text-sm font-medium">เลขติดตาม</p>
						{#if selectedShipping.trackingNumber}
							<p class="text-sm font-mono">{selectedShipping.trackingNumber}</p>
						{:else}
							<p class="text-sm text-muted-foreground">ยังไม่มี</p>
						{/if}
					</div>
				</div>
				
				<div>
					<p class="text-sm font-medium mb-2">ที่อยู่จัดส่ง</p>
					<div class="bg-muted p-3 rounded-lg text-sm">
						<p class="font-medium">{selectedShipping.shippingAddress.name}</p>
						<p>{selectedShipping.shippingAddress.phone}</p>
						<p>{selectedShipping.shippingAddress.address}</p>
						<p>
							{selectedShipping.shippingAddress.district} 
							{selectedShipping.shippingAddress.province} 
							{selectedShipping.shippingAddress.postalCode}
						</p>
					</div>
				</div>
				
				<div class="grid grid-cols-2 gap-4">
					<div>
						<p class="text-sm font-medium">สถานะ</p>
						<Badge variant={getStatusBadgeVariant(selectedShipping.status)}>
							{getStatusText(selectedShipping.status)}
						</Badge>
					</div>
					<div>
						<p class="text-sm font-medium">ค่าจัดส่ง</p>
						<p class="text-sm font-bold">{formatCurrency(selectedShipping.shippingCost)}</p>
					</div>
				</div>
				
				{#if selectedShipping.estimatedDeliveryDate}
					<div>
						<p class="text-sm font-medium">วันที่คาดว่าจะส่งมอบ</p>
						<p class="text-sm">{formatDate(selectedShipping.estimatedDeliveryDate)}</p>
					</div>
				{/if}
				
				{#if selectedShipping.actualDeliveryDate}
					<div>
						<p class="text-sm font-medium">วันที่ส่งมอบจริง</p>
						<p class="text-sm">{formatDate(selectedShipping.actualDeliveryDate)}</p>
					</div>
				{/if}
				
				{#if selectedShipping.notes}
					<div>
						<p class="text-sm font-medium">หมายเหตุ</p>
						<p class="text-sm whitespace-pre-wrap">{selectedShipping.notes}</p>
					</div>
				{/if}
			</div>
		{/if}
	</DialogContent>
</Dialog>

<!-- Dialog อัปเดตการจัดส่ง -->
<Dialog bind:open={updateDialogOpen}>
	<DialogContent>
		{#if selectedShipping}
			<DialogHeader>
				<DialogTitle>อัปเดตการจัดส่ง</DialogTitle>
				<DialogDescription>
					คำสั่งซื้อ {selectedShipping.orderId}
				</DialogDescription>
			</DialogHeader>
			
			<div class="space-y-4">
				<div class="space-y-2">
					<Label for="tracking">เลขติดตาม</Label>
					<Input 
						id="tracking"
						placeholder="เลขติดตามพัสดุ"
						bind:value={updateForm.trackingNumber}
					/>
				</div>
				
				<div class="space-y-2">
					<Label for="status">สถานะ</Label>
					<Select bind:value={updateForm.status}>
						<SelectTrigger>
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="preparing">กำลังเตรียม</SelectItem>
							<SelectItem value="shipped">จัดส่งแล้ว</SelectItem>
							<SelectItem value="in_transit">ระหว่างขนส่ง</SelectItem>
							<SelectItem value="delivered">ส่งมอบแล้ว</SelectItem>
							<SelectItem value="failed">จัดส่งไม่สำเร็จ</SelectItem>
							<SelectItem value="returned">ส่งคืนแล้ว</SelectItem>
						</SelectContent>
					</Select>
				</div>
				
				<div class="space-y-2">
					<Label for="delivery-date">วันที่คาดว่าจะส่งมอบ</Label>
					<Input 
						id="delivery-date"
						type="date"
						bind:value={updateForm.estimatedDeliveryDate}
					/>
				</div>
				
				<div class="space-y-2">
					<Label for="notes">หมายเหตุ</Label>
					<textarea 
						id="notes"
						class="w-full p-2 border rounded-md"
						rows="3"
						placeholder="หมายเหตุเพิ่มเติม..."
						bind:value={updateForm.notes}
					></textarea>
				</div>
				
				<div class="flex gap-2">
					<Button 
						onclick={updateShipping}
						disabled={processing}
						class="flex-1"
					>
						{#if processing}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
						{:else}
							<CheckCircle class="h-4 w-4 mr-2" />
						{/if}
						อัปเดตการจัดส่ง
					</Button>
					<Button variant="outline" onclick={() => updateDialogOpen = false}>
						ยกเลิก
					</Button>
				</div>
			</div>
		{/if}
	</DialogContent>
</Dialog>
