<script lang="ts">
    import Icon from "@iconify/svelte";
    import { t } from "svelte-i18n";
    import { authStore } from "$lib/stores/auth.svelte";
    import { siteStore } from "$lib/stores/site.svelte";
    import Chart from "$lib/components/ui/Chart.svelte";
    import SEO from "$lib/components/layout/SEO.svelte";

    let { data } = $props<{
        data: { user?: any; site?: any; error?: string };
    }>();

    let user = $derived(data?.user || authStore.user);
    let error = $derived(data?.error);
    let site = $derived(data?.site);

    // รอให้ authStore initialized
    let isReady = $derived(authStore.isInitialized);

    // Mock data สำหรับ orders dashboard
    const orderData = {
        revenue: {
            labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
            datasets: [{
                label: 'รายได้ (บาท)',
                data: [85000, 120000, 95000, 150000, 130000, 180000],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        },
        status: {
            labels: ['รอดำเนินการ', 'กำลังจัดส่ง', 'จัดส่งแล้ว', 'ยกเลิก'],
            datasets: [{
                data: [12, 8, 45, 3],
                backgroundColor: [
                    'rgb(59, 130, 246)',
                    'rgb(245, 158, 11)',
                    'rgb(34, 197, 94)',
                    'rgb(239, 68, 68)'
                ]
            }]
        }
    };

    const chartOptions = {
        plugins: {
            legend: {
                display: true,
                position: 'top' as const
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };

    const pieOptions = {
        plugins: {
            legend: {
                display: true,
                position: 'bottom' as const
            }
        }
    };

    // สถิติคำสั่งซื้อ
    const orderStats = [
        {
            title: 'คำสั่งซื้อทั้งหมด',
            value: '68',
            change: '+12%',
            changeType: 'positive',
            icon: 'mdi:shopping-cart',
            color: 'bg-blue-500'
        },
        {
            title: 'รายได้รวม',
            value: '฿750,000',
            change: '+18%',
            changeType: 'positive',
            icon: 'mdi:currency-usd',
            color: 'bg-green-500'
        },
        {
            title: 'รอดำเนินการ',
            value: '12',
            change: '+3',
            changeType: 'positive',
            icon: 'mdi:clock-outline',
            color: 'bg-yellow-500'
        },
        {
            title: 'จัดส่งแล้ว',
            value: '45',
            change: '+8',
            changeType: 'positive',
            icon: 'mdi:check-circle',
            color: 'bg-purple-500'
        }
    ];

    // คำสั่งซื้อล่าสุด
    const recentOrders = [
        {
            id: 'ORD-2024-001',
            customer: 'สมชาย ใจดี',
            email: '<EMAIL>',
            total: '฿2,500',
            status: 'pending',
            date: '2024-01-15',
            items: 3,
            payment: 'credit_card'
        },
        {
            id: 'ORD-2024-002',
            customer: 'สมหญิง สวยงาม',
            email: '<EMAIL>',
            total: '฿1,890',
            status: 'shipping',
            date: '2024-01-14',
            items: 2,
            payment: 'bank_transfer'
        },
        {
            id: 'ORD-2024-003',
            customer: 'สมศักดิ์ เก่งกล้า',
            email: '<EMAIL>',
            total: '฿4,200',
            status: 'delivered',
            date: '2024-01-13',
            items: 5,
            payment: 'cash_on_delivery'
        },
        {
            id: 'ORD-2024-004',
            customer: 'สมปอง รักดี',
            email: '<EMAIL>',
            total: '฿890',
            status: 'cancelled',
            date: '2024-01-12',
            items: 1,
            payment: 'credit_card'
        }
    ];

    function getStatusBadge(status: string) {
        const badges = {
            pending: 'badge-warning',
            shipping: 'badge-info',
            delivered: 'badge-success',
            cancelled: 'badge-error'
        };
        return badges[status as keyof typeof badges] || 'badge-neutral';
    }

    function getStatusText(status: string) {
        const texts = {
            pending: 'รอดำเนินการ',
            shipping: 'กำลังจัดส่ง',
            delivered: 'จัดส่งแล้ว',
            cancelled: 'ยกเลิก'
        };
        return texts[status as keyof typeof texts] || status;
    }

    function getPaymentIcon(payment: string) {
        const icons = {
            credit_card: 'mdi:credit-card',
            bank_transfer: 'mdi:bank',
            cash_on_delivery: 'mdi:cash',
            paypal: 'mdi:paypal'
        };
        return icons[payment as keyof typeof icons] || 'mdi:credit-card';
    }

    function getPaymentText(payment: string) {
        const texts = {
            credit_card: 'บัตรเครดิต',
            bank_transfer: 'โอนเงิน',
            cash_on_delivery: 'เก็บเงินปลายทาง',
            paypal: 'PayPal'
        };
        return texts[payment as keyof typeof texts] || payment;
    }
</script>

<SEO
    title="Orders - จัดการคำสั่งซื้อ"
    description="จัดการคำสั่งซื้อ ดูสถิติการขาย และติดตามสถานะ"
    keywords="orders, คำสั่งซื้อ, การขาย, สถิติ, สถานะ"
    url="/dashboard/orders"
    noindex={true}
/>

{#if !isReady}
    <div class="flex items-center justify-center min-h-screen">
        <div class="loading loading-spinner loading-lg"></div>
    </div>
{:else}
    <div class="p-6 space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-base-content">
                    <Icon icon="mdi:shopping-cart" class="w-8 h-8 inline mr-2" />
                    จัดการคำสั่งซื้อ
                </h1>
                <p class="text-base-content/60 mt-1">
                    จัดการคำสั่งซื้อ ดูสถิติการขาย และติดตามสถานะ
                </p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary">
                    <Icon icon="mdi:plus" class="w-5 h-5" />
                    สร้างคำสั่งซื้อ
                </button>
                <button class="btn btn-outline">
                    <Icon icon="mdi:download" class="w-5 h-5" />
                    ส่งออกข้อมูล
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {#each orderStats as stat}
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-base-content/60">{stat.title}</p>
                                <p class="text-2xl font-bold text-base-content">{stat.value}</p>
                                <p class="text-sm {stat.changeType === 'positive' ? 'text-green-500' : stat.changeType === 'negative' ? 'text-red-500' : 'text-gray-500'}">
                                    {stat.change} จากเดือนที่แล้ว
                                </p>
                            </div>
                            <div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
                                <Icon icon={stat.icon} class="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>
            {/each}
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Revenue Chart -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:chart-line" class="w-5 h-5" />
                        รายได้รายเดือน
                    </h3>
                    <Chart data={orderData.revenue} options={chartOptions} height="300px" />
                </div>
            </div>

            <!-- Order Status -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:pie-chart" class="w-5 h-5" />
                        สถานะคำสั่งซื้อ
                    </h3>
                    <Chart type="doughnut" data={orderData.status} options={pieOptions} height="300px" />
                </div>
            </div>
        </div>

        <!-- Orders Table -->
        <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="card-title">
                        <Icon icon="mdi:shopping-cart" class="w-5 h-5" />
                        คำสั่งซื้อล่าสุด
                    </h3>
                    <button class="btn btn-sm btn-outline">
                        ดูทั้งหมด
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="table table-zebra">
                        <thead>
                            <tr>
                                <th>คำสั่งซื้อ</th>
                                <th>ลูกค้า</th>
                                <th>อีเมล</th>
                                <th>จำนวนสินค้า</th>
                                <th>ยอดรวม</th>
                                <th>การชำระเงิน</th>
                                <th>สถานะ</th>
                                <th>วันที่</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each recentOrders as order}
                                <tr>
                                    <td>
                                        <div class="font-bold">{order.id}</div>
                                    </td>
                                    <td>{order.customer}</td>
                                    <td>{order.email}</td>
                                    <td>
                                        <span class="badge badge-outline">
                                            {order.items} รายการ
                                        </span>
                                    </td>
                                    <td>{order.total}</td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <Icon icon={getPaymentIcon(order.payment)} class="w-4 h-4" />
                                            <span class="text-sm">{getPaymentText(order.payment)}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge {getStatusBadge(order.status)}">
                                            {getStatusText(order.status)}
                                        </span>
                                    </td>
                                    <td>{order.date}</td>
                                    <td>
                                        <div class="flex gap-1">
                                            <button class="btn btn-xs btn-outline">
                                                <Icon icon="mdi:eye" class="w-3 h-3" />
                                            </button>
                                            <button class="btn btn-xs btn-outline">
                                                <Icon icon="mdi:pencil" class="w-3 h-3" />
                                            </button>
                                            <button class="btn btn-xs btn-outline">
                                                <Icon icon="mdi:truck" class="w-3 h-3" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/if} 