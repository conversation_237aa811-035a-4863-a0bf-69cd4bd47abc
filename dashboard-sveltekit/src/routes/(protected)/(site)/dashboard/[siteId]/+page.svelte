<script lang="ts">
    import Icon from "@iconify/svelte";
    import { t } from "svelte-i18n";
    import { authStore } from "$lib/stores/auth.svelte";
    import { siteStore } from "$lib/stores/site.svelte";
    import {
        showSuccessToast,
        showErrorToast,
        showWarningToast,
        showInfoToast,
        showConfirm,
        showSuccess,
    } from "$lib/utils/sweetalert";
    import SEO from "$lib/components/layout/SEO.svelte";
    import Chart from "$lib/components/ui/Chart.svelte";
    import { page } from "$app/state";
    import Image from "$lib/components/ui/Image.svelte";

    let { data } = $props<{
        data: { user?: any; site?: any; error?: string };
    }>();

    let user = $derived(data?.user || authStore.user);
    let error = $derived(data?.error);
    let site = $derived(data?.site);

    // รอให้ authStore initialized
    let isReady = $derived(authStore.isInitialized);

    // Mock data สำหรับ dashboard
    const mockData = {
        sales: {
            labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
            datasets: [{
                label: 'ยอดขาย (บาท)',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        },
        visitors: {
            labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
            datasets: [{
                label: 'ผู้เข้าชม',
                data: [1200, 1900, 1500, 2500, 2200, 3000],
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4
            }]
        },
        orders: {
            labels: ['รอดำเนินการ', 'กำลังจัดส่ง', 'จัดส่งแล้ว', 'ยกเลิก'],
            datasets: [{
                data: [12, 8, 45, 3],
                backgroundColor: [
                    'rgb(59, 130, 246)',
                    'rgb(245, 158, 11)',
                    'rgb(34, 197, 94)',
                    'rgb(239, 68, 68)'
                ]
            }]
        },
        products: {
            labels: ['สินค้าขายดี', 'สินค้าใหม่', 'สินค้าลดราคา', 'สินค้าปกติ'],
            datasets: [{
                data: [35, 25, 20, 20],
                backgroundColor: [
                    'rgb(59, 130, 246)',
                    'rgb(34, 197, 94)',
                    'rgb(245, 158, 11)',
                    'rgb(156, 163, 175)'
                ]
            }]
        }
    };

    const chartOptions = {
        plugins: {
            legend: {
                display: true,
                position: 'top' as const
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };

    const pieOptions = {
        plugins: {
            legend: {
                display: true,
                position: 'bottom' as const
            }
        }
    };

    // สถิติสรุป
    const stats = [
        {
            title: 'ยอดขายรวม',
            value: '฿127,000',
            change: '+15%',
            changeType: 'positive',
            icon: 'mdi:currency-usd',
            color: 'bg-blue-500'
        },
        {
            title: 'คำสั่งซื้อ',
            value: '68',
            change: '+8%',
            changeType: 'positive',
            icon: 'mdi:shopping-cart',
            color: 'bg-green-500'
        },
        {
            title: 'ผู้เข้าชม',
            value: '12,300',
            change: '+12%',
            changeType: 'positive',
            icon: 'mdi:eye',
            color: 'bg-purple-500'
        },
        {
            title: 'สินค้าขายดี',
            value: '35',
            change: '+5%',
            changeType: 'positive',
            icon: 'mdi:star',
            color: 'bg-orange-500'
        }
    ];

    // กิจกรรมล่าสุด
    const recentActivities = [
        {
            type: 'order',
            message: 'คำสั่งซื้อใหม่ #ORD-2024-001',
            time: '2 นาทีที่แล้ว',
            icon: 'mdi:shopping-cart',
            color: 'text-green-500'
        },
        {
            type: 'payment',
            message: 'ชำระเงินสำเร็จ ฿2,500',
            time: '5 นาทีที่แล้ว',
            icon: 'mdi:credit-card',
            color: 'text-blue-500'
        },
        {
            type: 'product',
            message: 'เพิ่มสินค้าใหม่: iPhone 15 Pro',
            time: '10 นาทีที่แล้ว',
            icon: 'mdi:package',
            color: 'text-purple-500'
        },
        {
            type: 'customer',
            message: 'ลูกค้าใหม่: สมชาย ใจดี',
            time: '15 นาทีที่แล้ว',
            icon: 'mdi:account-plus',
            color: 'text-orange-500'
        }
    ];
</script>

<SEO
    title="Dashboard - จัดการเว็บไซต์ของคุณ"
    description="จัดการเว็บไซต์ร้านค้าออนไลน์ของคุณ สร้างเว็บไซต์ใหม่ เติมเงิน และดูสถิติการขาย"
    keywords="dashboard, จัดการเว็บไซต์, ร้านค้าออนไลน์, สถิติการขาย, เว็บไซต์"
    url="/dashboard"
    noindex={true}
/>

{#if !isReady}
    <div class="flex items-center justify-center min-h-screen">
        <div class="loading loading-spinner loading-lg"></div>
    </div>
{:else}
    <div class="p-6 space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-base-content">
                    {#if $siteStore.site}
                        Dashboard - {$siteStore.site.name}
                    {:else}
                        Dashboard
                    {/if}
                </h1>
                <p class="text-base-content/60 mt-1">
                    ยินดีต้อนรับกลับ! นี่คือภาพรวมของร้านค้าออนไลน์ของคุณ
                </p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary">
                    <Icon icon="mdi:plus" class="w-5 h-5" />
                    เพิ่มสินค้า
                </button>
                <button class="btn btn-outline">
                    <Icon icon="mdi:cog" class="w-5 h-5" />
                    ตั้งค่า
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {#each stats as stat}
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-base-content/60">{stat.title}</p>
                                <p class="text-2xl font-bold text-base-content">{stat.value}</p>
                                <p class="text-sm {stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'}">
                                    {stat.change} จากเดือนที่แล้ว
                                </p>
                            </div>
                            <div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
                                <Icon icon={stat.icon} class="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>
            {/each}
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sales Chart -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:chart-line" class="w-5 h-5" />
                        ยอดขายรายเดือน
                    </h3>
                    <Chart data={mockData.sales} options={chartOptions} height="300px" />
                </div>
            </div>

            <!-- Visitors Chart -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:eye" class="w-5 h-5" />
                        ผู้เข้าชมรายเดือน
                    </h3>
                    <Chart data={mockData.visitors} options={chartOptions} height="300px" />
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Orders Status -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:shopping-cart" class="w-5 h-5" />
                        สถานะคำสั่งซื้อ
                    </h3>
                    <Chart type="doughnut" data={mockData.orders} options={pieOptions} height="300px" />
                </div>
            </div>

            <!-- Product Categories -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:package" class="w-5 h-5" />
                        ประเภทสินค้า
                    </h3>
                    <Chart type="pie" data={mockData.products} options={pieOptions} height="300px" />
                </div>
            </div>
        </div>

        <!-- Recent Activities & Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Recent Activities -->
            <div class="card bg-base-100 shadow-lg lg:col-span-2">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:clock-outline" class="w-5 h-5" />
                        กิจกรรมล่าสุด
                    </h3>
                    <div class="space-y-4">
                        {#each recentActivities as activity}
                            <div class="flex items-center gap-3 p-3 rounded-lg bg-base-200">
                                <div class="w-10 h-10 rounded-full bg-base-300 flex items-center justify-center">
                                    <Icon icon={activity.icon} class="w-5 h-5 {activity.color}" />
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-base-content">{activity.message}</p>
                                    <p class="text-sm text-base-content/60">{activity.time}</p>
                                </div>
                            </div>
                        {/each}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:lightning-bolt" class="w-5 h-5" />
                        การดำเนินการด่วน
                    </h3>
                    <div class="space-y-3">
                        <button class="btn btn-primary btn-sm w-full justify-start">
                            <Icon icon="mdi:plus" class="w-4 h-4" />
                            เพิ่มสินค้าใหม่
                        </button>
                        <button class="btn btn-outline btn-sm w-full justify-start">
                            <Icon icon="mdi:shopping-cart" class="w-4 h-4" />
                            ดูคำสั่งซื้อ
                        </button>
                        <button class="btn btn-outline btn-sm w-full justify-start">
                            <Icon icon="mdi:account-group" class="w-4 h-4" />
                            จัดการลูกค้า
                        </button>
                        <button class="btn btn-outline btn-sm w-full justify-start">
                            <Icon icon="mdi:chart-bar" class="w-4 h-4" />
                            ดูรายงาน
                        </button>
                        <button class="btn btn-outline btn-sm w-full justify-start">
                            <Icon icon="mdi:cog" class="w-4 h-4" />
                            ตั้งค่าร้านค้า
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/if}
