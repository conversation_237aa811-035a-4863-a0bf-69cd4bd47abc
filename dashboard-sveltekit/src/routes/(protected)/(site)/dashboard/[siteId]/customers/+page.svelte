<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { PageData } from './$types';
	        import Icon from '@iconify/svelte';
        import { customerService } from '$lib/services/customer';
        import type { Customer } from '$lib/types';
        import { $fetch } from 'ofetch';

	let { data } = $props<{ data: PageData }>();

	const customers = $state(data.customers);
	const pagination = $state(data.pagination);
	const search = $state(data.search);
	let isLoading = $state(false);
	let message = $state('');
	let messageType = $state<'success' | 'error' | ''>('');
	let selectedCustomer = $state<Customer | null>(null);
	let showEditModal = $state(false);
	let showDeleteModal = $state(false);

	// Search functionality
	async function handleSearch() {
		isLoading = true;
		try {
			const url = new URL(window.location.href);
			url.searchParams.set('search', search);
			url.searchParams.set('page', '1');
			await goto(url.toString());
		} catch (error) {
			console.error('Error searching:', error);
			showMessage('เกิดข้อผิดพลาดในการค้นหา', 'error');
		} finally {
			isLoading = false;
		}
	}

	// Pagination
	async function goToPage(pageNum: number) {
		isLoading = true;
		try {
			const url = new URL(window.location.href);
			url.searchParams.set('page', pageNum.toString());
			await goto(url.toString());
		} catch (error) {
			console.error('Error navigating:', error);
			showMessage('เกิดข้อผิดพลาดในการนำทาง', 'error');
		} finally {
			isLoading = false;
		}
	}

	// Edit customer
	function openEditModal(customer: Customer) {
		selectedCustomer = customer;
		showEditModal = true;
	}

	function closeEditModal() {
		selectedCustomer = null;
		showEditModal = false;
	}

	async function handleEditCustomer({ result }: any) {
		isLoading = true;
		try {
			if (result.type === 'success') {
				showMessage('อัปเดตลูกค้าสำเร็จ', 'success');
				closeEditModal();
				// Refresh the page to get updated data
				window.location.reload();
			} else {
				showMessage(result.data?.error || 'เกิดข้อผิดพลาดในการอัปเดต', 'error');
			}
		} catch (error) {
			console.error('Error updating customer:', error);
			showMessage('เกิดข้อผิดพลาดในการอัปเดต', 'error');
		} finally {
			isLoading = false;
		}
	}

	// Delete customer
	function openDeleteModal(customer: Customer) {
		selectedCustomer = customer;
		showDeleteModal = true;
	}

	function closeDeleteModal() {
		selectedCustomer = null;
		showDeleteModal = false;
	}

	async function handleDeleteCustomer() {
		if (!selectedCustomer) return;

		isLoading = true;
		try {
			const formData = new FormData();
			formData.append('customerId', selectedCustomer._id);

			const result = await $fetch('?/deleteCustomer', {
				method: 'POST',
				body: formData,
				responseType: 'json'
			});

			if (result.type === 'success') {
				showMessage('ลบลูกค้าสำเร็จ', 'success');
				closeDeleteModal();
				// Refresh the page to get updated data
				window.location.reload();
			} else {
				showMessage(result.data?.error || 'เกิดข้อผิดพลาดในการลบ', 'error');
			}
		} catch (error) {
			console.error('Error deleting customer:', error);
			showMessage('เกิดข้อผิดพลาดในการลบ', 'error');
		} finally {
			isLoading = false;
		}
	}

	function showMessage(msg: string, type: 'success' | 'error') {
		message = msg;
		messageType = type;
		setTimeout(() => {
			message = '';
			messageType = '';
		}, 3000);
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getFullName(customer: Customer) {
		const firstName = customer.firstName || '';
		const lastName = customer.lastName || '';
		return `${firstName} ${lastName}`.trim() || 'ไม่ระบุชื่อ';
	}
</script>

<svelte:head>
	<title>จัดการลูกค้า - {page.data.site?.name || 'Dashboard'}</title>
</svelte:head>

<div class="container mx-auto p-6">
	<!-- Header -->
	<div class="flex justify-between items-center mb-6">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">จัดการลูกค้า</h1>
			<p class="text-gray-600">จัดการข้อมูลลูกค้าทั้งหมดในเว็บไซต์</p>
		</div>
		<div class="flex gap-2">
			<button 
				class="btn btn-primary" 
				onclick={() => window.location.reload()}
				disabled={isLoading}
			>
				<Icon icon="mdi:refresh" class="w-4 h-4" />
				รีเฟรช
			</button>
		</div>
	</div>

	<!-- Search and Filters -->
	<div class="card bg-base-100 shadow-sm mb-6">
		<div class="card-body">
			<div class="flex gap-4 items-end">
				<div class="form-control flex-1">
					<label class="label" for="search-customer">
						<span class="label-text">ค้นหาลูกค้า</span>
					</label>
					<div class="input-group">
						<input
							id="search-customer"
							type="text"
							placeholder="ค้นหาด้วยชื่อ, นามสกุล, หรืออีเมล"
							class="input input-bordered flex-1"
							bind:value={search}
							onkeydown={(e) => e.key === 'Enter' && handleSearch()}
						/>
						<button 
							class="btn btn-primary" 
							onclick={handleSearch}
							disabled={isLoading}
						>
							<Icon icon="mdi:magnify" class="w-4 h-4" />
							ค้นหา
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Message -->
	{#if message}
		<div class="alert alert-{messageType === 'success' ? 'success' : 'error'} mb-6">
			<Icon icon={messageType === 'success' ? 'mdi:check-circle' : 'mdi:alert-circle'} class="w-5 h-5" />
			<span>{message}</span>
		</div>
	{/if}

	<!-- Customers Table -->
	<div class="card bg-base-100 shadow-sm">
		<div class="card-body">
			{#if customers.length === 0}
				<div class="text-center py-8">
					<Icon icon="mdi:account-group-outline" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
					<h3 class="text-lg font-semibold text-gray-600 mb-2">ไม่พบลูกค้า</h3>
					<p class="text-gray-500">ยังไม่มีลูกค้าในระบบ</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>ลูกค้า</th>
								<th>อีเมล</th>
								<th>เบอร์โทร</th>
								<th>เงิน</th>
								<th>ทอง</th>
								<th>สถานะ</th>
								<th>วันที่สมัคร</th>
								<th>การจัดการ</th>
							</tr>
						</thead>
						<tbody>
							{#each customers as customer}
								<tr>
									<td>
										<div class="flex items-center gap-3">
											<div class="avatar">
												<div class="mask mask-squircle w-12 h-12">
													<img src={`https://res.cloudinary.com/demo/image/upload/v1/samples/people/${customer.avatar}.jpg`} alt="Avatar" />
												</div>
											</div>
											<div>
												<div class="font-bold">{getFullName(customer)}</div>
												<div class="text-sm opacity-50">ID: {customer._id}</div>
											</div>
										</div>
									</td>
									<td>
										<div class="flex items-center gap-2">
											<span>{customer.email}</span>
											{#if customer.isEmailVerified}
												<div class="badge badge-success badge-sm">
													<Icon icon="mdi:check-circle" class="w-3 h-3" />
													ยืนยันแล้ว
												</div>
											{:else}
												<div class="badge badge-warning badge-sm">
													<Icon icon="mdi:alert-circle" class="w-3 h-3" />
													ยังไม่ยืนยัน
												</div>
											{/if}
										</div>
									</td>
									<td>{customer.phone || '-'}</td>
									<td>
										<div class="flex items-center gap-1">
											<Icon icon="mdi:currency-usd" class="w-4 h-4 text-green-500" />
											<span class="font-mono">{customer.moneyPoint.toLocaleString()}</span>
										</div>
									</td>
									<td>
										<div class="flex items-center gap-1">
											<Icon icon="mdi:star" class="w-4 h-4 text-yellow-500" />
											<span class="font-mono">{customer.goldPoint.toLocaleString()}</span>
										</div>
									</td>
									<td>
										<div class="badge badge-{customer.isEmailVerified ? 'success' : 'warning'}">
											{customer.isEmailVerified ? 'ใช้งานได้' : 'รอยืนยัน'}
										</div>
									</td>
									<td>{formatDate(customer.createdAt)}</td>
									<td>
										<div class="flex gap-1">
											<button 
												class="btn btn-sm btn-outline"
												onclick={() => openEditModal(customer)}
											>
												<Icon icon="mdi:pencil" class="w-4 h-4" />
												แก้ไข
											</button>
											<button 
												class="btn btn-sm btn-error"
												onclick={() => openDeleteModal(customer)}
											>
												<Icon icon="mdi:delete" class="w-4 h-4" />
												ลบ
											</button>
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{#if pagination.totalPages > 1}
					<div class="flex justify-center mt-6">
						<div class="join">
							{#if pagination.page > 1}
								<button 
									class="join-item btn btn-outline"
									onclick={() => goToPage(pagination.page - 1)}
									disabled={isLoading}
								>
									<Icon icon="mdi:chevron-left" class="w-4 h-4" />
								</button>
							{/if}

							{#each Array.from({ length: pagination.totalPages }, (_, i) => i + 1) as pageNum}
								<button 
									class="join-item btn {pageNum === pagination.page ? 'btn-primary' : 'btn-outline'}"
									onclick={() => goToPage(pageNum)}
									disabled={isLoading}
								>
									{pageNum}
								</button>
							{/each}

							{#if pagination.page < pagination.totalPages}
								<button 
									class="join-item btn btn-outline"
									onclick={() => goToPage(pagination.page + 1)}
									disabled={isLoading}
								>
									<Icon icon="mdi:chevron-right" class="w-4 h-4" />
								</button>
							{/if}
						</div>
					</div>
				{/if}
			{/if}
		</div>
	</div>
</div>

<!-- Edit Customer Modal -->
{#if showEditModal && selectedCustomer}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">แก้ไขข้อมูลลูกค้า</h3>
			<form method="POST" action="?/updateCustomer" use:enhance={handleEditCustomer}>
				<input type="hidden" name="customerId" value={selectedCustomer._id} />
				
				<div class="grid grid-cols-2 gap-4 mb-4">
					<div class="form-control">
						<label class="label" for="edit-customer-firstname">
							<span class="label-text">ชื่อ</span>
						</label>
						<input 
							id="edit-customer-firstname"
							type="text" 
							name="firstName"
							class="input input-bordered" 
							value={selectedCustomer.firstName || ''}
						/>
					</div>
					<div class="form-control">
						<label class="label" for="edit-customer-lastname">
							<span class="label-text">นามสกุล</span>
						</label>
						<input 
							id="edit-customer-lastname"
							type="text" 
							name="lastName"
							class="input input-bordered" 
							value={selectedCustomer.lastName || ''}
						/>
					</div>
				</div>

				<div class="form-control mb-4">
					<label class="label" for="edit-customer-email">
						<span class="label-text">อีเมล</span>
					</label>
					<input 
						id="edit-customer-email"
						type="email" 
						name="email"
						class="input input-bordered" 
						value={selectedCustomer.email}
						required
					/>
				</div>

				<div class="form-control mb-4">
					<label class="label" for="edit-customer-phone">
						<span class="label-text">เบอร์โทร</span>
					</label>
					<input 
						id="edit-customer-phone"
						type="tel" 
						name="phone"
						class="input input-bordered" 
						value={selectedCustomer.phone || ''}
					/>
				</div>

				<div class="grid grid-cols-2 gap-4 mb-6">
					<div class="form-control">
						<label class="label" for="edit-customer-money">
							<span class="label-text">เงิน (Money Point)</span>
						</label>
						<input 
							id="edit-customer-money"
							type="number" 
							name="moneyPoint"
							class="input input-bordered" 
							value={selectedCustomer.moneyPoint}
							min="0"
						/>
					</div>
					<div class="form-control">
						<label class="label" for="edit-customer-gold">
							<span class="label-text">ทอง (Gold Point)</span>
						</label>
						<input 
							id="edit-customer-gold"
							type="number" 
							name="goldPoint"
							class="input input-bordered" 
							value={selectedCustomer.goldPoint}
							min="0"
						/>
					</div>
				</div>

				<div class="modal-action">
					<button 
						type="button" 
						class="btn btn-outline" 
						onclick={closeEditModal}
						disabled={isLoading}
					>
						ยกเลิก
					</button>
					<button 
						type="submit" 
						class="btn btn-primary" 
						disabled={isLoading}
					>
						{isLoading ? 'กำลังบันทึก...' : 'บันทึก'}
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}

<!-- Delete Customer Modal -->
{#if showDeleteModal && selectedCustomer}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">ยืนยันการลบลูกค้า</h3>
			<p class="mb-6">
				คุณต้องการลบลูกค้า <strong>{getFullName(selectedCustomer)}</strong> 
				({selectedCustomer.email}) หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้
			</p>
			
			<div class="modal-action">
				<button 
					class="btn btn-outline" 
					onclick={closeDeleteModal}
					disabled={isLoading}
				>
					ยกเลิก
				</button>
				<button 
					class="btn btn-error" 
					onclick={handleDeleteCustomer}
					disabled={isLoading}
				>
					{isLoading ? 'กำลังลบ...' : 'ลบลูกค้า'}
				</button>
			</div>
		</div>
	</div>
{/if} 