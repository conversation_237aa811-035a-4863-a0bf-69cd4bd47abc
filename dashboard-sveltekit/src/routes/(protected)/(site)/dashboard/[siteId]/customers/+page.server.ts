import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { customerService } from '$lib/services/customer';
import type { Customer } from '$lib/types';

export const load: PageServerLoad = async ({ locals, params, url }) => {
    // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
    const { siteId } = params;
    
    try {
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '10');
        const search = url.searchParams.get('search') || '';

        const response = await customerService.getCustomers(siteId, locals.token!, page, limit, search);

        if (!response.success) {
            throw error(500, response.error || 'Failed to load customers');
        }

        return {
            customers: response.data?.customers || [],
            pagination: response.data?.pagination || {
                page: 1,
                limit: 10,
                total: 0,
                totalPages: 0
            },
            search
        };
    } catch (err) {
        console.error('Error loading customers:', err);
        throw error(500, 'Failed to load customers');
    }
};

export const actions: Actions = {
    updateCustomer: async ({ request, locals, params }) => {
        const { siteId } = params;
        const formData = await request.formData();
        const customerId = formData.get('customerId') as string;
        
        const customerData = {
            firstName: formData.get('firstName') as string,
            lastName: formData.get('lastName') as string,
            email: formData.get('email') as string,
            phone: formData.get('phone') as string,
            moneyPoint: parseInt(formData.get('moneyPoint') as string || '0'),
            goldPoint: parseInt(formData.get('goldPoint') as string || '0')
        };

        const response = await customerService.updateCustomer(customerId, customerData, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    },

    deleteCustomer: async ({ request, locals, params }) => {
        const { siteId } = params;
        const formData = await request.formData();
        const customerId = formData.get('customerId') as string;

        const response = await customerService.deleteCustomer(customerId, locals.token!);

        return response.success ? response : fail(400, { error: response.error });
    }
}; 