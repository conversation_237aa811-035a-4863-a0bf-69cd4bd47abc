<script lang="ts">
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import { authStore } from "$lib/stores/auth.svelte";
    import Header from "$lib/components/layout/Header.svelte";
    import Sidebar from "$lib/components/layout/Sidebar.svelte";
    let { children, data } = $props();
    onMount(() => {
        // ตรวจสอบการ login
        if (!authStore.isAuthenticated) {
            goto("/signin");
        }
    });
</script>

{#if authStore.isAuthenticated}
    <div class="drawer lg:drawer-open">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />

        <!-- Main Content -->
        <div class="drawer-content flex flex-col">
            <Header {data} />
            <main class="flex-1 p-6 bg-base-100">
                {@render children()}
            </main>
        </div>

        <!-- Sidebar -->
        <Sidebar {data} />
    </div>
{:else}
    <div class="min-h-screen flex items-center justify-center">
        <div class="loading loading-spinner loading-lg"></div>
    </div>
{/if}
