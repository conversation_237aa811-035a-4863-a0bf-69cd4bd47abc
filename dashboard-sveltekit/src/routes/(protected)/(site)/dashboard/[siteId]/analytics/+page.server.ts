import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { productService } from '$lib/services/product';
import { orderService } from '$lib/services/order';
import { customerService } from '$lib/services/customer';
import { analyticsService } from '$lib/services/analytics';

export const load: PageServerLoad = async ({ params, locals, url, setHeaders }) => {
    try {
        const siteId = params.siteId;
        if (!siteId) {
            throw error(400, 'Site ID is required');
        }

        // Get query parameters for date range
        const period = url.searchParams.get('period') || 'month';
        const startDate = url.searchParams.get('startDate');
        const endDate = url.searchParams.get('endDate');

        // Fetch all analytics data in parallel with error handling
        let productStatsResponse, orderStatsResponse, customerStatsResponse, salesAnalyticsResponse;

        try {
            productStatsResponse = await productService.getProductStats(siteId, locals.token!);
        } catch (err) {
            console.error('Error fetching product stats:', err);
            productStatsResponse = {
                success: false,
                error: 'Failed to fetch product stats',
                data: {
                    totalProducts: 0,
                    activeProducts: 0,
                    outOfStockProducts: 0,
                    lowStockProducts: 0
                }
            };
        }

        try {
            orderStatsResponse = await orderService.getOrderStats(siteId, locals.token!, {
                period,
                startDate,
                endDate
            });
        } catch (err) {
            console.error('Error fetching order stats:', err);
            orderStatsResponse = {
                success: false,
                error: 'Failed to fetch order stats',
                data: {
                    today: 0,
                    month: 0,
                    total: 0,
                    todayChange: '+0%',
                    monthChange: '+0%'
                }
            };
        }

        try {
            customerStatsResponse = await customerService.getCustomerStats(siteId, locals.token!, {
                period,
                startDate,
                endDate
            });
        } catch (err) {
            console.error('Error fetching customer stats:', err);
            customerStatsResponse = {
                success: false,
                error: 'Failed to fetch customer stats',
                data: {
                    new: 0,
                    total: 0,
                    active: 0,
                    newChange: '+0%'
                }
            };
        }

        try {
            salesAnalyticsResponse = await analyticsService.getSalesAnalytics(siteId, locals.token!, {
                period,
                startDate,
                endDate
            });
        } catch (err) {
            console.error('Error fetching sales analytics:', err);
            salesAnalyticsResponse = {
                success: false,
                error: 'Failed to fetch sales analytics',
                data: {
                    today: 0,
                    month: 0,
                    total: 0,
                    todayChange: '+0%',
                    monthChange: '+0%',
                    chartData: [0, 0, 0, 0, 0, 0]
                }
            };
        }

        // Set cache headers
        setHeaders({
            'Cache-Control': 'public, max-age=300' // 5 minutes
        });

        return {
            analytics: {
                sales: salesAnalyticsResponse.success ? salesAnalyticsResponse.data : {
                    today: 0,
                    month: 0,
                    total: 0,
                    todayChange: '+0%',
                    monthChange: '+0%',
                    chartData: [0, 0, 0, 0, 0, 0]
                },
                products: productStatsResponse.success ? productStatsResponse.data : {
                    total: 0,
                    active: 0,
                    outOfStock: 0,
                    lowStock: 0
                },
                orders: orderStatsResponse.success ? orderStatsResponse.data : {
                    today: 0,
                    month: 0,
                    total: 0,
                    todayChange: '+0%',
                    monthChange: '+0%'
                },
                customers: customerStatsResponse.success ? customerStatsResponse.data : {
                    new: 0,
                    total: 0,
                    active: 0,
                    newChange: '+0%'
                },
                topProducts: salesAnalyticsResponse.success ? (salesAnalyticsResponse.data?.topProducts || []) : [],
                topCategories: salesAnalyticsResponse.success ? (salesAnalyticsResponse.data?.topCategories || []) : [],
                recentOrders: orderStatsResponse.success ? (orderStatsResponse.data?.recentOrders || []) : []
            },
            filters: {
                period,
                startDate,
                endDate
            },
            errors: {
                analytics: !salesAnalyticsResponse.success ? salesAnalyticsResponse.error : null,
                products: !productStatsResponse.success ? productStatsResponse.error : null,
                orders: !orderStatsResponse.success ? orderStatsResponse.error : null,
                customers: !customerStatsResponse.success ? customerStatsResponse.error : null
            }
        };

    } catch (err) {
        console.error('Error loading analytics page:', err);
        throw error(500, 'Failed to load analytics');
    }
};

export const actions: Actions = {
    exportReport: async ({ params, locals, request }) => {
        try {
            const siteId = params.siteId;
            if (!siteId) {
                return fail(400, {
                    success: false,
                    error: 'Site ID is required'
                });
            }

            const formData = await request.formData();
            const format = formData.get('format') as string || 'excel';
            const period = formData.get('period') as string || 'month';
            const startDate = formData.get('startDate')?.toString() || undefined;
            const endDate = formData.get('endDate')?.toString() || undefined;

            const config = {
                period,
                startDate,
                endDate,
                format
            };

            // ใช้ exportAnalytics แทน exportReport
            const result = await analyticsService.exportAnalytics(siteId, locals.token!, config);

            if (result.success) {
                return {
                    success: true,
                    message: 'ส่งออกรายงานสำเร็จ',
                    downloadUrl: result.data.downloadUrl
                };
            } else {
                return fail(500, {
                    success: false,
                    error: result.error || 'เกิดข้อผิดพลาดในการส่งออกรายงาน'
                });
            }
        } catch (err) {
            console.error('Error exporting report:', err);
            return fail(500, {
                success: false,
                error: 'เกิดข้อผิดพลาดในการส่งออกรายงาน'
            });
        }
    },

    refreshData: async ({ params, locals, request }) => {
        try {
            const siteId = params.siteId;
            if (!siteId) {
                return fail(400, {
                    success: false,
                    error: 'Site ID is required'
                });
            }

            const formData = await request.formData();
            const period = formData.get('period') as string || 'month';
            const startDate = formData.get('startDate') as string;
            const endDate = formData.get('endDate') as string;

            // รีเฟรชข้อมูลทั้งหมด
            const [productStats, orderStats, customerStats, salesAnalytics] = await Promise.all([
                productService.getProductStats(siteId, locals.token!),
                orderService.getOrderStats(siteId, locals.token!, { period, startDate, endDate }),
                customerService.getCustomerStats(siteId, locals.token!, { period, startDate, endDate }),
                analyticsService.getSalesAnalytics(siteId, locals.token!, { period, startDate, endDate })
            ]);

            return {
                success: true,
                message: 'รีเฟรชข้อมูลสำเร็จ',
                data: {
                    products: productStats.success ? productStats.data : null,
                    orders: orderStats.success ? orderStats.data : null,
                    customers: customerStats.success ? customerStats.data : null,
                    sales: salesAnalytics.success ? salesAnalytics.data : null
                }
            };
        } catch (err) {
            console.error('Error refreshing data:', err);
            return fail(500, {
                success: false,
                error: 'เกิดข้อผิดพลาดในการรีเฟรชข้อมูล'
            });
        }
    }
}; 