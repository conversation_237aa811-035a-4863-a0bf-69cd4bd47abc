<script lang="ts">
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import Icon from '@iconify/svelte';
	import { 
		showSuccessToast, 
		showErrorToast, 
		showConfirm
	} from '$lib/utils/sweetalert';
	import { Badge, Avatar } from '$lib/components/ui';

	export let data: any;
	export let form: any;

	interface TeamMember {
		_id: string;
		userId: string;
		userName: string;
		userEmail: string;
		role: 'owner' | 'admin' | 'editor' | 'viewer';
		joinedAt: string;
		lastActiveAt?: string;
	}

	interface Invitation {
		_id: string;
		toEmail?: string;
		toUserId?: string;
		toUserName?: string;
		role: 'owner' | 'admin' | 'editor' | 'viewer';
		status: 'pending' | 'accepted' | 'rejected' | 'expired';
		message?: string;
		createdAt: string;
		expiresAt: string;
	}

	let teamMembers: TeamMember[] = $state(data.teamMembers || []);
	let sentInvitations: Invitation[] = $state(data.sentInvitations || []);
	let inviteDialogOpen = $state(false);

	// ฟอร์มเชิญสมาชิกใหม่
	let inviteForm = $state({
		email: '',
		role: 'viewer' as 'owner' | 'admin' | 'editor' | 'viewer',
		message: ''
	});

	$: siteId = data.siteId;

	// Handle form result
	$: if (form?.success) {
		showSuccessToast(form.message || 'ดำเนินการสำเร็จ');
		inviteDialogOpen = false;
		inviteForm = { email: '', role: 'viewer', message: '' };
		invalidateAll();
	} else if (form?.error) {
		showErrorToast(form.error);
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม role
	function getRoleBadgeColor(role: string) {
		switch (role) {
			case 'owner': return 'error';
			case 'admin': return 'warning';
			case 'editor': return 'info';
			case 'viewer': return 'success';
			default: return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม role
	function getRoleIcon(role: string) {
		switch (role) {
			case 'owner': return 'mdi:crown';
			case 'admin': return 'mdi:shield-account';
			case 'editor': return 'mdi:pencil';
			case 'viewer': return 'mdi:eye';
			default: return 'mdi:account';
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม status
	function getStatusBadgeColor(status: string) {
		switch (status) {
			case 'pending': return 'warning';
			case 'accepted': return 'success';
			case 'rejected': return 'error';
			case 'expired': return 'neutral';
			default: return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม status
	function getStatusIcon(status: string) {
		switch (status) {
			case 'pending': return 'mdi:clock-outline';
			case 'accepted': return 'mdi:check-circle';
			case 'rejected': return 'mdi:close-circle';
			case 'expired': return 'mdi:timer-off';
			default: return 'mdi:clock-outline';
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	// ฟังก์ชันสำหรับยืนยันการลบ
	async function confirmRemoveMember(memberId: string, memberName: string) {
		const result = await showConfirm(
			'ยืนยันการลบสมาชิก',
			`คุณแน่ใจหรือไม่ที่จะลบ "${memberName}" ออกจากทีม?`
		);
		
		if (result.isConfirmed) {
			// Submit form to remove member
			const form = document.getElementById('remove-member-form') as HTMLFormElement;
			const memberIdInput = form.querySelector('input[name="memberId"]') as HTMLInputElement;
			memberIdInput.value = memberId;
			form.submit();
		}
	}

	// ฟังก์ชันสำหรับยืนยันการยกเลิกคำเชิญ
	async function confirmCancelInvitation(invitationId: string, email: string) {
		const result = await showConfirm(
			'ยืนยันการยกเลิกคำเชิญ',
			`คุณแน่ใจหรือไม่ที่จะยกเลิกคำเชิญสำหรับ "${email}"?`
		);

		if (result.isConfirmed) {
			// Submit form to cancel invitation
			const form = document.getElementById('cancel-invitation-form') as HTMLFormElement;
			const invitationIdInput = form.querySelector('input[name="invitationId"]') as HTMLInputElement;
			invitationIdInput.value = invitationId;
			form.submit();
		}
	}

	// ฟังก์ชันสำหรับส่งคำเชิญใหม่
	function resendInvitation(invitationId: string) {
		const form = document.getElementById('resend-invitation-form') as HTMLFormElement;
		const invitationIdInput = form.querySelector('input[name="invitationId"]') as HTMLInputElement;
		invitationIdInput.value = invitationId;
		form.submit();
	}
</script>

<svelte:head>
	<title>จัดการทีมงาน - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">จัดการทีมงาน</h1>
			<p class="text-base-content/60">เชิญและจัดการสมาชิกในทีมของคุณ</p>
		</div>
		
		<button 
			class="btn btn-primary gap-2"
			onclick={() => inviteDialogOpen = true}
		>
			<Icon icon="mdi:account-plus" class="w-4 h-4" />
			เชิญสมาชิกใหม่
		</button>
	</div>

	<!-- Stats -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
		<div class="stat bg-base-200 rounded-lg">
			<div class="stat-figure text-primary">
				<Icon icon="mdi:account-group" class="w-8 h-8" />
			</div>
			<div class="stat-title">สมาชิกทั้งหมด</div>
			<div class="stat-value text-primary">{teamMembers.length}</div>
		</div>
		
		<div class="stat bg-base-200 rounded-lg">
			<div class="stat-figure text-warning">
				<Icon icon="mdi:email-send" class="w-8 h-8" />
			</div>
			<div class="stat-title">คำเชิญที่ส่งไป</div>
			<div class="stat-value text-warning">{sentInvitations.length}</div>
		</div>
		
		<div class="stat bg-base-200 rounded-lg">
			<div class="stat-figure text-info">
				<Icon icon="mdi:clock-outline" class="w-8 h-8" />
			</div>
			<div class="stat-title">รอการตอบรับ</div>
			<div class="stat-value text-info">
				{sentInvitations.filter(inv => inv.status === 'pending').length}
			</div>
		</div>
	</div>

	<!-- Team Members -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title">
				<Icon icon="mdi:account-group" class="w-5 h-5" />
				สมาชิกทีม ({teamMembers.length})
			</h2>
			
			{#if teamMembers.length === 0}
				<div class="text-center py-8">
					<Icon icon="mdi:account-group-outline" class="w-16 h-16 mx-auto text-base-content/30 mb-4" />
					<p class="text-base-content/60">ยังไม่มีสมาชิกในทีม</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>สมาชิก</th>
								<th>บทบาท</th>
								<th>เข้าร่วมเมื่อ</th>
								<th>การดำเนินการ</th>
							</tr>
						</thead>
						<tbody>
							{#each teamMembers as member}
								<tr>
									<td>
										<div class="flex items-center gap-3">
											<Avatar size="sm" />
											<div>
												<div class="font-bold">{member.userName}</div>
												<div class="text-sm text-base-content/60">{member.userEmail}</div>
											</div>
										</div>
									</td>
									<td>
										<Badge 
											color={getRoleBadgeColor(member.role)}
											icon={getRoleIcon(member.role)}
											iconPosition="left"
										>
											{member.role}
										</Badge>
									</td>
									<td>{formatDate(member.joinedAt)}</td>
									<td>
										<div class="flex gap-2">
											{#if member.role !== 'owner'}
												<button 
													class="btn btn-sm btn-error btn-outline"
													onclick={() => confirmRemoveMember(member._id, member.userName)}
												>
													<Icon icon="mdi:delete" class="w-4 h-4" />
												</button>
											{/if}
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	</div>

	<!-- Sent Invitations -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title">
				<Icon icon="mdi:email-send" class="w-5 h-5" />
				คำเชิญที่ส่งไป ({sentInvitations.length})
			</h2>
			
			{#if sentInvitations.length === 0}
				<div class="text-center py-8">
					<Icon icon="mdi:email-send-outline" class="w-16 h-16 mx-auto text-base-content/30 mb-4" />
					<p class="text-base-content/60">ยังไม่มีคำเชิญที่ส่งไป</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>อีเมล</th>
								<th>บทบาท</th>
								<th>สถานะ</th>
								<th>ส่งเมื่อ</th>
								<th>การดำเนินการ</th>
							</tr>
						</thead>
						<tbody>
							{#each sentInvitations as invitation}
								<tr>
									<td>{invitation.toEmail}</td>
									<td>
										<Badge 
											color={getRoleBadgeColor(invitation.role)}
											icon={getRoleIcon(invitation.role)}
											iconPosition="left"
										>
											{invitation.role}
										</Badge>
									</td>
									<td>
										<Badge 
											color={getStatusBadgeColor(invitation.status)}
											icon={getStatusIcon(invitation.status)}
											iconPosition="left"
										>
											{invitation.status}
										</Badge>
									</td>
									<td>{formatDate(invitation.createdAt)}</td>
									<td>
										<div class="flex gap-2">
											{#if invitation.status === 'pending'}
												<button
													class="btn btn-sm btn-info btn-outline"
													onclick={() => resendInvitation(invitation._id)}
													title="ส่งคำเชิญใหม่"
												>
													<Icon icon="mdi:refresh" class="w-4 h-4" />
												</button>
												<button
													class="btn btn-sm btn-warning btn-outline"
													onclick={() => confirmCancelInvitation(invitation._id, invitation.toEmail || '')}
													title="ยกเลิกคำเชิญ"
												>
													<Icon icon="mdi:cancel" class="w-4 h-4" />
												</button>
											{/if}
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	</div>
</div>

<!-- Invite Modal -->
{#if inviteDialogOpen}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">
				<Icon icon="mdi:account-plus" class="w-5 h-5 inline mr-2" />
				เชิญสมาชิกใหม่
			</h3>

			<form method="POST" action="?/invite" use:enhance>
				<div class="space-y-4">
					<div class="form-control">
						<label class="label" for="email">
							<span class="label-text">อีเมล</span>
						</label>
						<input
							type="email"
							id="email"
							name="email"
							placeholder="<EMAIL>"
							class="input input-bordered w-full"
							bind:value={inviteForm.email}
							required
						/>
					</div>

					<div class="form-control">
						<label class="label" for="role">
							<span class="label-text">บทบาท</span>
						</label>
						<select
							id="role"
							name="role"
							class="select select-bordered w-full"
							bind:value={inviteForm.role}
						>
							<option value="viewer">Viewer - ดูข้อมูลได้อย่างเดียว</option>
							<option value="editor">Editor - แก้ไขเนื้อหาได้</option>
							<option value="admin">Admin - จัดการเว็บไซต์ได้</option>
							<option value="owner">Owner - สิทธิ์เต็ม</option>
						</select>
					</div>

					<div class="form-control">
						<label class="label" for="message">
							<span class="label-text">ข้อความ (ไม่บังคับ)</span>
						</label>
						<textarea
							id="message"
							name="message"
							placeholder="ข้อความเชิญ..."
							class="textarea textarea-bordered w-full"
							bind:value={inviteForm.message}
						></textarea>
					</div>
				</div>

				<div class="modal-action">
					<button
						type="button"
						class="btn btn-ghost"
						onclick={() => inviteDialogOpen = false}
					>
						ยกเลิก
					</button>
					<button type="submit" class="btn btn-primary">
						<Icon icon="mdi:send" class="w-4 h-4 mr-2" />
						ส่งคำเชิญ
					</button>
				</div>
			</form>
		</div>
		<div class="modal-backdrop" onclick={() => inviteDialogOpen = false}></div>
	</div>
{/if}

<!-- Hidden Forms for Server Actions -->
<form id="remove-member-form" method="POST" action="?/removeMember" use:enhance style="display: none;">
	<input type="hidden" name="memberId" />
</form>

<form id="cancel-invitation-form" method="POST" action="?/cancelInvitation" use:enhance style="display: none;">
	<input type="hidden" name="invitationId" />
</form>

<form id="resend-invitation-form" method="POST" action="?/resendInvitation" use:enhance style="display: none;">
	<input type="hidden" name="invitationId" />
</form>
