<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { apiClient } from '$lib/api/client';
	import { toast } from 'svelte-sonner';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '$lib/components/ui/dialog';
	import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '$lib/components/ui/table';
	import { 
		Plus, Edit, Trash2, Search, Package, 
		MoreHorizontal, Eye, EyeOff, Image
	} from 'lucide-svelte';

	interface Brand {
		_id: string;
		name: string;
		description?: string;
		logoUrl?: string;
		websiteUrl?: string;
		isActive: boolean;
		productCount: number;
		createdAt: string;
		updatedAt: string;
	}

	let brands: Brand[] = $state([]);
	let loading = $state(true);
	let searchQuery = $state('');
	let createDialogOpen = $state(false);
	let editDialogOpen = $state(false);
	let processing = $state(false);
	let selectedBrand: Brand | null = $state(null);

	// ฟอร์มสร้าง/แก้ไขแบรนด์
	let brandForm = $state({
		name: '',
		description: '',
		logoUrl: '',
		websiteUrl: '',
		isActive: true
	});

	$: siteId = $page.params.siteId;
	$: filteredBrands = brands.filter(brand => 
		brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
		(brand.description && brand.description.toLowerCase().includes(searchQuery.toLowerCase()))
	);

	// ดึงข้อมูลแบรนด์
	async function fetchBrands() {
		try {
			loading = true;
			const response = await apiClient.get(`/brand/${siteId}`);
			
			if (response.success) {
				brands = response.data.brands || [];
			} else {
				toast.error(response.message || 'ไม่สามารถดึงข้อมูลแบรนด์ได้');
			}
		} catch (error: any) {
			console.error('Error fetching brands:', error);
			toast.error('เกิดข้อผิดพลาดในการดึงข้อมูลแบรนด์');
		} finally {
			loading = false;
		}
	}

	// สร้างแบรนด์ใหม่
	async function createBrand() {
		if (!brandForm.name.trim()) {
			toast.error('กรุณากรอกชื่อแบรนด์');
			return;
		}

		try {
			processing = true;
			const response = await apiClient.post(`/brand/${siteId}`, brandForm);
			
			if (response.success) {
				toast.success('สร้างแบรนด์เรียบร้อยแล้ว');
				createDialogOpen = false;
				resetForm();
				await fetchBrands();
			} else {
				toast.error(response.message || 'ไม่สามารถสร้างแบรนด์ได้');
			}
		} catch (error: any) {
			console.error('Error creating brand:', error);
			toast.error('เกิดข้อผิดพลาดในการสร้างแบรนด์');
		} finally {
			processing = false;
		}
	}

	// แก้ไขแบรนด์
	async function updateBrand() {
		if (!brandForm.name.trim() || !selectedBrand) {
			toast.error('กรุณากรอกข้อมูลให้ครบถ้วน');
			return;
		}

		try {
			processing = true;
			const response = await apiClient.put(`/brand/${siteId}/${selectedBrand._id}`, brandForm);
			
			if (response.success) {
				toast.success('อัปเดตแบรนด์เรียบร้อยแล้ว');
				editDialogOpen = false;
				resetForm();
				await fetchBrands();
			} else {
				toast.error(response.message || 'ไม่สามารถอัปเดตแบรนด์ได้');
			}
		} catch (error: any) {
			console.error('Error updating brand:', error);
			toast.error('เกิดข้อผิดพลาดในการอัปเดตแบรนด์');
		} finally {
			processing = false;
		}
	}

	// ลบแบรนด์
	async function deleteBrand(brand: Brand) {
		if (brand.productCount > 0) {
			toast.error('ไม่สามารถลบแบรนด์ที่มีสินค้าอยู่ได้');
			return;
		}

		if (!confirm(`คุณแน่ใจหรือไม่ที่จะลบแบรนด์ "${brand.name}"?`)) return;

		try {
			const response = await apiClient.delete(`/brand/${siteId}/${brand._id}`);
			
			if (response.success) {
				toast.success('ลบแบรนด์เรียบร้อยแล้ว');
				await fetchBrands();
			} else {
				toast.error(response.message || 'ไม่สามารถลบแบรนด์ได้');
			}
		} catch (error: any) {
			console.error('Error deleting brand:', error);
			toast.error('เกิดข้อผิดพลาดในการลบแบรนด์');
		}
	}

	// เปิด/ปิดการใช้งานแบรนด์
	async function toggleBrandStatus(brand: Brand) {
		try {
			const response = await apiClient.put(`/brand/${siteId}/${brand._id}`, {
				...brand,
				isActive: !brand.isActive
			});
			
			if (response.success) {
				toast.success(`${!brand.isActive ? 'เปิด' : 'ปิด'}การใช้งานแบรนด์เรียบร้อยแล้ว`);
				await fetchBrands();
			} else {
				toast.error(response.message || 'ไม่สามารถเปลี่ยนสถานะแบรนด์ได้');
			}
		} catch (error: any) {
			console.error('Error toggling brand status:', error);
			toast.error('เกิดข้อผิดพลาดในการเปลี่ยนสถานะแบรนด์');
		}
	}

	// เปิดฟอร์มแก้ไข
	function openEditDialog(brand: Brand) {
		selectedBrand = brand;
		brandForm = {
			name: brand.name,
			description: brand.description || '',
			logoUrl: brand.logoUrl || '',
			websiteUrl: brand.websiteUrl || '',
			isActive: brand.isActive
		};
		editDialogOpen = true;
	}

	// รีเซ็ตฟอร์ม
	function resetForm() {
		brandForm = {
			name: '',
			description: '',
			logoUrl: '',
			websiteUrl: '',
			isActive: true
		};
		selectedBrand = null;
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	onMount(() => {
		fetchBrands();
	});
</script>

<svelte:head>
	<title>จัดการแบรนด์ - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการแบรนด์</h1>
			<p class="text-muted-foreground">จัดการแบรนด์สินค้าในเว็บไซต์ของคุณ</p>
		</div>
		
		<Dialog bind:open={createDialogOpen} onOpenChange={() => resetForm()}>
			<DialogTrigger asChild>
				<Button>
					<Plus class="h-4 w-4 mr-2" />
					เพิ่มแบรนด์
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>เพิ่มแบรนด์ใหม่</DialogTitle>
					<DialogDescription>
						สร้างแบรนด์ใหม่สำหรับสินค้าของคุณ
					</DialogDescription>
				</DialogHeader>
				
				<div class="space-y-4">
					<div class="space-y-2">
						<Label for="name">ชื่อแบรนด์ *</Label>
						<Input 
							id="name"
							placeholder="ชื่อแบรนด์"
							bind:value={brandForm.name}
						/>
					</div>
					
					<div class="space-y-2">
						<Label for="description">คำอธิบาย</Label>
						<Textarea 
							id="description"
							placeholder="คำอธิบายเกี่ยวกับแบรนด์"
							bind:value={brandForm.description}
						/>
					</div>
					
					<div class="space-y-2">
						<Label for="logoUrl">URL โลโก้</Label>
						<Input 
							id="logoUrl"
							type="url"
							placeholder="https://example.com/logo.png"
							bind:value={brandForm.logoUrl}
						/>
					</div>
					
					<div class="space-y-2">
						<Label for="websiteUrl">เว็บไซต์แบรนด์</Label>
						<Input 
							id="websiteUrl"
							type="url"
							placeholder="https://brand-website.com"
							bind:value={brandForm.websiteUrl}
						/>
					</div>
					
					<div class="flex gap-2">
						<Button 
							onclick={createBrand}
							disabled={processing}
							class="flex-1"
						>
							{#if processing}
								<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							{:else}
								<Plus class="h-4 w-4 mr-2" />
							{/if}
							สร้างแบรนด์
						</Button>
						<Button variant="outline" onclick={() => createDialogOpen = false}>
							ยกเลิก
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	</div>

	<!-- ค้นหา -->
	<Card>
		<CardContent class="p-4">
			<div class="relative">
				<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
				<Input 
					placeholder="ค้นหาแบรนด์..."
					bind:value={searchQuery}
					class="pl-10"
				/>
			</div>
		</CardContent>
	</Card>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Package class="h-5 w-5" />
					แบรนด์ทั้งหมด ({filteredBrands.length})
				</CardTitle>
			</CardHeader>
			<CardContent>
				{#if filteredBrands.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery ? 'ไม่พบแบรนด์ที่ค้นหา' : 'ยังไม่มีแบรนด์'}
					</div>
				{:else}
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>แบรนด์</TableHead>
								<TableHead>คำอธิบาย</TableHead>
								<TableHead>สินค้า</TableHead>
								<TableHead>สถานะ</TableHead>
								<TableHead>วันที่สร้าง</TableHead>
								<TableHead class="text-right">การจัดการ</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each filteredBrands as brand (brand._id)}
								<TableRow>
									<TableCell>
										<div class="flex items-center gap-3">
											{#if brand.logoUrl}
												<img 
													src={brand.logoUrl} 
													alt={brand.name}
													class="h-8 w-8 rounded object-cover"
													onerror="this.style.display='none'"
												/>
											{:else}
												<div class="h-8 w-8 rounded bg-muted flex items-center justify-center">
													<Image class="h-4 w-4 text-muted-foreground" />
												</div>
											{/if}
											<div>
												<p class="font-medium">{brand.name}</p>
												{#if brand.websiteUrl}
													<a 
														href={brand.websiteUrl} 
														target="_blank" 
														class="text-xs text-blue-600 hover:underline"
													>
														{brand.websiteUrl}
													</a>
												{/if}
											</div>
										</div>
									</TableCell>
									<TableCell>
										<p class="text-sm text-muted-foreground max-w-xs truncate">
											{brand.description || '-'}
										</p>
									</TableCell>
									<TableCell>
										<Badge variant="outline">
											{brand.productCount} สินค้า
										</Badge>
									</TableCell>
									<TableCell>
										<Badge variant={brand.isActive ? 'default' : 'secondary'}>
											{brand.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน'}
										</Badge>
									</TableCell>
									<TableCell class="text-sm text-muted-foreground">
										{formatDate(brand.createdAt)}
									</TableCell>
									<TableCell class="text-right">
										<div class="flex items-center justify-end gap-2">
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => toggleBrandStatus(brand)}
											>
												{#if brand.isActive}
													<EyeOff class="h-4 w-4" />
												{:else}
													<Eye class="h-4 w-4" />
												{/if}
											</Button>
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => openEditDialog(brand)}
											>
												<Edit class="h-4 w-4" />
											</Button>
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => deleteBrand(brand)}
												disabled={brand.productCount > 0}
											>
												<Trash2 class="h-4 w-4" />
											</Button>
										</div>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				{/if}
			</CardContent>
		</Card>
	{/if}
</div>

<!-- Dialog แก้ไขแบรนด์ -->
<Dialog bind:open={editDialogOpen} onOpenChange={() => resetForm()}>
	<DialogContent>
		<DialogHeader>
			<DialogTitle>แก้ไขแบรนด์</DialogTitle>
			<DialogDescription>
				แก้ไขข้อมูลแบรนด์ {selectedBrand?.name}
			</DialogDescription>
		</DialogHeader>
		
		<div class="space-y-4">
			<div class="space-y-2">
				<Label for="edit-name">ชื่อแบรนด์ *</Label>
				<Input 
					id="edit-name"
					placeholder="ชื่อแบรนด์"
					bind:value={brandForm.name}
				/>
			</div>
			
			<div class="space-y-2">
				<Label for="edit-description">คำอธิบาย</Label>
				<Textarea 
					id="edit-description"
					placeholder="คำอธิบายเกี่ยวกับแบรนด์"
					bind:value={brandForm.description}
				/>
			</div>
			
			<div class="space-y-2">
				<Label for="edit-logoUrl">URL โลโก้</Label>
				<Input 
					id="edit-logoUrl"
					type="url"
					placeholder="https://example.com/logo.png"
					bind:value={brandForm.logoUrl}
				/>
			</div>
			
			<div class="space-y-2">
				<Label for="edit-websiteUrl">เว็บไซต์แบรนด์</Label>
				<Input 
					id="edit-websiteUrl"
					type="url"
					placeholder="https://brand-website.com"
					bind:value={brandForm.websiteUrl}
				/>
			</div>
			
			<div class="flex gap-2">
				<Button 
					onclick={updateBrand}
					disabled={processing}
					class="flex-1"
				>
					{#if processing}
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
					{:else}
						<Edit class="h-4 w-4 mr-2" />
					{/if}
					อัปเดตแบรนด์
				</Button>
				<Button variant="outline" onclick={() => editDialogOpen = false}>
					ยกเลิก
				</Button>
			</div>
		</div>
	</DialogContent>
</Dialog>
