<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { apiClient } from '$lib/api/client';
	import { toast } from 'svelte-sonner';
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '$lib/components/ui/select';
	import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '$lib/components/ui/dialog';
	import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '$lib/components/ui/table';
	import { 
		Star, Search, MessageSquare, Eye, EyeOff, 
		Trash2, Filter, User, Package, Calendar
	} from 'lucide-svelte';

	interface Review {
		_id: string;
		productId: string;
		productName: string;
		customerId: string;
		customerName: string;
		customerEmail: string;
		rating: number;
		title?: string;
		comment: string;
		isApproved: boolean;
		isVisible: boolean;
		images?: string[];
		createdAt: string;
		updatedAt: string;
	}

	let reviews: Review[] = $state([]);
	let loading = $state(true);
	let searchQuery = $state('');
	let statusFilter = $state('all');
	let ratingFilter = $state('all');
	let selectedReview: Review | null = $state(null);
	let viewDialogOpen = $state(false);

	$: siteId = $page.params.siteId;
	$: filteredReviews = reviews.filter(review => {
		const matchesSearch = 
			review.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
			review.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
			review.comment.toLowerCase().includes(searchQuery.toLowerCase()) ||
			(review.title && review.title.toLowerCase().includes(searchQuery.toLowerCase()));

		const matchesStatus = 
			statusFilter === 'all' ||
			(statusFilter === 'approved' && review.isApproved) ||
			(statusFilter === 'pending' && !review.isApproved) ||
			(statusFilter === 'visible' && review.isVisible) ||
			(statusFilter === 'hidden' && !review.isVisible);

		const matchesRating = 
			ratingFilter === 'all' ||
			review.rating.toString() === ratingFilter;

		return matchesSearch && matchesStatus && matchesRating;
	});

	// ดึงข้อมูลรีวิว
	async function fetchReviews() {
		try {
			loading = true;
			const response = await apiClient.get(`/review/${siteId}`);
			
			if (response.success) {
				reviews = response.data.reviews || [];
			} else {
				toast.error(response.message || 'ไม่สามารถดึงข้อมูลรีวิวได้');
			}
		} catch (error: any) {
			console.error('Error fetching reviews:', error);
			toast.error('เกิดข้อผิดพลาดในการดึงข้อมูลรีวิว');
		} finally {
			loading = false;
		}
	}

	// อนุมัติรีวิว
	async function approveReview(reviewId: string) {
		try {
			const response = await apiClient.put(`/review/${siteId}/${reviewId}/approve`);
			
			if (response.success) {
				toast.success('อนุมัติรีวิวเรียบร้อยแล้ว');
				await fetchReviews();
			} else {
				toast.error(response.message || 'ไม่สามารถอนุมัติรีวิวได้');
			}
		} catch (error: any) {
			console.error('Error approving review:', error);
			toast.error('เกิดข้อผิดพลาดในการอนุมัติรีวิว');
		}
	}

	// ปฏิเสธรีวิว
	async function rejectReview(reviewId: string) {
		try {
			const response = await apiClient.put(`/review/${siteId}/${reviewId}/reject`);
			
			if (response.success) {
				toast.success('ปฏิเสธรีวิวเรียบร้อยแล้ว');
				await fetchReviews();
			} else {
				toast.error(response.message || 'ไม่สามารถปฏิเสธรีวิวได้');
			}
		} catch (error: any) {
			console.error('Error rejecting review:', error);
			toast.error('เกิดข้อผิดพลาดในการปฏิเสธรีวิว');
		}
	}

	// เปิด/ปิดการแสดงรีวิว
	async function toggleReviewVisibility(reviewId: string, isVisible: boolean) {
		try {
			const response = await apiClient.put(`/review/${siteId}/${reviewId}/visibility`, {
				isVisible: !isVisible
			});
			
			if (response.success) {
				toast.success(`${!isVisible ? 'แสดง' : 'ซ่อน'}รีวิวเรียบร้อยแล้ว`);
				await fetchReviews();
			} else {
				toast.error(response.message || 'ไม่สามารถเปลี่ยนการแสดงรีวิวได้');
			}
		} catch (error: any) {
			console.error('Error toggling review visibility:', error);
			toast.error('เกิดข้อผิดพลาดในการเปลี่ยนการแสดงรีวิว');
		}
	}

	// ลบรีวิว
	async function deleteReview(reviewId: string) {
		if (!confirm('คุณแน่ใจหรือไม่ที่จะลบรีวิวนี้?')) return;

		try {
			const response = await apiClient.delete(`/review/${siteId}/${reviewId}`);
			
			if (response.success) {
				toast.success('ลบรีวิวเรียบร้อยแล้ว');
				await fetchReviews();
			} else {
				toast.error(response.message || 'ไม่สามารถลบรีวิวได้');
			}
		} catch (error: any) {
			console.error('Error deleting review:', error);
			toast.error('เกิดข้อผิดพลาดในการลบรีวิว');
		}
	}

	// เปิดดูรายละเอียดรีวิว
	function viewReview(review: Review) {
		selectedReview = review;
		viewDialogOpen = true;
	}

	// สร้างดาวสำหรับแสดงคะแนน
	function renderStars(rating: number) {
		return Array.from({ length: 5 }, (_, i) => i < rating);
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// คำนวณสถิติรีวิว
	$: reviewStats = {
		total: reviews.length,
		approved: reviews.filter(r => r.isApproved).length,
		pending: reviews.filter(r => !r.isApproved).length,
		averageRating: reviews.length > 0 
			? (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1)
			: '0.0'
	};

	onMount(() => {
		fetchReviews();
	});
</script>

<svelte:head>
	<title>จัดการรีวิวสินค้า - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการรีวิวสินค้า</h1>
			<p class="text-muted-foreground">จัดการและตรวจสอบรีวิวจากลูกค้า</p>
		</div>
	</div>

	<!-- สถิติรีวิว -->
	<div class="grid gap-4 md:grid-cols-4">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">รีวิวทั้งหมด</CardTitle>
				<MessageSquare class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{reviewStats.total}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">อนุมัติแล้ว</CardTitle>
				<Eye class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-green-600">{reviewStats.approved}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">รอการอนุมัติ</CardTitle>
				<EyeOff class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-orange-600">{reviewStats.pending}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">คะแนนเฉลี่ย</CardTitle>
				<Star class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{reviewStats.averageRating}</div>
			</CardContent>
		</Card>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card>
		<CardContent class="p-4">
			<div class="flex flex-col md:flex-row gap-4">
				<div class="relative flex-1">
					<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<Input 
						placeholder="ค้นหารีวิว..."
						bind:value={searchQuery}
						class="pl-10"
					/>
				</div>
				<Select bind:value={statusFilter}>
					<SelectTrigger class="w-full md:w-48">
						<SelectValue placeholder="สถานะ" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">ทั้งหมด</SelectItem>
						<SelectItem value="approved">อนุมัติแล้ว</SelectItem>
						<SelectItem value="pending">รอการอนุมัติ</SelectItem>
						<SelectItem value="visible">แสดงอยู่</SelectItem>
						<SelectItem value="hidden">ซ่อนอยู่</SelectItem>
					</SelectContent>
				</Select>
				<Select bind:value={ratingFilter}>
					<SelectTrigger class="w-full md:w-32">
						<SelectValue placeholder="คะแนน" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">ทั้งหมด</SelectItem>
						<SelectItem value="5">5 ดาว</SelectItem>
						<SelectItem value="4">4 ดาว</SelectItem>
						<SelectItem value="3">3 ดาว</SelectItem>
						<SelectItem value="2">2 ดาว</SelectItem>
						<SelectItem value="1">1 ดาว</SelectItem>
					</SelectContent>
				</Select>
			</div>
		</CardContent>
	</Card>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<MessageSquare class="h-5 w-5" />
					รีวิวทั้งหมด ({filteredReviews.length})
				</CardTitle>
			</CardHeader>
			<CardContent>
				{#if filteredReviews.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || statusFilter !== 'all' || ratingFilter !== 'all' 
							? 'ไม่พบรีวิวที่ตรงกับเงื่อนไข' 
							: 'ยังไม่มีรีวิว'}
					</div>
				{:else}
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>สินค้า</TableHead>
								<TableHead>ลูกค้า</TableHead>
								<TableHead>คะแนน</TableHead>
								<TableHead>รีวิว</TableHead>
								<TableHead>สถานะ</TableHead>
								<TableHead>วันที่</TableHead>
								<TableHead class="text-right">การจัดการ</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each filteredReviews as review (review._id)}
								<TableRow>
									<TableCell>
										<div class="flex items-center gap-2">
											<Package class="h-4 w-4 text-muted-foreground" />
											<span class="font-medium">{review.productName}</span>
										</div>
									</TableCell>
									<TableCell>
										<div class="flex items-center gap-2">
											<User class="h-4 w-4 text-muted-foreground" />
											<div>
												<p class="font-medium">{review.customerName}</p>
												<p class="text-xs text-muted-foreground">{review.customerEmail}</p>
											</div>
										</div>
									</TableCell>
									<TableCell>
										<div class="flex items-center gap-1">
											{#each renderStars(review.rating) as filled}
												<Star 
													class="h-4 w-4 {filled ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}" 
												/>
											{/each}
											<span class="ml-1 text-sm text-muted-foreground">
												({review.rating})
											</span>
										</div>
									</TableCell>
									<TableCell>
										<div class="max-w-xs">
											{#if review.title}
												<p class="font-medium text-sm truncate">{review.title}</p>
											{/if}
											<p class="text-sm text-muted-foreground truncate">
												{review.comment}
											</p>
										</div>
									</TableCell>
									<TableCell>
										<div class="flex flex-col gap-1">
											<Badge variant={review.isApproved ? 'default' : 'secondary'}>
												{review.isApproved ? 'อนุมัติแล้ว' : 'รอการอนุมัติ'}
											</Badge>
											<Badge variant={review.isVisible ? 'outline' : 'destructive'}>
												{review.isVisible ? 'แสดงอยู่' : 'ซ่อนอยู่'}
											</Badge>
										</div>
									</TableCell>
									<TableCell class="text-sm text-muted-foreground">
										{formatDate(review.createdAt)}
									</TableCell>
									<TableCell class="text-right">
										<div class="flex items-center justify-end gap-2">
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => viewReview(review)}
											>
												<Eye class="h-4 w-4" />
											</Button>
											{#if !review.isApproved}
												<Button 
													variant="ghost" 
													size="sm"
													onclick={() => approveReview(review._id)}
												>
													<Eye class="h-4 w-4 text-green-600" />
												</Button>
											{:else}
												<Button 
													variant="ghost" 
													size="sm"
													onclick={() => rejectReview(review._id)}
												>
													<EyeOff class="h-4 w-4 text-orange-600" />
												</Button>
											{/if}
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => toggleReviewVisibility(review._id, review.isVisible)}
											>
												{#if review.isVisible}
													<EyeOff class="h-4 w-4" />
												{:else}
													<Eye class="h-4 w-4" />
												{/if}
											</Button>
											<Button 
												variant="ghost" 
												size="sm"
												onclick={() => deleteReview(review._id)}
											>
												<Trash2 class="h-4 w-4" />
											</Button>
										</div>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				{/if}
			</CardContent>
		</Card>
	{/if}
</div>

<!-- Dialog ดูรายละเอียดรีวิว -->
<Dialog bind:open={viewDialogOpen}>
	<DialogContent class="max-w-2xl">
		{#if selectedReview}
			<DialogHeader>
				<DialogTitle>รายละเอียดรีวิว</DialogTitle>
				<DialogDescription>
					รีวิวสำหรับสินค้า {selectedReview.productName}
				</DialogDescription>
			</DialogHeader>
			
			<div class="space-y-4">
				<div class="grid grid-cols-2 gap-4">
					<div>
						<p class="text-sm font-medium">ลูกค้า</p>
						<p class="text-sm text-muted-foreground">{selectedReview.customerName}</p>
						<p class="text-xs text-muted-foreground">{selectedReview.customerEmail}</p>
					</div>
					<div>
						<p class="text-sm font-medium">คะแนน</p>
						<div class="flex items-center gap-1">
							{#each renderStars(selectedReview.rating) as filled}
								<Star 
									class="h-4 w-4 {filled ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}" 
								/>
							{/each}
							<span class="ml-1 text-sm">({selectedReview.rating}/5)</span>
						</div>
					</div>
				</div>
				
				{#if selectedReview.title}
					<div>
						<p class="text-sm font-medium">หัวข้อ</p>
						<p class="text-sm">{selectedReview.title}</p>
					</div>
				{/if}
				
				<div>
					<p class="text-sm font-medium">ความคิดเห็น</p>
					<p class="text-sm whitespace-pre-wrap">{selectedReview.comment}</p>
				</div>
				
				{#if selectedReview.images && selectedReview.images.length > 0}
					<div>
						<p class="text-sm font-medium mb-2">รูปภาพ</p>
						<div class="grid grid-cols-3 gap-2">
							{#each selectedReview.images as image}
								<img 
									src={image} 
									alt="รีวิวรูปภาพ"
									class="w-full h-20 object-cover rounded border"
								/>
							{/each}
						</div>
					</div>
				{/if}
				
				<div class="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
					<div>
						<p>วันที่สร้าง: {formatDate(selectedReview.createdAt)}</p>
					</div>
					<div>
						<p>อัปเดตล่าสุด: {formatDate(selectedReview.updatedAt)}</p>
					</div>
				</div>
				
				<div class="flex gap-2">
					{#if !selectedReview.isApproved}
						<Button 
							onclick={() => {
								approveReview(selectedReview._id);
								viewDialogOpen = false;
							}}
							class="flex-1"
						>
							อนุมัติรีวิว
						</Button>
					{:else}
						<Button 
							variant="outline"
							onclick={() => {
								rejectReview(selectedReview._id);
								viewDialogOpen = false;
							}}
							class="flex-1"
						>
							ยกเลิกการอนุมัติ
						</Button>
					{/if}
					<Button 
						variant="outline"
						onclick={() => {
							toggleReviewVisibility(selectedReview._id, selectedReview.isVisible);
							viewDialogOpen = false;
						}}
						class="flex-1"
					>
						{selectedReview.isVisible ? 'ซ่อนรีวิว' : 'แสดงรีวิว'}
					</Button>
				</div>
			</div>
		{/if}
	</DialogContent>
</Dialog>
