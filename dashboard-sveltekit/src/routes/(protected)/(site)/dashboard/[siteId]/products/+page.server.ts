import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { productService } from '$lib/services/product';
import { categoryService } from '$lib/services/category';

export const load: PageServerLoad = async ({ params, locals, url, setHeaders }) => {
    try {
        const siteId = params.siteId;
        if (!siteId) {
            throw error(400, 'Site ID is required');
        }

        // Get query parameters
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '10');
        const search = url.searchParams.get('search') || '';
        const categoryId = url.searchParams.get('categoryId') || '';
        const status = url.searchParams.get('status') as 'active' | 'inactive' | undefined;
        const sortBy = url.searchParams.get('sortBy') || 'createdAt';
        const sortOrder = url.searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';

        // Fetch products and categories in parallel with error handling
        let productsResponse, categoriesResponse;

        try {
            productsResponse = await productService.getProducts(siteId, locals.token!, {
                page,
                limit,
                search,
                categoryId,
                status,
                sortBy,
                sortOrder
            });
        } catch (err) {
            console.error('Error fetching products:', err);
            productsResponse = {
                success: false,
                error: 'Failed to fetch products',
                data: { products: [], total: 0, page: 1, limit: 10, totalPages: 0 }
            };
        }

        try {
            categoriesResponse = await categoryService.getCategories(siteId, locals.token!);
        } catch (err) {
            console.error('Error fetching categories:', err);
            categoriesResponse = {
                success: false,
                error: 'Failed to fetch categories',
                data: []
            };
        }

        // Set cache headers
        setHeaders({
            'Cache-Control': 'public, max-age=300' // 5 minutes
        });

        return {
            products: productsResponse.success ? productsResponse.data : {
                products: [],
                total: 0,
                page: 1,
                limit: 10,
                totalPages: 0
            },
            categories: categoriesResponse.success ? categoriesResponse.data : [],
            pagination: {
                page: productsResponse.success ? (productsResponse.data?.page || 1) : 1,
                limit: productsResponse.success ? (productsResponse.data?.limit || 10) : 10,
                total: productsResponse.success ? (productsResponse.data?.total || 0) : 0,
                totalPages: productsResponse.success ? (productsResponse.data?.totalPages || 0) : 0
            },
            filters: {
                search,
                categoryId,
                status,
                sortBy,
                sortOrder
            },
            errors: {
                products: !productsResponse.success ? productsResponse.error : null,
                categories: !categoriesResponse.success ? categoriesResponse.error : null
            }
        };

    } catch (err) {
        console.error('Error loading products page:', err);
        throw error(500, 'Failed to load products');
    }
};

export const actions: Actions = {
    deleteProduct: async ({ request, params, locals }) => {
        try {
            if (!locals.token || !locals.user) {
                return { success: false, error: 'กรุณาเข้าสู่ระบบ' };
            }

            const siteId = params.siteId;
            if (!siteId) {
                return { success: false, error: 'Site ID is required' };
            }

            const formData = await request.formData();
            const productId = formData.get('productId') as string;

            if (!productId) {
                return { success: false, error: 'Product ID is required' };
            }

            const response = await productService.deleteProduct(productId, siteId, locals.token);

            if (response.success) {
                return { success: true, message: 'ลบสินค้าเรียบร้อย' };
            } else {
                return { success: false, error: response.error || 'เกิดข้อผิดพลาดในการลบสินค้า' };
            }
        } catch (err) {
            console.error('Error deleting product:', err);
            return { success: false, error: 'เกิดข้อผิดพลาดในการลบสินค้า' };
        }
    },

    updateStock: async ({ request, params, locals }) => {
        try {
            if (!locals.token || !locals.user) {
                return { success: false, error: 'กรุณาเข้าสู่ระบบ' };
            }

            const siteId = params.siteId;
            if (!siteId) {
                return { success: false, error: 'Site ID is required' };
            }

            const formData = await request.formData();
            const productId = formData.get('productId') as string;
            const stock = parseInt(formData.get('stock') as string);

            if (!productId || isNaN(stock)) {
                return { success: false, error: 'Product ID and stock are required' };
            }

            const response = await productService.updateProductStock(productId, { stock }, siteId, locals.token);

            if (response.success) {
                return { success: true, message: 'อัปเดตสต็อกเรียบร้อย' };
            } else {
                return { success: false, error: response.error || 'เกิดข้อผิดพลาดในการอัปเดตสต็อก' };
            }
        } catch (err) {
            console.error('Error updating stock:', err);
            return { success: false, error: 'เกิดข้อผิดพลาดในการอัปเดตสต็อก' };
        }
    }
}; 