# หน้าสร้างสินค้าใหม่ (Create Product Page)

หน้านี้ใช้สำหรับสร้างสินค้าใหม่ในระบบ

## ฟีเจอร์

### 1. ข้อมูลพื้นฐาน
- **ชื่อสินค้า**: ชื่อสินค้าที่จะแสดง
- **คำอธิบาย**: รายละเอียดสินค้า
- **ราคา**: ราคาขาย
- **ราคาเปรียบเทียบ**: ราคาเดิม (สำหรับแสดงส่วนลด)
- **ต้นทุน**: ต้นทุนสินค้า
- **SKU**: รหัสสินค้า
- **บาร์โค้ด**: บาร์โค้ดสินค้า
- **สต็อก**: จำนวนสินค้าในคลัง
- **สต็อกต่ำเตือน**: จำนวนที่เตือนเมื่อสต็อกต่ำ
- **หมวดหมู่**: หมวดหมู่สินค้า

### 2. ข้อมูลเพิ่มเติม
- **แท็ก**: คำค้นหาสำหรับสินค้า
- **น้ำหนัก**: น้ำหนักสินค้า (กรัม)
- **ขนาด**: ความยาว, ความกว้าง, ความสูง (ซม.)
- **สถานะ**: เปิดขาย/ปิดขาย
- **ประเภท**: สินค้าทั่วไป/สินค้าดิจิทัล

### 3. SEO
- **SEO Title**: ชื่อหน้าเว็บ
- **SEO Description**: คำอธิบายหน้าเว็บ
- **SEO Keywords**: คำค้นหา

## การเชื่อมต่อ API

### Backend Endpoints
- `GET /product/dashboard/:siteId/category/list` - ดึงรายการหมวดหมู่
- `POST /product/dashboard/:siteId/create` - สร้างสินค้าใหม่

### Service
- `productService.createProduct()` - สร้างสินค้าใหม่
- `categoryService.getCategories()` - ดึงรายการหมวดหมู่

## การใช้งาน

1. **กรอกข้อมูลพื้นฐาน**: ชื่อ, ราคา, สต็อก (จำเป็น)
2. **เลือกหมวดหมู่**: เลือกหมวดหมู่สินค้า
3. **เพิ่มแท็ก**: เพิ่มคำค้นหาสำหรับสินค้า
4. **ตั้งค่า SEO**: กรอกข้อมูล SEO สำหรับการค้นหา
5. **บันทึก**: กดปุ่มสร้างสินค้า

## การตรวจสอบข้อมูล

### ข้อมูลที่จำเป็น
- ชื่อสินค้า (ไม่ว่าง)
- ราคา (มากกว่า 0)

### ข้อมูลเพิ่มเติม
- SKU, บาร์โค้ด (ไม่ซ้ำ)
- หมวดหมู่ (ถ้ามี)
- แท็ก, SEO keywords

## การจัดการข้อผิดพลาด

- แสดงข้อความเมื่อข้อมูลไม่ครบ
- แสดงข้อความเมื่อเกิดข้อผิดพลาดในการสร้าง
- Redirect กลับไปหน้ารายการพร้อมข้อความสำเร็จ

## การปรับแต่ง

### เพิ่มฟิลด์
สามารถเพิ่มฟิลด์เพิ่มเติมได้:
- รูปภาพสินค้า
- ตัวเลือกสินค้า (variants)
- ข้อมูลการจัดส่ง
- ข้อมูลภาษี

### เพิ่มการตรวจสอบ
สามารถเพิ่มการตรวจสอบเพิ่มเติม:
- ตรวจสอบ SKU ซ้ำ
- ตรวจสอบราคาต้องมากกว่าต้นทุน
- ตรวจสอบขนาดสินค้า 