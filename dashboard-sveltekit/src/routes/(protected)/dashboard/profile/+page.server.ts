import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { userService } from '$lib/services/user';
import type { UpdateProfileData, ChangePasswordData } from '$lib/schemas/user.schema';
import { $fetch } from 'ofetch';

export const load: PageServerLoad = async ({ locals }) => {
  // Auth check already done in layout
  try {
    const result = await userService.getCurrentUser(locals.token!);

    if (!result.success) {
      console.error('Failed to load user profile:', result.error);
      return {
        user: locals.user, // Fallback to locals
        error: result.error
      };
    }

    return {
      user: result.data
    };

  } catch (error) {
    console.error('Profile load error:', error);
    return {
      user: locals.user, // Fallback to locals
      error: 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Update user profile
   */
  updateProfile: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const profileData: UpdateProfileData = {
        firstName: data.get('firstName')?.toString() || undefined,
        lastName: data.get('lastName')?.toString() || undefined,
      };

      // Call user service (validation included)
      const result = await userService.updateProfile(profileData, locals.token!);

      if (!result.success) {
        return fail(400, {
          error: result.error,
          type: 'profile'
        });
      }

      return {
        success: true,
        user: result.data,
        message: 'อัปเดตโปรไฟล์สำเร็จ',
        type: 'profile'
      };

    } catch (error) {
      console.error('Update profile error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์',
        type: 'profile'
      });
    }
  },

  /**
   * ✅ Change password
   */
  changePassword: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const passwordData: ChangePasswordData = {
        currentPassword: data.get('currentPassword')?.toString() || '',
        newPassword: data.get('newPassword')?.toString() || '',
        confirmPassword: data.get('confirmPassword')?.toString() || undefined,
      };

      // Call user service (validation included)
      const result = await userService.changePassword(passwordData, locals.token!);

      if (!result.success) {
        return fail(400, {
          error: result.error,
          type: 'password'
        });
      }

      return {
        success: true,
        message: 'เปลี่ยนรหัสผ่านสำเร็จ',
        type: 'password'
      };

    } catch (error) {
      console.error('Change password error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน',
        type: 'password'
      });
    }
  },

  /**
   * ✅ Update avatar
   */
  updateAvatar: async ({ request, locals }) => {
    try {
      const data = await request.formData();
      const avatarFile = data.get('avatar') as File;

      if (!avatarFile || avatarFile.size === 0) {
        return fail(400, {
          error: 'กรุณาเลือกไฟล์รูปภาพ',
          type: 'avatar'
        });
      }

      // Call user service (validation included)
      const result = await userService.updateAvatar({ avatar: avatarFile }, locals.token!);

      if (!result.success) {
        return fail(400, {
          error: result.error,
          type: 'avatar'
        });
      }

      return {
        success: true,
        avatar: result.data?.avatar,
        message: 'อัปเดตรูปโปรไฟล์สำเร็จ',
        type: 'avatar'
      };

    } catch (error) {
      console.error('Update avatar error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการอัปเดตรูปโปรไฟล์',
        type: 'avatar'
      });
    }
  },

  /**
   * ✅ Refresh user profile data
   */
  refreshProfile: async ({ locals }) => {
    try {
      console.log('RefreshProfile: Starting refresh with token:', locals.token?.substring(0, 10) + '...');
      
      const result = await userService.getCurrentUser(locals.token!);
      console.log('RefreshProfile: Service result:', result);

      if (!result.success) {
        console.log('RefreshProfile: Service failed:', result.error);
        return fail(400, {
          error: result.error,
          type: 'refresh'
        });
      }

      console.log('RefreshProfile: Success, user data:', result.data);
      return {
        success: true,
        user: (result.data as any).user,  // ← ใช้ type assertion
        message: 'รีโหลดข้อมูลโปรไฟล์สำเร็จ',
        type: 'refresh'
      };

    } catch (error) {
      console.error('Refresh profile error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการรีโหลดข้อมูลโปรไฟล์',
        type: 'refresh'
      });
    }
  },

  /**
   * ✅ Test refresh token
   */
  testToken: async ({ cookies }) => {
    const refreshToken = cookies.get('refreshToken');

    if (!refreshToken) {
      return fail(400, {
        error: 'ไม่พบ refresh token',
        type: 'token'
      });
    }

    try {
      // เรียก API refresh token
      const data = await $fetch(`${process.env.API_URL || 'http://localhost:5000/v1'}/user/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
        responseType: 'json'
      });

      if (data.success && data.data) {
        // อัปเดต cookies
        const cookieOptions = {
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax' as const,
        };

        cookies.set('auth_token', data.data.token, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24 * 7 // 7 วัน
        });

        if (data.data.refreshToken) {
          cookies.set('refreshToken', data.data.refreshToken, {
            ...cookieOptions,
            maxAge: 60 * 60 * 24 * 30 // 30 วัน
          });
        }

        return {
          success: true,
          user: data.data.user,
          message: 'Refresh token สำเร็จ',
          type: 'token'
        };
      } else {
        return fail(400, {
          error: 'Refresh token ล้มเหลว',
          type: 'token'
        });
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      return fail(400, {
        error: error instanceof Error ? error.message : 'Refresh token ไม่ถูกต้อง',
        type: 'token'
      });
    }
  }
};