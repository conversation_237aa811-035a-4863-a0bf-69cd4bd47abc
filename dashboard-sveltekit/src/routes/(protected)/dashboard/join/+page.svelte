<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { apiClient } from '$lib/api/client';
	import { authStore } from '$lib/stores/auth.svelte';
	import { toast } from 'svelte-sonner';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import { CheckCircle, XCircle, Clock, Users, Building, Mail, Calendar } from 'lucide-svelte';

	interface Invitation {
		_id: string;
		siteId: string;
		siteName: string;
		fromUserId: string;
		fromUserName: string;
		fromUserEmail: string;
		role: 'owner' | 'admin' | 'editor' | 'viewer';
		message?: string;
		status: 'pending' | 'accepted' | 'rejected' | 'expired';
		createdAt: string;
		expiresAt: string;
	}

	let invitations: Invitation[] = $state([]);
	let loading = $state(true);
	let processingId = $state<string | null>(null);

	// ดึงคำเชิญที่ได้รับ
	async function fetchInvitations() {
		try {
			loading = true;
			const response = await apiClient.get('/invitations/received');
			
			if (response.success) {
				invitations = response.data.invitations || [];
			} else {
				toast.error(response.message || 'ไม่สามารถดึงข้อมูลคำเชิญได้');
			}
		} catch (error: any) {
			console.error('Error fetching invitations:', error);
			toast.error('เกิดข้อผิดพลาดในการดึงข้อมูลคำเชิญ');
		} finally {
			loading = false;
		}
	}

	// รับคำเชิญ
	async function acceptInvitation(invitationId: string) {
		try {
			processingId = invitationId;
			const response = await apiClient.post(`/invitations/${invitationId}/accept`);
			
			if (response.success) {
				toast.success('เข้าร่วมทีมงานเรียบร้อยแล้ว');
				// อัปเดตสถานะในรายการ
				invitations = invitations.map(inv => 
					inv._id === invitationId 
						? { ...inv, status: 'accepted' as const }
						: inv
				);
				// รีเฟรช auth store เพื่ออัปเดตข้อมูลผู้ใช้
				await authStore.refreshUser();
			} else {
				toast.error(response.message || 'ไม่สามารถเข้าร่วมทีมงานได้');
			}
		} catch (error: any) {
			console.error('Error accepting invitation:', error);
			toast.error('เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน');
		} finally {
			processingId = null;
		}
	}

	// ปฏิเสธคำเชิญ
	async function rejectInvitation(invitationId: string) {
		try {
			processingId = invitationId;
			const response = await apiClient.post(`/invitations/${invitationId}/reject`);
			
			if (response.success) {
				toast.success('ปฏิเสธคำเชิญเรียบร้อยแล้ว');
				// อัปเดตสถานะในรายการ
				invitations = invitations.map(inv => 
					inv._id === invitationId 
						? { ...inv, status: 'rejected' as const }
						: inv
				);
			} else {
				toast.error(response.message || 'ไม่สามารถปฏิเสธคำเชิญได้');
			}
		} catch (error: any) {
			console.error('Error rejecting invitation:', error);
			toast.error('เกิดข้อผิดพลาดในการปฏิเสธคำเชิญ');
		} finally {
			processingId = null;
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม role
	function getRoleBadgeVariant(role: string) {
		switch (role) {
			case 'owner': return 'destructive';
			case 'admin': return 'default';
			case 'editor': return 'secondary';
			case 'viewer': return 'outline';
			default: return 'outline';
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม status
	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case 'pending': return 'default';
			case 'accepted': return 'default';
			case 'rejected': return 'destructive';
			case 'expired': return 'secondary';
			default: return 'outline';
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม status
	function getStatusIcon(status: string) {
		switch (status) {
			case 'pending': return Clock;
			case 'accepted': return CheckCircle;
			case 'rejected': return XCircle;
			case 'expired': return XCircle;
			default: return Clock;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// ตรวจสอบว่าคำเชิญหมดอายุหรือไม่
	function isExpired(expiresAt: string) {
		return new Date(expiresAt) < new Date();
	}

	onMount(() => {
		fetchInvitations();
	});
</script>

<svelte:head>
	<title>เข้าร่วมทีมงาน - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">เข้าร่วมทีมงาน</h1>
			<p class="text-muted-foreground">จัดการคำเชิญเข้าร่วมทีมงานที่คุณได้รับ</p>
		</div>
		<Button variant="outline" onclick={() => goto('/dashboard')}>
			กลับไปหน้าหลัก
		</Button>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else if invitations.length === 0}
		<Card>
			<CardContent class="flex flex-col items-center justify-center py-12">
				<Users class="h-12 w-12 text-muted-foreground mb-4" />
				<h3 class="text-lg font-semibold mb-2">ไม่มีคำเชิญ</h3>
				<p class="text-muted-foreground text-center">
					คุณยังไม่มีคำเชิญเข้าร่วมทีมงานใดๆ<br />
					เมื่อมีคำเชิญใหม่ คุณจะเห็นรายการที่นี่
				</p>
			</CardContent>
		</Card>
	{:else}
		<div class="grid gap-4">
			{#each invitations as invitation (invitation._id)}
				{@const expired = isExpired(invitation.expiresAt)}
				{@const StatusIcon = getStatusIcon(invitation.status)}
				
				<Card class="transition-all hover:shadow-md">
					<CardHeader>
						<div class="flex items-start justify-between">
							<div class="space-y-1">
								<CardTitle class="flex items-center gap-2">
									<Building class="h-5 w-5" />
									{invitation.siteName}
								</CardTitle>
								<CardDescription class="flex items-center gap-2">
									<Mail class="h-4 w-4" />
									เชิญโดย {invitation.fromUserName} ({invitation.fromUserEmail})
								</CardDescription>
							</div>
							<div class="flex items-center gap-2">
								<Badge variant={getRoleBadgeVariant(invitation.role)}>
									{invitation.role}
								</Badge>
								<Badge variant={getStatusBadgeVariant(invitation.status)}>
									<StatusIcon class="h-3 w-3 mr-1" />
									{invitation.status === 'pending' ? 'รอดำเนินการ' :
									 invitation.status === 'accepted' ? 'ยอมรับแล้ว' :
									 invitation.status === 'rejected' ? 'ปฏิเสธแล้ว' : 'หมดอายุ'}
								</Badge>
							</div>
						</div>
					</CardHeader>
					
					<CardContent class="space-y-4">
						{#if invitation.message}
							<div class="bg-muted p-3 rounded-lg">
								<p class="text-sm">{invitation.message}</p>
							</div>
						{/if}
						
						<div class="flex items-center gap-4 text-sm text-muted-foreground">
							<div class="flex items-center gap-1">
								<Calendar class="h-4 w-4" />
								ส่งเมื่อ: {formatDate(invitation.createdAt)}
							</div>
							<div class="flex items-center gap-1">
								<Clock class="h-4 w-4" />
								หมดอายุ: {formatDate(invitation.expiresAt)}
							</div>
						</div>
						
						{#if expired}
							<div class="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
								<p class="text-sm text-destructive font-medium">คำเชิญนี้หมดอายุแล้ว</p>
							</div>
						{/if}
						
						{#if invitation.status === 'pending' && !expired}
							<Separator />
							<div class="flex gap-2">
								<Button 
									onclick={() => acceptInvitation(invitation._id)}
									disabled={processingId === invitation._id}
									class="flex-1"
								>
									{#if processingId === invitation._id}
										<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
									{:else}
										<CheckCircle class="h-4 w-4 mr-2" />
									{/if}
									ยอมรับ
								</Button>
								<Button 
									variant="outline"
									onclick={() => rejectInvitation(invitation._id)}
									disabled={processingId === invitation._id}
									class="flex-1"
								>
									{#if processingId === invitation._id}
										<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
									{:else}
										<XCircle class="h-4 w-4 mr-2" />
									{/if}
									ปฏิเสธ
								</Button>
							</div>
						{/if}
					</CardContent>
				</Card>
			{/each}
		</div>
	{/if}
</div>
