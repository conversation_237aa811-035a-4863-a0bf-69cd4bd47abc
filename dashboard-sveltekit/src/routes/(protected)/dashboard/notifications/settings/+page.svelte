<script lang="ts">
    import { onMount } from "svelte";
    import Icon from "@iconify/svelte";
    import { notificationStore } from "$lib/stores/notification.svelte";
    import NotificationPreferences from "$lib/components/notifications/NotificationPreferences.svelte";
    import NotificationChannels from "$lib/components/notifications/NotificationChannels.svelte";
    import NotificationQuietHours from "$lib/components/notifications/NotificationQuietHours.svelte";

    // Reactive values
    let settings = $derived(notificationStore.settings);
    let isLoading = $derived(notificationStore.isLoading);
    let error = $derived(notificationStore.error);

    let isSaving = $state(false);
    let saveSuccess = $state(false);

    onMount(() => {
        // โหลดการตั้งค่าเมื่อเริ่มต้น
        notificationStore.loadSettings();
    });

    // จัดการการบันทึกการตั้งค่า
    async function handleSaveSettings(updatedSettings: any) {
        isSaving = true;
        saveSuccess = false;

        try {
            await notificationStore.updateSettings(updatedSettings);
            saveSuccess = true;

            // ซ่อนข้อความสำเร็จหลัง 3 วินาที
            setTimeout(() => {
                saveSuccess = false;
            }, 3000);
        } catch (error) {
            console.error("Error saving settings:", error);
        } finally {
            isSaving = false;
        }
    }

    // รีเซ็ตการตั้งค่าเป็นค่าเริ่มต้น
    async function handleResetSettings() {
        const defaultSettings = {
            preferences: {
                orderUpdates: true,
                productAlerts: true,
                promotions: true,
                systemMessages: true,
                chatMessages: true,
                affiliateUpdates: true,
                topupAlerts: true,
                membershipUpdates: true,
                expiryWarnings: true,
                inventoryAlerts: false,
                paymentNotifications: true,
                securityAlerts: true,
                marketingMessages: false,
            },
            channels: {
                inApp: true,
                email: false,
                push: false,
                sms: false,
            },
            quietHours: {
                enabled: false,
                startTime: "22:00",
                endTime: "08:00",
                timezone: "Asia/Bangkok",
            },
        };

        await handleSaveSettings(defaultSettings);
    }
</script>

<svelte:head>
    <title>ตั้งค่าการแจ้งเตือน - Dashboard</title>
</svelte:head>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold flex items-center gap-3">
                <Icon icon="solar:settings-bold" class="size-8" />
                ตั้งค่าการแจ้งเตือน
            </h1>
            <p class="text-base-content/70 mt-1">
                จัดการการตั้งค่าการแจ้งเตือนและช่องทางการรับข้อมูล
            </p>
        </div>

        <div class="flex items-center gap-2">
            <!-- Back to Notifications -->
            <a href="/dashboard/notifications" class="btn btn-ghost">
                <Icon icon="solar:arrow-left-bold" class="size-5" />
                กลับ
            </a>

            <!-- Reset to Default -->
            <button
                onclick={handleResetSettings}
                class="btn btn-outline"
                disabled={isLoading || isSaving}
            >
                <Icon icon="solar:restart-bold" class="size-5" />
                รีเซ็ต
            </button>
        </div>
    </div>

    <!-- Success Message -->
    {#if saveSuccess}
        <div class="alert alert-success">
            <Icon icon="solar:check-circle-bold" class="size-6" />
            <span>บันทึกการตั้งค่าเรียบร้อยแล้ว</span>
        </div>
    {/if}

    <!-- Error Message -->
    {#if error}
        <div class="alert alert-error">
            <Icon icon="solar:danger-circle-bold" class="size-6" />
            <div>
                <h3 class="font-bold">เกิดข้อผิดพลาด</h3>
                <div class="text-xs">{error}</div>
            </div>
            <button
                onclick={() => notificationStore.loadSettings()}
                class="btn btn-sm btn-error"
            >
                ลองใหม่
            </button>
        </div>
    {/if}

    <!-- Loading State -->
    {#if isLoading && !settings}
        <div class="flex justify-center py-12">
            <div class="text-center">
                <span class="loading loading-spinner loading-lg mb-4"></span>
                <p class="text-base-content/70">กำลังโหลดการตั้งค่า...</p>
            </div>
        </div>
    {:else if settings}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Notification Preferences -->
            <div class="space-y-6">
                <NotificationPreferences
                    preferences={settings.preferences}
                    onSave={(preferences) =>
                        handleSaveSettings({ preferences })}
                    {isSaving}
                />
            </div>

            <!-- Channels & Quiet Hours -->
            <div class="space-y-6">
                <NotificationChannels
                    channels={settings.channels}
                    onSave={(channels) => handleSaveSettings({ channels })}
                    {isSaving}
                />

                <NotificationQuietHours
                    quietHours={settings.quietHours}
                    onSave={(quietHours) => handleSaveSettings({ quietHours })}
                    {isSaving}
                />
            </div>
        </div>

        <!-- Save All Button -->
        <div class="flex justify-center pt-6">
            <button
                onclick={() => handleSaveSettings(settings)}
                class="btn btn-primary btn-lg"
                class:loading={isSaving}
                disabled={isSaving}
            >
                {#if !isSaving}
                    <Icon icon="solar:diskette-bold" class="size-6" />
                {/if}
                บันทึกการตั้งค่าทั้งหมด
            </button>
        </div>
    {:else}
        <!-- Empty State -->
        <div class="text-center py-12">
            <Icon
                icon="solar:settings-minimalistic-bold"
                class="size-16 mx-auto mb-4 text-base-content/30"
            />
            <h3 class="text-xl font-semibold mb-2">ไม่พบการตั้งค่า</h3>
            <p class="text-base-content/70 mb-4">
                ไม่สามารถโหลดการตั้งค่าการแจ้งเตือนได้
            </p>
            <button
                onclick={() => notificationStore.loadSettings()}
                class="btn btn-primary"
            >
                <Icon icon="solar:refresh-bold" class="size-5" />
                ลองใหม่
            </button>
        </div>
    {/if}
</div>
