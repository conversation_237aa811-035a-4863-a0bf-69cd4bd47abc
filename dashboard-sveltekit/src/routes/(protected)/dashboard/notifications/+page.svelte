<script lang="ts">
    import { onMount } from "svelte";
    import Icon from "@iconify/svelte";
    import { notificationStore } from "$lib/stores/notification.svelte";
    import NotificationList from "$lib/components/notifications/NotificationList.svelte";
    import NotificationFilters from "$lib/components/notifications/NotificationFilters.svelte";
    import NotificationStats from "$lib/components/notifications/NotificationStats.svelte";
    import LoadingTransition from "$lib/components/transitions/LoadingTransition.svelte";
    import { page } from "$app/state";

    // Reactive values
    let notifications = $derived(notificationStore.notifications);
    let unreadCount = $derived(notificationStore.unreadCount);
    let isLoading = $derived(notificationStore.isLoading);
    let error = $derived(notificationStore.error);
    let pagination = $derived(notificationStore.pagination);
    let stats = $derived(notificationStore.stats);

    // Filter states
    let selectedType = $state("");
    let selectedStatus = $state("");
    let currentPage = $state(1);

    onMount(() => {
        // โหลดข้อมูลเริ่มต้น
        loadNotifications();
        notificationStore.loadStats();
    });

    async function loadNotifications() {
        const params: any = {
            page: currentPage,
            limit: 20,
        };

        if (selectedType) params.type = selectedType;
        if (selectedStatus) params.status = selectedStatus;

        await notificationStore.loadNotifications(params);
    }

    // จัดการการเปลี่ยนแปลงตัวกรอง
    function handleFilterChange() {
        currentPage = 1;
        loadNotifications();
    }

    // จัดการการเปลี่ยนหน้า
    function handlePageChange(newPage: number) {
        currentPage = newPage;
        loadNotifications();
    }

    // จัดการการทำเครื่องหมายอ่านทั้งหมด
    async function handleMarkAllAsRead() {
        await notificationStore.markAllAsRead();
        await loadNotifications(); // รีเฟรชข้อมูล
    }

    // จัดการการรีเฟรช
    function handleRefresh() {
        loadNotifications();
        notificationStore.loadStats();
    }
</script>

<svelte:head>
    <title>การแจ้งเตือน - Dashboard</title>
</svelte:head>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold flex items-center gap-3">
                <Icon icon="solar:bell-bold" class="size-8" />
                การแจ้งเตือน
                {#if unreadCount > 0}
                    <span class="badge badge-error">{unreadCount}</span>
                {/if}
            </h1>
            <p class="text-base-content/70 mt-1">
                จัดการและดูการแจ้งเตือนทั้งหมดของคุณ
            </p>
        </div>

        <div class="flex items-center gap-2">
            <!-- Refresh Button -->
            <button
                onclick={handleRefresh}
                class="btn btn-ghost"
                class:loading={isLoading}
                disabled={isLoading}
            >
                {#if !isLoading}
                    <Icon icon="solar:refresh-bold" class="size-5" />
                {/if}
                รีเฟรช
            </button>

            <!-- Mark All as Read -->
            {#if unreadCount > 0}
                <button
                    onclick={handleMarkAllAsRead}
                    class="btn btn-primary"
                    disabled={isLoading}
                >
                    <Icon icon="solar:check-read-bold" class="size-5" />
                    อ่านทั้งหมด ({unreadCount})
                </button>
            {/if}
        </div>
    </div>

    <!-- Stats -->
    {#if stats.length > 0}
        <NotificationStats {stats} />
    {/if}

    <!-- Filters -->
    <NotificationFilters
        bind:selectedType
        bind:selectedStatus
        onChange={handleFilterChange}
    />

    <!-- Error State -->
    {#if error}
        <div class="alert alert-error">
            <Icon icon="solar:danger-circle-bold" class="size-6" />
            <div>
                <h3 class="font-bold">เกิดข้อผิดพลาด</h3>
                <div class="text-xs">{error}</div>
            </div>
            <button onclick={handleRefresh} class="btn btn-sm btn-error">
                ลองใหม่
            </button>
        </div>
    {/if}

    <!-- Notifications List -->
    <LoadingTransition
        isLoading={isLoading && notifications.length === 0}
        loadingText="กำลังโหลดการแจ้งเตือน..."
        showSkeleton={true}
    >
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body p-0">
                {#if notifications.length === 0 && !isLoading}
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <Icon
                            icon="solar:bell-off-bold"
                            class="size-16 mx-auto mb-4 text-base-content/30"
                        />
                        <h3 class="text-xl font-semibold mb-2">
                            ไม่มีการแจ้งเตือน
                        </h3>
                        <p class="text-base-content/70 mb-4">
                            {selectedType || selectedStatus
                                ? "ไม่พบการแจ้งเตือนที่ตรงกับเงื่อนไขที่เลือก"
                                : "คุณยังไม่มีการแจ้งเตือนใดๆ"}
                        </p>
                        {#if selectedType || selectedStatus}
                            <button
                                onclick={() => {
                                    selectedType = "";
                                    selectedStatus = "";
                                    handleFilterChange();
                                }}
                                class="btn btn-primary btn-sm"
                            >
                                <Icon
                                    icon="solar:refresh-bold"
                                    class="size-4"
                                />
                                ล้างตัวกรอง
                            </button>
                        {/if}
                    </div>
                {:else}
                    <NotificationList
                        {notifications}
                        onRefresh={handleRefresh}
                    />

                    <!-- Pagination -->
                    {#if pagination.pages > 1}
                        <div class="divider"></div>
                        <div class="flex justify-center p-4">
                            <div class="join">
                                <!-- Previous Button -->
                                <button
                                    class="join-item btn"
                                    class:btn-disabled={currentPage <= 1}
                                    onclick={() =>
                                        handlePageChange(currentPage - 1)}
                                    disabled={currentPage <= 1 || isLoading}
                                >
                                    <Icon
                                        icon="solar:arrow-left-bold"
                                        class="size-4"
                                    />
                                    ก่อนหน้า
                                </button>

                                <!-- Page Numbers -->
                                {#each Array.from( { length: Math.min(5, pagination.pages) }, (_, i) => {
                                        const startPage = Math.max(1, currentPage - 2);
                                        return startPage + i;
                                    }, ) as pageNum}
                                    {#if pageNum <= pagination.pages}
                                        <button
                                            class="join-item btn"
                                            class:btn-active={pageNum ===
                                                currentPage}
                                            onclick={() =>
                                                handlePageChange(pageNum)}
                                            disabled={isLoading}
                                        >
                                            {pageNum}
                                        </button>
                                    {/if}
                                {/each}

                                <!-- Next Button -->
                                <button
                                    class="join-item btn"
                                    class:btn-disabled={currentPage >=
                                        pagination.pages}
                                    onclick={() =>
                                        handlePageChange(currentPage + 1)}
                                    disabled={currentPage >= pagination.pages ||
                                        isLoading}
                                >
                                    ถัดไป
                                    <Icon
                                        icon="solar:arrow-right-bold"
                                        class="size-4"
                                    />
                                </button>
                            </div>
                        </div>

                        <!-- Pagination Info -->
                        <div
                            class="text-center text-sm text-base-content/70 pb-4"
                        >
                            แสดง {(currentPage - 1) * pagination.limit + 1} - {Math.min(
                                currentPage * pagination.limit,
                                pagination.total,
                            )}
                            จาก {pagination.total} รายการ
                        </div>
                    {/if}
                {/if}
            </div>
        </div>
    </LoadingTransition>
</div>
