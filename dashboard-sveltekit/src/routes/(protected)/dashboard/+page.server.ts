import type { PageServerLoad } from './$types';
import { siteService } from '$lib/services/site';

// แยก pagination params ออกมา
function getPaginationParams(url: URL) {
  return {
    page: url.searchParams.get('page') || '1',
    limit: url.searchParams.get('limit') || '10',
    search: url.searchParams.get('search') || '',
    status: url.searchParams.get('status') || ''
  };
}

// แยก default response ออกมา
function getDefaultResponse(page: number, limit: number) {
  return {
    sites: [],
    pagination: {
      page,
      limit,
      total: 0,
      totalPages: 0
    }
  };
}

// แยก logic การโหลด sites ออกมา
async function loadUserSites(token: string, params: ReturnType<typeof getPaginationParams>) {
  console.log('Fetching sites with token:', token?.substring(0, 20) + '...');
  console.log('Pagination params:', params);

  const result = await siteService.getUserSitesWithPagination(params, token);
  console.log('Sites service result:', result);

  return result;
}

export const load: PageServerLoad = async ({ locals, url, parent }) => {
  try {
    // รับข้อมูลจาก parent layout
    const parentData = await parent();
    const token = parentData.token;

    if (!token) {
      console.error('No token found');
      const params = getPaginationParams(url);
      return {
        ...getDefaultResponse(parseInt(params.page), parseInt(params.limit)),
        error: 'ไม่พบ token สำหรับการเข้าถึง'
      };
    }

    // ดึง pagination parameters
    const params = getPaginationParams(url);

    // โหลดข้อมูล sites
    const result = await loadUserSites(token, params);
    console.log("result", result);

    // จัดการ error
    if (!result.success) {
      console.error('Sites service error:', result.error);
      return {
        ...getDefaultResponse(parseInt(params.page), parseInt(params.limit)),
        error: result.error
      };
    }

    // ส่งข้อมูลสำเร็จ
    const sites = result.data?.sites || [];
    const pagination = result.data?.pagination || {
      page: parseInt(params.page),
      limit: parseInt(params.limit),
      total: sites.length,
      totalPages: Math.ceil(sites.length / parseInt(params.limit))
    };

    return {
      sites,
      pagination,
      error: null
    };

  } catch (error) {
    console.error('Error loading sites:', error);
    const params = getPaginationParams(url);
    return {
      ...getDefaultResponse(parseInt(params.page), parseInt(params.limit)),
      error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
    };
  }
}; 