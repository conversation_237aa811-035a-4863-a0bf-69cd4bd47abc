import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { siteService } from '$lib/services/site';
import { discountService } from '$lib/services/discount';
import { subscriptionService } from '$lib/services/subscription';
import { requireAuth } from '$lib/utils/server-auth';

export const ssr = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
  try {
    // ✅ ดึงข้อมูลแพ็คเกจจาก subscription service
    const packagesResult = await subscriptionService.getPackages(locals.token!);

    return {
      packages: packagesResult.success && packagesResult.data ? 
        (Array.isArray(packagesResult.data) ? packagesResult.data : (packagesResult.data as any)?.packages || []) : []
    };
  } catch (error) {
    console.error('Error loading packages:', error);
    return {
      packages: []
    };
  }
};

export const actions: Actions = {
  checkDomain: async ({ request, locals }: any) => {
    // ✅ Centralized auth check
    requireAuth(locals);

    try {
      const formData = await request.formData();
      const result = await siteService.checkDomain({
        typeDomain: formData.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: formData.get('subDomain') as string,
        mainDomain: formData.get('mainDomain') as string,
        customDomain: formData.get('customDomain') as string
      }, locals.token);

      // Service already handles all logic, just transform for SvelteKit
      return result.success
        ? result
        : fail(400, { error: result.error, success: false });

    } catch (error) {
      console.error('Domain check error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน',
        success: false
      });
    }
  },

  // สร้างเว็บไซต์ - Simplified version
  createSite: async ({ request, locals }: any) => {
    // ✅ Centralized auth check
    requireAuth(locals);

    try {
      const formData = await request.formData();
      const result = await siteService.createSiteWithValidation({
        siteName: formData.get('siteName') as string,
        typeDomain: formData.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: formData.get('subDomain') as string,
        mainDomain: formData.get('mainDomain') as string,
        customDomain: formData.get('customDomain') as string,
        packageType: formData.get('packageType') as string
      }, (locals.user as any).moneyPoint || 0, locals.token);

      // Service already handles all logic, just transform for SvelteKit
      return result.success
        ? result
        : fail(400, { error: result.error, success: false });

    } catch (error) {
      console.error('Create site error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
        success: false
      });
    }
  },

  // ตรวจสอบโค้ดส่วนลด - เชื่อมต่อกับ backend API
  checkDiscount: async ({ request, locals }: any) => {
    // ✅ Centralized auth check
    requireAuth(locals);

    try {
      const formData = await request.formData();
      const discountCode = formData.get('discountCode') as string;
      const orderAmount = parseFloat(formData.get('orderAmount') as string) || 0;

      // Basic client-side validation
      if (!discountCode || discountCode.trim().length === 0) {
        return fail(400, {
          error: 'กรุณากรอกรหัสส่วนลด',
          success: false
        });
      }

      // เรียกใช้ discount service เพื่อตรวจสอบกับ backend
      const result = await discountService.validateDiscount(
        discountCode,
        {
          target: 'package',
          orderAmount: orderAmount,
          items: [] // สามารถส่ง items ได้ถ้ามี
        },
        locals.token
      );

      if (result.success) {
        return {
          type: 'success',
          status: 200,
          data: {
            code: result.data?.code,
            discount: result.data?.discount,
            type: result.data?.type,
            name: result.data?.name,
            description: result.data?.description,
            minOrderAmount: result.data?.minOrderAmount,
            maxDiscountAmount: result.data?.maxDiscountAmount,
            isFirstTimeOnly: result.data?.isFirstTimeOnly,
            validUntil: result.data?.validUntil
          },
          message: result.message || `ได้รับส่วนลด ${result.data?.discount}${result.data?.type === 'percentage' ? '%' : '฿'}`
        };
      } else {
        return fail(400, {
          error: result.error || 'โค้ดส่วนลดไม่ถูกต้อง',
          success: false
        });
      }

    } catch (error) {
      console.error('Check discount error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการตรวจสอบโค้ดส่วนลด',
        success: false
      });
    }
  }
};
