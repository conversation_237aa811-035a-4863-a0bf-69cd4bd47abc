<script lang="ts">
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import LanguageSelector from "$lib/components/layout/LanguageSelector.svelte";
  import ThemeToggle from "$lib/components/layout/ThemeToggle.svelte";
  import { authStore } from "$lib/stores/auth.svelte";
  import { themeStore } from "$lib/stores/theme.svelte";
  import { languageStore } from "$lib/stores/language.svelte";
  import { t } from "svelte-i18n";
  import Icon from "@iconify/svelte";
  import Image from "$lib/components/ui/Image.svelte";
  import UserMenu from "$lib/components/layout/UserMenu.svelte";
  import NotificationBell from "$lib/components/layout/NotificationBell.svelte";
  import PageTransition from "$lib/components/transitions/PageTransition.svelte";
  import NavigationTransition from "$lib/components/transitions/NavigationTransition.svelte";
  import { onMount, onDestroy } from "svelte";

  let { data, children } = $props<{
    data: { user?: any; token?: string };
  }>();

  let user = $derived(data?.user || authStore.user);
  let isAuthenticated = $derived(authStore.isAuthenticated || !!data?.user);
  let isInitialized = $derived(authStore.isInitialized);

  onMount(async () => {
    // ตั้งค่า user จาก SSR
    if (data?.user) {
      authStore.setUserFromSSR(data.user);
    }

    // ตรวจสอบ authentication หลังจาก initialized
    if (isInitialized && !isAuthenticated) {
      console.log("Not authenticated, attempting token refresh...");

      // ลอง refresh token ก่อน
      const refreshSuccess = await authStore.refreshToken();

      if (!refreshSuccess) {
        console.log("Token refresh failed, redirecting to signin...");
        goto("/signin");
      }
    }
  });

  onDestroy(() => {
    // Cleanup stores
    authStore.destroy();
    themeStore.destroy();
    languageStore.destroy();
  });

  async function signout() {
    await authStore.signout();
  }
</script>

{#if isInitialized && isAuthenticated}
  <div class="space-y-4">
    <!-- Header -->
    <header>
      <div class="bg-base-200 p-2">
        <div
          class="container mx-auto px-4 sm:px-6 lg:px-8 flex flex-row justify-between"
        >
          <header class="navbar bg-base-100 border-b border-base-300">
            <div class="navbar-start flex flex-row gap-1">
              <a
                href="/dashboard"
                class="btn {page.url.pathname === '/dashboard'
                  ? 'btn-primary'
                  : 'btn-ghost'}"
              >
                <Icon icon="solar:home-smile-angle-bold" class="size-6" /> จัดการเว็บไซต์
              </a>

              <a
                href="/dashboard/create"
                class="btn {page.url.pathname === '/dashboard/create'
                  ? 'btn-primary'
                  : 'btn-ghost'}"
              >
                <Icon icon="solar:home-add-angle-bold" class="size-6" /> สร้างเว็บไซต์
              </a>

              <a
                href="/dashboard/join"
                class="btn {page.url.pathname === '/dashboard/join'
                  ? 'btn-primary'
                  : 'btn-ghost'}"
              >
                <Icon icon="solar:users-group-rounded-bold" class="size-6" /> เข้าร่วมเว็บไซต์
              </a>
            </div>

            <!-- Actions -->
            <div class="navbar-end">
              <UserMenu {data} />
            </div>
          </header>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 sm:px-6 lg:px-8">
      <NavigationTransition>
        <PageTransition>
          {@render children()}
        </PageTransition>
      </NavigationTransition>
    </main>
  </div>
{:else}
  <!-- Loading state -->
  <div class="flex items-center justify-center min-h-screen">
    <div class="loading loading-spinner loading-lg"></div>
  </div>
{/if}
