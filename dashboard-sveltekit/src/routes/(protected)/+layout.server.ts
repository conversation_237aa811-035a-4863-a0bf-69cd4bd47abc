import type { LayoutServerLoad } from './$types';
import { requireAuth } from '$lib/utils/server-auth';

export const load: LayoutServerLoad = async ({ locals }) => {
  // ตรวจสอบ authentication
  requireAuth(locals);
  console.log('Protected Layout Server: locals.user:', locals.user);
  console.log('Protected Layout Server: locals.token:', locals.token ? 'exists' : 'not found');
  console.log('Protected Layout Server: locals.token:', locals.token);

  return {
    user: locals.user,
    token: locals.token
  };
}; 