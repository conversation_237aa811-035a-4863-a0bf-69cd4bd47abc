<script lang="ts">
	import LanguageSelector from "$lib/components/layout/LanguageSelector.svelte";
	import ThemeToggle from "$lib/components/layout/ThemeToggle.svelte";

	let { children } = $props();
</script>

<div class="space-y-3">
	<!-- Public pages with header -->
	<header>
		<div class="flex flex-row gap-3 justify-between items-center p-3">
			<ThemeToggle />
			<LanguageSelector />
		</div>
		<div class="flex justify-center items-center">
			<a href="/" class="flex justify-center items-center">
				<img
					height="220"
					width="220"
					src="/logo.avif"
					alt="logo is1site"
					class="max-w-40 sm:max-w-full mx-auto box-shadow bg-white/60 dark:bg-white/20 backdrop-blur-sm p-5 rounded-lg box-shadow-xl"
				/>
			</a>
		</div>
	</header>

	<!-- Main Content -->
	<main class="p-3 md:p-5">
		{@render children()}
	</main>

	<footer>
		<div class="flex justify-center items-center">
			© {Date()} 1SITE. All rights reserved.
		</div>
	</footer>
</div>
