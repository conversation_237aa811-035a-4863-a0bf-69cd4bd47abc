<script lang="ts">
    import { goto } from "$app/navigation";
    import { onMount } from "svelte";
    import { t } from "svelte-i18n";
    import Icon from "@iconify/svelte";
    import { authStore, type User } from "$lib/stores/auth.svelte";
    import Button from "$lib/components/ui/Button.svelte";
    import Input from "$lib/components/ui/Input.svelte";
    import SEO from "$lib/components/layout/SEO.svelte";
    import Card from "$lib/components/ui/Card.svelte";
    import { validateLoginForm, type LoginForm } from "$lib/validators/auth";
    import {
        showLoading,
        showToast,
        showLoginError,
        showValidationError,
    } from "$lib/utils/sweetalert";
    import { enhance } from "$app/forms";
    import { logger, LogCategory } from "$lib/utils/logger";

    // Import validation functions
    const validateEmail = (value: string) => {
        if (!value) return "กรุณากรอกอีเมล";
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value))
            return "รูปแบบอีเมลไม่ถูกต้อง";
        return undefined;
    };

    const validatePassword = (value: string) => {
        if (!value) return "กรุณากรอกรหัสผ่าน";
        if (value.length < 6) return "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร";
        if (value.length > 128) return "รหัสผ่านต้องไม่เกิน 128 ตัวอักษร";
        return undefined;
    };

    let { form } = $props<{
        form?: any;
    }>();

    let formData = $state<LoginForm>({
        email: "",
        password: "",
        rememberMe: false,
    });
    let errors = $state<Record<string, string>>({});
    let isSubmitting = $state(false);

    // Form action result
    let formResult = $derived(form);

    // Clear errors when user types
    $effect(() => {
        if (formData.email && errors.email) {
            errors = { ...errors };
            delete errors.email;
        }
        if (formData.password && errors.password) {
            errors = { ...errors };
            delete errors.password;
        }
    });

    // Reactive validation
    $effect(() => {
        if (formData.email) {
            const emailError = validateEmail(formData.email);
            if (emailError && !errors.email) {
                errors = { ...errors, email: emailError };
            } else if (!emailError && errors.email) {
                errors = { ...errors };
                delete errors.email;
            }
        }
    });

    $effect(() => {
        if (formData.password) {
            const passwordError = validatePassword(formData.password);
            if (passwordError && !errors.password) {
                errors = { ...errors, password: passwordError };
            } else if (!passwordError && errors.password) {
                errors = { ...errors };
                delete errors.password;
            }
        }
    });

    onMount(() => {
        // ถ้า login แล้วให้ redirect ไป dashboard
        if (authStore.isAuthenticated) {
            goto("/dashboard");
        }

        // Clear sensitive data from localStorage if exists
        if (typeof window !== "undefined") {
            const oldToken = localStorage.getItem("auth_token");
            if (oldToken) {
                localStorage.removeItem("auth_token");
                logger.info(
                    LogCategory.AUTH,
                    "old_token_cleared",
                    "Cleared old auth token on signin page",
                );
            }
        }
    });

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === "Enter" && !isSubmitting) {
            const form = (event.target as HTMLElement)?.closest(
                "form",
            ) as HTMLFormElement;
            if (form) {
                form.requestSubmit();
            }
        }
    }
</script>

<SEO title={$t("auth.login")} />

<Card centered variant="default" shadow="lg" size="sm" title={$t("auth.login")}>
    <!-- Form Action Messages -->
    <!-- {#if formResult?.success}
        <div class="alert alert-success">
            <Icon icon="mdi:check-circle" class="w-5 h-5" />
            <span>{formResult.message}</span>
        </div>
    {:else if formResult?.error}
        <div class="alert alert-error">
            <Icon icon="mdi:alert-circle" class="w-5 h-5" />
            <span>{formResult.error}</span>
        </div>
    {/if} -->

    <!-- Login Form -->
    <form
        method="POST"
        action="?/signin"
        class="space-y-6"
        use:enhance={() => {
            return async ({ result }: any) => {
                isSubmitting = true;

                if (result.type === "success" && result.data?.success) {
                    // อัปเดต authStore ด้วยข้อมูลที่ได้จาก server
                    if (result.data?.user) {
                        authStore.updateUser(result.data.user as User);
                        // Token จะถูกจัดการโดย cookie แล้ว ไม่ต้องใช้ localStorage
                    }

                    logger.info(
                        LogCategory.AUTH,
                        "client_signin_success",
                        "Client-side signin success",
                    );

                    showToast("success", "เข้าสู่ระบบสำเร็จ!", {
                        timer: 1500,
                        timerProgressBar: true,
                        showCloseButton: false,
                        allowEscapeKey: true,
                    });

                    // Redirect ไป dashboard หลังจาก login สำเร็จ
                    setTimeout(() => {
                        goto("/dashboard");
                    }, 1500);
                } else if (
                    result.type === "failure" ||
                    result.data?.success === false
                ) {
                    const errorMessage =
                        result.data?.error || "เข้าสู่ระบบล้มเหลว";

                    logger.warn(
                        LogCategory.AUTH,
                        "client_signin_failed",
                        "Client-side signin failed",
                        {
                            error: errorMessage,
                        },
                    );

                    showLoginError(errorMessage as string, {
                        timer: 1500,
                        timerProgressBar: true,
                        showCloseButton: false,
                        allowEscapeKey: true,
                    });
                }
                isSubmitting = false;
                // Update form with result
                // await update();
            };
        }}
    >
        <Input
            id="email"
            name="email"
            type="email"
            bind:value={formData.email}
            label={$t("auth.email")}
            placeholder="<EMAIL>"
            icon="mdi:email"
            autocomplete="email"
            disabled={isSubmitting}
            onkeydown={handleKeydown}
            validate={errors.email}
            showRequired={true}
        />

        <Input
            id="password"
            name="password"
            type="password"
            bind:value={formData.password}
            label={$t("auth.password")}
            placeholder="••••••••"
            icon="mdi:lock"
            showPasswordToggle
            autocomplete="current-password"
            disabled={isSubmitting}
            onkeydown={handleKeydown}
            validate={errors.password}
            showRequired={true}
        />

        <div class="flex items-center justify-between">
            <label for="rememberMe" class="label cursor-pointer">
                <input
                    id="rememberMe"
                    name="rememberMe"
                    type="checkbox"
                    bind:checked={formData.rememberMe}
                    class="checkbox checkbox-primary checkbox-sm"
                    disabled={isSubmitting}
                />
                <span class="label-text ml-2">{$t("auth.rememberMe")}</span>
            </label>

            <a
                href="/forgot-password"
                class="link link-primary text-sm"
                class:pointer-events-none={isSubmitting}
            >
                {$t("auth.forgotPassword")}
            </a>
        </div>

        {#if authStore.error}
            <div class="alert alert-error">
                <Icon icon="mdi:alert-circle" class="w-5 h-5" />
                <span>{authStore.error}</span>
            </div>
        {/if}

        <Button
            type="submit"
            color="primary"
            size="lg"
            block
            loading={isSubmitting || authStore.isLoading}
            disabled={isSubmitting || authStore.isLoading}
        >
            {isSubmitting ? "กำลังเข้าสู่ระบบ..." : $t("auth.login")}
        </Button>
    </form>

    <!-- Register Link -->
    <div class="text-center mt-6">
        <span class="text-base-content/60">ยังไม่มีบัญชี? </span>
        <a
            href="/signup"
            class="link link-primary"
            class:pointer-events-none={isSubmitting}
        >
            {$t("auth.register")}
        </a>
    </div>
</Card>
