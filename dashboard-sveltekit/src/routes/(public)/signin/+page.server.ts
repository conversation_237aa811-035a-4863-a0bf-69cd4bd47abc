import type { Actions, PageServerLoad } from './$types';
import { authService } from '$lib/services/auth';
import { error, redirect } from '@sveltejs/kit';
import { validateSigninData, sanitizeAuthData } from '$lib/schemas/auth.schema';
import { ResponseHelper, type FormActionResponse } from '$lib/types/response';
import { logger, LogCategory } from '$lib/utils/logger';
import { getClientIP, getUserAgent } from '$lib/utils/security';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null
  };
};

export const actions: Actions = {
  signin: async ({ request, cookies, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const sessionId = cookies.get('session_id') || crypto.randomUUID();

    try {
      const formData = await request.formData();
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;
      const rememberMe = formData.get('rememberMe') === 'true';

      // Sanitize input
      const sanitizedData = sanitizeAuthData({
        email: email?.trim() || '',
        password: password?.trim() || ''
      });

      // Validate input
      const validationError = validateSigninData(sanitizedData);
      if (validationError) {
        logger.warn(LogCategory.AUTH, 'signin_validation_failed', 'Signin validation failed', {
          email: sanitizedData.email?.substring(0, 3) + '***',
          clientIP,
          userAgent
        });

        return ResponseHelper.formError(validationError);
      }

      // Log signin attempt
      logger.info(LogCategory.AUTH, 'signin_attempt', 'User attempting signin', {
        email: sanitizedData.email?.substring(0, 3) + '***',
        clientIP,
        userAgent,
        sessionId
      });

      // เรียก authService
      const result = await authService.signin(sanitizedData);

      if (result.success && result.data) {
        // จัดการ cookie สำหรับ remember me
        const cookieOptions = {
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax' as const,
        };

        if (rememberMe) {
          cookies.set('remember_me', 'true', {
            ...cookieOptions,
            maxAge: 60 * 60 * 24 * 30 // 30 วัน
          });
        } else {
          cookies.delete('remember_me', { path: '/' });
        }

        // Set auth token cookie
        cookies.set('auth_token', result.data.token, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24 * 7 // 7 วัน
        });

        // Set refresh token cookie
        if (result.data.refreshToken) {
          cookies.set('refreshToken', result.data.refreshToken, {
            ...cookieOptions,
            maxAge: 60 * 60 * 24 * 30 // 30 วัน
          });
        }

        // Set session cookie
        cookies.set('session_id', sessionId, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24 // 1 วัน
        });

        // Log successful signin
        logger.info(LogCategory.AUTH, 'signin_success', 'User signed in successfully', {
          userId: result.data.user._id,
          email: result.data.user.email?.substring(0, 3) + '***',
          clientIP,
          userAgent,
          sessionId
        });

        // Return success data for client-side handling (ไม่ส่ง token กลับ)
        return ResponseHelper.formSuccess(
          { user: result.data.user },
          'เข้าสู่ระบบสำเร็จ'
        );
      } else {
        // Log failed signin
        logger.warn(LogCategory.AUTH, 'signin_failed', 'Signin failed', {
          email: sanitizedData.email?.substring(0, 3) + '***',
          error: result.error,
          clientIP,
          userAgent,
          sessionId
        });
        // Throw error แทน return error object
        // throw new Error(error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ');
        // Return error message สำหรับ client-side
        return ResponseHelper.formError(
          result.error || 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
        );
      }
    } catch (error) {
      // Log error
      logger.error(LogCategory.AUTH, 'signin_exception', 'Exception during signin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        clientIP,
        userAgent,
        sessionId
      });
   // Throw error แทน return error object
  //  throw new Error(error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ');
      return ResponseHelper.formError(
        'เกิดข้อผิดพลาดในการเข้าสู่ระบบ กรุณาลองใหม่อีกครั้ง'
      );
    }
  }
};