<script lang="ts">
  import SEO from '$lib/components/layout/SEO.svelte';
  import { Card } from '$lib/components/ui';

  const helpSections = [
    {
      title: 'การเริ่มต้นใช้งาน',
      icon: '🚀',
      items: [
        { question: 'วิธีสมัครสมาชิก', answer: 'คลิกที่ปุ่ม "สมัครสมาชิก" แล้วกรอกข้อมูลที่จำเป็น ยืนยันอีเมล และเข้าสู่ระบบได้เลย' },
        { question: 'วิธีสร้างเว็บไซต์แรก', answer: 'หลังจากเข้าสู่ระบบแล้ว คลิก "สร้างเว็บไซต์ใหม่" เลือกแพ็คเกจ กรอกข้อมูลเว็บไซต์ และชำระเงิน' },
        { question: 'วิธีเติมเงินเข้าระบบ', answer: 'ไปที่หน้า Dashboard คลิก "เติมเงิน" เลือกจำนวนเงิน และชำระผ่านช่องทางที่ต้องการ' }
      ]
    },
    {
      title: 'การจัดการเว็บไซต์',
      icon: '⚙️',
      items: [
        { question: 'วิธีเพิ่มสินค้า', answer: 'เข้าไปในเว็บไซต์ของคุณ ไปที่ "จัดการสินค้า" คลิก "เพิ่มสินค้าใหม่" กรอกข้อมูลและอัปโหลดรูปภาพ' },
        { question: 'วิธีเปลี่ยนธีม', answer: 'ในหน้าจัดการเว็บไซต์ ไปที่ "การตั้งค่า" > "ธีม" เลือกธีมที่ต้องการและบันทึก' },
        { question: 'วิธีตั้งค่าโดเมน', answer: 'ไปที่ "การตั้งค่า" > "โดเมน" กรอกโดเมนที่ต้องการใช้ และทำตาม DNS Setting ที่แสดง' }
      ]
    },
    {
      title: 'การชำระเงินและแพ็คเกจ',
      icon: '💳',
      items: [
        { question: 'แพ็คเกจมีอะไรบ้าง', answer: 'มีแพ็คเกจเริ่มต้น 29 บาท/วัน, แพ็คเกจมาตรฐาน 49 บาท/วัน, และแพ็คเกจพรีเมียม 99 บาท/วัน' },
        { question: 'วิธีการชำระเงิน', answer: 'รองรับการชำระผ่าน QR Code, Gift Wallet, บัตรเครดิต และการเติมผ่านแอดมิน' },
        { question: 'การต่ออายุเว็บไซต์', answer: 'ระบบจะหักเงินอัตโนมัติทุกวัน หากเงินไม่พอจะแจ้งเตือนให้เติมเงิน' }
      ]
    },
    {
      title: 'การสนับสนุนและติดต่อ',
      icon: '🆘',
      items: [
        { question: 'ช่องทางติดต่อ', answer: 'Discord: discord.gg/webshop, Facebook: fb.com/webshopplatform, หรือส่งอีเมลมาที่ <EMAIL>' },
        { question: 'เวลาให้บริการ', answer: 'ให้บริการตอบคำถาม 24/7 ผ่าน Discord และ Facebook, อีเมลตอบภายใน 24 ชั่วโมง' },
        { question: 'การแก้ไขปัญหาเร่งด่วน', answer: 'หากมีปัญหาเร่งด่วน ติดต่อผ่าน Discord จะได้รับการตอบกลับเร็วที่สุด' }
      ]
    }
  ];
</script>

<SEO 
  title="วิธีใช้งาน - คู่มือการใช้งาน WebShop Platform"
  description="คู่มือการใช้งานระบบเช่าเว็บไซต์ครบวงจร วิธีสมัครสมาชิก สร้างเว็บไซต์ จัดการสินค้า และการใช้งานต่างๆ"
  keywords="วิธีใช้งาน, คู่มือ, การสร้างเว็บไซต์, จัดการสินค้า, เติมเงิน, ติดต่อสนับสนุน"
  url="/help"
  type="article"
/>

<div class="max-w-6xl mx-auto px-4 py-8">
  <div class="text-center mb-12">
    <h1 class="text-4xl font-bold text-primary mb-4">วิธีใช้งาน WebShop Platform</h1>
    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
      คู่มือการใช้งานระบบเช่าเว็บไซต์ครบวงจร ตั้งแต่การสมัครสมาชิกจนถึงการจัดการเว็บไซต์
    </p>
  </div>

  <div class="grid gap-8 md:grid-cols-2">
    {#each helpSections as section}
      <Card title={`${section.icon} ${section.title}`} class="h-fit">
        <div class="space-y-4">
          {#each section.items as item}
            <div class="border-l-4 border-primary pl-4">
              <h3 class="font-semibold text-base-content mb-2">{item.question}</h3>
              <p class="text-sm text-base-content/80">{item.answer}</p>
            </div>
          {/each}
        </div>
      </Card>
    {/each}
  </div>

  <!-- Contact Section -->
  <Card title="🤝 ต้องการความช่วยเหลือเพิ่มเติม?" class="mt-12 text-center">
    <p class="mb-6">หากคุณมีคำถามที่ไม่พบคำตอบในคู่มือนี้ สามารถติดต่อทีมสนับสนุนได้ทันที</p>
    
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <a href="https://discord.gg/webshop" target="_blank" 
         class="btn btn-primary btn-outline">
        <span class="text-lg">💬</span>
        Discord Community
      </a>
      <a href="https://facebook.com/webshopplatform" target="_blank" 
         class="btn btn-secondary btn-outline">
        <span class="text-lg">📘</span>
        Facebook Page
      </a>
      <a href="mailto:<EMAIL>" 
         class="btn btn-accent btn-outline">
        <span class="text-lg">📧</span>
        ส่งอีเมล
      </a>
    </div>
  </Card>
</div>