import type { Actions, PageServerLoad } from './$types';
import { authService } from '$lib/services/auth';
import { redirect } from '@sveltejs/kit';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null
  };
};

export const actions: Actions = {
  forgotPassword: async ({ request }) => {
    try {
      const formData = await request.formData();
      const email = formData.get('email') as string;

      // Validation
      if (!email?.trim()) {
        return {
          success: false,
          error: 'กรุณากรอกอีเมล'
        };
      }

      // เรียก authService โดยตรง
      const result = await authService.forgotPassword(email.trim());

      if (result.success) {
        return {
          success: true,
          message: 'ส่งอีเมลรีเซ็ตรหัสผ่านแล้ว กรุณาตรวจสอบกล่องจดหมายของคุณ'
        };
      } else {
        return {
          success: false,
          error: result.error || 'ไม่สามารถส่งอีเมลรีเซ็ตรหัสผ่านได้'
        };
      }
    } catch (error) {
      console.error('Forgot password action error:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการส่งอีเมลรีเซ็ตรหัสผ่าน'
      };
    }
  }
}; 