<script lang="ts">
    import { goto } from "$app/navigation";
    import { onMount } from "svelte";
    import { t } from "svelte-i18n";
    import Icon from "@iconify/svelte";
    import { authStore } from "$lib/stores/auth.svelte";
    import Button from "$lib/components/ui/Button.svelte";
    import Input from "$lib/components/ui/Input.svelte";
    import SEO from "$lib/components/layout/SEO.svelte";
    import Card from "$lib/components/ui/Card.svelte";
    import {
        validateRegisterForm,
        type RegisterForm,
    } from "$lib/validators/auth";
    import {
        showRegisterSuccess,
        showRegisterError,
        showValidationError,
    } from "$lib/utils/sweetalert";
    import { enhance } from "$app/forms";

    let { form } = $props<{
        form?: any;
    }>();

    let formData = $state<RegisterForm>({
        email: "",
        password: "",
        confirmPassword: "",
        agreeToTerms: false,
    });
    let errors = $state<Record<string, string>>({});

    // Form action result
    let formResult = $derived(form);

    onMount(() => {
        // ถ้า login แล้วให้ redirect ไป dashboard
        if (authStore.isAuthenticated) {
            goto("/dashboard");
        }
    });

    // Redirect after successful signup
    $effect(() => {
        if (formResult?.success) {
            showRegisterSuccess();
            setTimeout(() => {
                goto("/dashboard");
            }, 1500);
        }
    });

    function validateFormData(): boolean {
        const result = validateRegisterForm(formData);

        if (result.success) {
            errors = {};
            return true;
        } else {
            errors = result.errors;
            // แสดง validation errors
            Object.values(result.errors).forEach((error) => {
                showValidationError(error);
            });
            return false;
        }
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === "Enter") {
            const form = event.target?.closest('form') as HTMLFormElement;
            if (form) {
                form.requestSubmit();
            }
        }
    }

    function handleFieldChange(field: keyof RegisterForm, value: unknown) {
        formData[field] = value as never;

        // Clear error when user starts typing
        if (errors[field]) {
            errors = { ...errors };
            delete errors[field];
        }
    }
</script>

<SEO title={$t("auth.register")} />

<Card
    centered
    variant="default"
    shadow="lg"
    size="sm"
    title={$t("auth.register")}
>
    <!-- Form Action Messages -->
    {#if formResult?.success}
        <div class="alert alert-success">
            <Icon icon="mdi:check-circle" class="w-5 h-5" />
            <span>{formResult.message}</span>
        </div>
    {:else if formResult?.error}
        <div class="alert alert-error">
            <Icon icon="mdi:alert-circle" class="w-5 h-5" />
            <span>{formResult.error}</span>
        </div>
    {/if}

    <!-- Register Form -->
    <form method="POST" action="?/signup" class="space-y-6" use:enhance={() => {
        return async ({ result }) => {
            if (result.type === 'failure') {
                showRegisterError(result.data?.error || 'ลงทะเบียนล้มเหลว');
            }
        };
    }}>
        <Input
            id="email"
            name="email"
            type="email"
            style="bordered"
            value={formData.email}
            label={$t("auth.email")}
            placeholder="<EMAIL>"
            icon="mdi:email"
            error={errors.email}
            required
            autocomplete="email"
            onkeydown={handleKeydown}
            onchange={(e) =>
                handleFieldChange(
                    "email",
                    (e.target as HTMLInputElement).value,
                )}
        />

        <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            label={$t("auth.password")}
            placeholder="••••••••"
            icon="mdi:lock"
            error={errors.password}
            required
            showPasswordToggle
            autocomplete="new-password"
            onkeydown={handleKeydown}
            onchange={(e) =>
                handleFieldChange(
                    "password",
                    (e.target as HTMLInputElement).value,
                )}
        />

        <Input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            value={formData.confirmPassword}
            label={$t("auth.confirmPassword")}
            placeholder="••••••••"
            icon="mdi:lock-check"
            error={errors.confirmPassword}
            required
            showPasswordToggle
            autocomplete="new-password"
            onkeydown={handleKeydown}
            onchange={(e) =>
                handleFieldChange(
                    "confirmPassword",
                    (e.target as HTMLInputElement).value,
                )}
        />

        <div class="flex items-start gap-2">
            <input
                id="agreeToTerms"
                name="agreeToTerms"
                type="checkbox"
                checked={formData.agreeToTerms}
                class="checkbox checkbox-primary checkbox-sm mt-1"
                onchange={(e) =>
                    handleFieldChange(
                        "agreeToTerms",
                        (e.target as HTMLInputElement).checked,
                    )}
            />
            <label for="agreeToTerms" class="label cursor-pointer">
                <span class="label-text text-sm">
                    ฉันยอมรับ <a href="/terms" class="link link-primary"
                        >เงื่อนไขการใช้งาน</a
                    >
                    และ
                    <a href="/privacy" class="link link-primary"
                        >นโยบายความเป็นส่วนตัว</a
                    >
                </span>
            </label>
        </div>

        {#if authStore.error}
            <div class="alert alert-error">
                <Icon icon="mdi:alert-circle" class="w-5 h-5" />
                <span>{authStore.error}</span>
            </div>
        {/if}

        <Button
            type="submit"
            color="primary"
            size="lg"
            block
            loading={authStore.isLoading}
            disabled={authStore.isLoading}
        >
            {$t("auth.register")}
        </Button>
    </form>

    <!-- Login Link -->
    <div class="text-center mt-6">
        <span class="text-base-content/60">มีบัญชีอยู่แล้ว? </span>
        <a href="/signin" class="link link-primary">
            {$t("auth.login")}
        </a>
    </div>
</Card>
