import type { Actions, PageServerLoad } from './$types';
import { authService } from '$lib/services/auth';
import { redirect } from '@sveltejs/kit';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null
  };
};

export const actions: Actions = {
  signup: async ({ request, cookies }) => {
    try {
      const formData = await request.formData();
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;
      const confirmPassword = formData.get('confirmPassword') as string;
      const firstName = formData.get('firstName') as string;
      const lastName = formData.get('lastName') as string;

      // Validation
      if (!email?.trim()) {
        return {
          success: false,
          error: 'กรุณากรอกอีเมล'
        };
      }

      if (!password?.trim()) {
        return {
          success: false,
          error: 'กรุณากรอกรหัสผ่าน'
        };
      }

      if (password !== confirmPassword) {
        return {
          success: false,
          error: 'รหัสผ่านไม่ตรงกัน'
        };
      }

      if (!firstName?.trim()) {
        return {
          success: false,
          error: 'กรุณากรอกชื่อจริง'
        };
      }

      if (!lastName?.trim()) {
        return {
          success: false,
          error: 'กรุณากรอกนามสกุล'
        };
      }

      // เรียก authService โดยตรง
      const result = await authService.signup({
        email: email.trim(),
        password: password.trim(),
        firstName: firstName.trim(),
        lastName: lastName.trim()
      });

      if (result.success && result.data) {
        return {
          success: true,
          user: result.data.user,
          token: result.data.token
        };
      } else {
        return {
          success: false,
          error: result.error || 'ลงทะเบียนล้มเหลว'
        };
      }
    } catch (error) {
      console.error('Signup action error:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการลงทะเบียน'
      };
    }
  }
}; 