<script lang="ts">
  const { status, error } = $props();
</script>

<div
  class="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4"
>
  <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
    <div class="mb-6">
      <div
        class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <svg
          class="w-8 h-8 text-red-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          ></path>
        </svg>
      </div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">เกิดข้อผิดพลาด</h1>
      <p class="text-gray-600">
        {#if error?.message}
          {error.message}
        {:else}
          เกิดข้อผิดพลาดที่ไม่คาดคิด
        {/if}
      </p>
      {#if status}
        <p class="text-sm text-gray-500 mt-2">รหัสข้อผิดพลาด: {status}</p>
      {/if}
    </div>

    <div class="space-y-3">
      <button
        onclick={() => window.location.reload()}
        class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
      >
        ลองใหม่อีกครั้ง
      </button>

      <a
        href="/"
        class="block w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
      >
        กลับหน้าหลัก
      </a>
    </div>

    <div class="mt-6 pt-6 border-t border-gray-200">
      <p class="text-sm text-gray-500">
        หากปัญหายังคงอยู่ กรุณาติดต่อผู้ดูแลระบบ
      </p>
    </div>
  </div>
</div>
