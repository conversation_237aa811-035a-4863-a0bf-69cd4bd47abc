{"name": "dashboard-sveltekit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:unit": "vitest", "test": "npm run test:unit -- --run", "clean": "rm -rf node_modules bun.lock .svelte-kit"}, "devDependencies": {"@iconify/svelte": "^5.0.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.26.1", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@vitest/browser": "^3.2.4", "daisyui": "^5.0.50", "playwright": "^1.54.1", "svelte": "^5.37.1", "svelte-check": "^4.3.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-devtools-json": "^0.4.1", "vitest": "^3.2.4", "vitest-browser-svelte": "^1.0.0"}, "dependencies": {"chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "ofetch": "^1.4.1", "svelte-i18n": "^4.0.1", "sweetalert2": "^11.22.2", "sweetalert2-neutral": "^11.22.2-neutral", "valibot": "1.1.0"}}